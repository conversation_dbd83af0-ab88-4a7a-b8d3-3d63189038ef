package com.chervon.common.core.error;

/**
 * 默认错误实现类
 * @date 2023/5/11
 */
public class DefaultError implements IError {
    private String code;
    private String message;

    DefaultError(String code, String reason) {
        this.code = code;
        this.message = reason;
    }

    /**
     * 错误编号
     */
    @Override
    public String getCode() {
        return this.code;
    }

    /**
     * 原因
     */
    @Override
    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message){
        this.message = message;
    }

    /**
     * 生成错误实例
     *
     * @param code   编号
     * @param message 原因
     * @return
     */
    public static IError of(String code, String message) {
        return new DefaultError(code, message);
    }
}