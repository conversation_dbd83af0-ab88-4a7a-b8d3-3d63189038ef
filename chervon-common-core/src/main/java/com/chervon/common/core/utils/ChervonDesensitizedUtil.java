package com.chervon.common.core.utils;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date
 */
public class ChervonDesensitizedUtil {

	/**
	 * @return
	 */
	public static String Sensitive(String uerId, int front, int end) {
		//身份证不能为空
		if (StrUtil.isBlank(uerId)) {
			return StrUtil.EMPTY;
		}
		//需要截取的长度不能大于身份证号长度
		if ((front + end) > uerId.length()) {
			return uerId;
		}
		//需要截取的不能小于0
		if (front < 0 || end < 0) {
			return StrUtil.EMPTY;
		}
		return StrUtil.hide(uerId, front, uerId.length() - end);
	}


}
