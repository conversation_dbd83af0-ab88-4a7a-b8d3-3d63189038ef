package com.chervon.common.core.exception;

/**
 * 不是枚举类型
 */
public class NotEnumTypeException extends RuntimeException {
    private static final long serialVersionUID = 479806686866828994L;
    /**
     * 类型
     */
    private final Class<?> clazz;

    public NotEnumTypeException(Class<?> clazz) {
        super("class " + clazz.getTypeName() + " is not an enumerated type.");
        this.clazz = clazz;
    }

    /**
     * 获取异常类型
     *
     * @return 异常类型
     */
    public Class<?> getClazz() {
        return clazz;
    }
}
