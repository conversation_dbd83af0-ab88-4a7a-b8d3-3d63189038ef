package com.chervon.common.core.prop;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * <AUTHOR>
 * @description AWS配置文件读取
 * @date 2022/3/30
 */

@Data
@ConfigurationProperties(prefix = "env.aws")
@RefreshScope
public class AwsProperties {

    private String accessId;
    private String secretKey;
    private String region;
    private String ruleToken;
    private String policyName;
    private String pinpointAppId;
    private BucketProperties pictureBucket;
    private BucketProperties otaBucket;
    private BucketProperties defaultBucket;
    private BucketProperties receiptBucket;
    private String certSecretKey;

    @Data
    public static class BucketProperties {
        private String name;
        private String cdnHost;
        private String s3Host;
    }
}

