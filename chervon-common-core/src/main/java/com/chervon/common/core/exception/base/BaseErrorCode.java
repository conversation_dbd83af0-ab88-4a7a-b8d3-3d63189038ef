package com.chervon.common.core.exception.base;

import com.chervon.common.core.exception.ErrorCodeI;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通用错误码
 *
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum BaseErrorCode implements ErrorCodeI {

    /**
     * 1000002001
     */
    /**
     * 操作成功
     */
    SUCCESS("1000002001", "Success", "Success"),

    /**
     * 系统异常
     */
    SYSTEM_ERROR("1000002002", "System error", "System error"),

    /**
     * 接口超时
     */
    API_TIMEOUT("1000002003", "Api timeout", "Api timeout"),

    /**
     * 参数错误
     */
    PARAM_ERROR("1000002004", "Parameter error", "Parameter error"),

    /**
     * 数据库操作失败
     */
    DB_ERROR("1000002005", "Database error", "Database error"),

    /**
     * 缓存操作失败
     */
    CACHE_ERROR("1000002006", "Cache error", "Cache error"),

    /**
     * 没有权限
     */
    NO_PERMISSION("403", "no permission", "no permission"),


    REQUEST_ERROR("1000002009", "request error", "request error"),

    /**
     * 参数不全
     */
    PARAM_INCOMPLETE("1000002010", "Parameter error", "Incomplete parameters"),

    ;

    private final String code;

    private final String defaultMessage;

    private final String errorMessage;

}
