package com.chervon.common.core.utils;

import cn.hutool.core.date.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/2/17 21:11
 */
public class DateTimeZoneUtil {

    private DateTimeZoneUtil() {
    }

    public static String format(LocalDateTime dateTime, int zone) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.plusHours(zone).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    public static String format(Date dateTime, int zone) {
        if (dateTime == null) {
            return "";
        }
        return DateUtil.format(DateUtils.addHours(dateTime, zone), "yyyy-MM-dd HH:mm:ss");
    }

    public static String format(String dateTime, int zone) {
        if (StringUtils.isBlank(dateTime)) {
            return "";
        }
        try {
            LocalDateTime parse = LocalDateTime.parse(dateTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return parse.plusHours(zone).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 根据时区+时间获取UTC时间
     *
     * @param time 时间字符串 yyyy-MM-dd HH:mm:ss
     * @param zone 时区 -12 到 +12
     * @return UTC+0时区的LocalDateTime
     */
    public static LocalDateTime getRealTime(String time, String zone) {
        LocalDateTime oldDateTime = LocalDateTime.parse(time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        ZoneId oldZone = ZoneId.of("UTC" + zone);
        ZoneId newZone = ZoneId.of("UTC");
        return oldDateTime.atZone(oldZone)
                .withZoneSameInstant(newZone)
                .toLocalDateTime();
    }
}
