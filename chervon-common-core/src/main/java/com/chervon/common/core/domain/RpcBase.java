package com.chervon.common.core.domain;

import lombok.Data;
import org.springframework.context.i18n.LocaleContextHolder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/14 21:56
 */
@Data
public class RpcBase implements Serializable {

    /**
     * 语言
     */
    private String lang;

    /**
     * 登录信息
     */
    private LoginSysUser login;

    public RpcBase() {
        this.lang = LocaleContextHolder.getLocale().getLanguage();
    }

    public RpcBase(String lang) {
        this.lang = lang;
    }

    public RpcBase(LoginSysUser login) {
        this.lang = LocaleContextHolder.getLocale().getLanguage();
        this.login = login;
    }

    public RpcBase(String lang, LoginSysUser login) {
        this.lang = lang;
        this.login = login;
    }
}
