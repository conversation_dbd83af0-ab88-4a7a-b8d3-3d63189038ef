package com.chervon.common.core.domain;

/**
 * <AUTHOR>
 * @date 2023/4/17 22:33
 */
public class LoginUserContext {

    private static final ThreadLocal<LoginSysUser> userHolder = new ThreadLocal<>();

    public static void setUser(LoginSysUser loginUser) {
        userHolder.set(loginUser);
    }

    public static LoginSysUser getUser() {
        return userHolder.get();
    }

    public static void clear() {
        userHolder.remove();
    }

}
