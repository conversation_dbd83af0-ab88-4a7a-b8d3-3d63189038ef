package com.chervon.common.core.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022-07-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MultiLanguageVo implements Serializable {

    /**
     * 多语言Id
     */
    private Long langId;

    /**
     * 具体的多语言文案
     */
    @NotEmpty
    private String message;
}
