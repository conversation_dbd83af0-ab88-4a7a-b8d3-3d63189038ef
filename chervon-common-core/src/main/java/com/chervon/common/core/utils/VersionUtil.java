package com.chervon.common.core.utils;

/**
 * 版本的格式转换
 */
public class VersionUtil {

	public static void main(String[] args) {
		String s = VersionUtil.convertVersion("44.9.7");
		System.out.println(s);

	}

	public static String convertVersion(String version) {
		if (StringUtils.isEmpty(version)) {
			return version;
		}
		String[] split = version.split("\\.");
		StringBuilder result = new StringBuilder();
		for (int i = 0; i < split.length; i++) {
			if (i != 0) {
				result.append(".");
			}
			String s = split[i];
			if (s.length() >= 3) {
				result.append(s);
				continue;
			}
			int i1 = 3 - s.length();
			String b = "";
			for (int j = 0; j < i1; j++) {
				b = b + "0";
			}
			s = b + s;
			result.append(s);
		}
		return result.toString();

	}

}
