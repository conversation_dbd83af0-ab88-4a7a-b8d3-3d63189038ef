package com.chervon.common.core.exception;

import org.springframework.util.StringUtils;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeoutException;

/**
 * 异常工具
 */
public class ExceptionUtils {
    /**
     * 参数异常类型集合
     */
    protected static final Set<Class<?>> parameterExceptionSet = new HashSet<>();

    static {
        parameterExceptionSet.add(IllegalArgumentException.class);
    }

    /**
     * 超时异常
     *
     * @param ex
     * @return
     */
    public static boolean isTimeOutException(Throwable ex) {
        if (TimeoutException.class.isAssignableFrom(ex.getClass())) {
            return true;
        } else if (SocketTimeoutException.class.isAssignableFrom(ex.getClass())) {
            return true;
        }
        if (ex.getCause() != null) {
            return isTimeOutException(ex.getCause());
        }
        return false;
    }

    /**
     * 连接异常
     *
     * @param throwable
     * @return
     */
    public static boolean isConnectionException(Throwable throwable) {
        return isConnectException(throwable) || isSocketConnectTimeout(throwable);
    }

    /**
     * 连接异常
     *
     * @param throwable
     * @return
     */
    private static boolean isConnectException(Throwable throwable) {
        if (ConnectException.class.isAssignableFrom(throwable.getClass())) {
            return true;
        }
        if (throwable.getCause() != null) {
            return isConnectException(throwable.getCause());
        }
        return false;
    }

    /**
     * Socket 连接异常
     *
     * @param throwable
     * @return
     */
    private static boolean isSocketConnectTimeout(Throwable throwable) {
        if (SocketTimeoutException.class.isAssignableFrom(throwable.getClass())) {
            if (throwable.getMessage() != null) {
                return throwable.getMessage().trim().equalsIgnoreCase("connect timed out");
            }
        }
        if (throwable.getCause() != null) {
            return isSocketConnectTimeout(throwable.getCause());
        }
        return false;
    }

    /**
     * 获取异常消息
     *
     * @param throwable
     * @return
     */
    public static String getExceptionMsg(Throwable throwable) {
        if (throwable != null) {
            if (StringUtils.hasText(throwable.getMessage())) {
                return throwable.getMessage();
            } else {
                if (throwable.getCause() != null) {
                    return getExceptionMsg(throwable.getCause());
                }
            }
        }
        return null;
    }

    /**
     * 获取深度异常消息描述
     *
     * @param th
     * @return
     */
    public static String getDepthExceptionMsg(Throwable th) {
        if (th.getCause() != null) {
            Throwable throwable = th.getCause();
            String msg = getDepthExceptionMsg(throwable);
            if (StringUtils.hasText(msg)) {
                return msg;
            } else {
                return throwable.getMessage();
            }
        } else {
            return th.getMessage();
        }
    }

    /**
     * 获取用户自定义的系统异常
     *
     * @param throwable 异常对象实例
     * @return SystemException or null
     */
    public static ServiceException getUserDefinedException(Throwable throwable) {
        if (throwable != null) {
            if (ServiceException.class.isAssignableFrom(throwable.getClass())) {
                return (ServiceException) throwable;
            } else {
                return getUserDefinedException(throwable.getCause());
            }
        }
        return null;
    }

    /**
     * 是否参数异常
     */
    public static boolean isParameterException(Throwable ex) {
        for (Class<?> clazz : parameterExceptionSet) {
            if (ex.getClass() == clazz) {
                return true;
            }
        }
        return false;
    }

    /**
     * 注册参数异常类型
     *
     * @param clazz 异常类型
     */
    public static void registerParameterException(Class<?> clazz) {
        parameterExceptionSet.add(clazz);
    }
}
