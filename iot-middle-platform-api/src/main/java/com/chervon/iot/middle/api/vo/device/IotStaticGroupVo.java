package com.chervon.iot.middle.api.vo.device;

import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @className GroupVo
 * @description
 * @date 2022/2/14 15:55
 */
@Data
public class IotStaticGroupVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Length(max = 128)
    @NotNull
    private String groupName;

    @Length(max = 2048)
    private String remark;
}
