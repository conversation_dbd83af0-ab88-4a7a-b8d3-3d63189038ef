package com.chervon.iot.middle.api.vo.device;

import java.io.Serializable;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @className DeviceTaskVo
 * @description
 * @date 2022/2/14 15:55
 */
@Data
public class IotDeviceServiceVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Length(min = 1, max = 128)
    private String deviceId;

    @Length(min = 1, max = 128)
    private String key;

    /**
     * 输入参数
     **/
    private Object inputParam;
}
