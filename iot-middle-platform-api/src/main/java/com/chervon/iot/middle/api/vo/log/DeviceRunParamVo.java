package com.chervon.iot.middle.api.vo.log;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/11/29 14:37
 */
@Data
public class DeviceRunParamVo extends PageRequest implements Serializable {

	@ApiModelProperty("设备id")
	private String deviceId;

	@ApiModelProperty("开始时间")
	private Long startTime;

	@ApiModelProperty("结束时间")
	private Long endTime;
}
