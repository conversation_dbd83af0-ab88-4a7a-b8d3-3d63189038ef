package com.chervon.iot.middle.api.vo.ota;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @className OtaJobItemVo
 * @description 升级作业列表项vo
 * @date 2022/3/10 14:04
 */
@Data
public class IotOtaJobItemVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private String jobId;

    private String status;

    private Date queuedAt;

    private Date startedAt;

    private Date lastUpdatedAt;

    private Date completedAt;
}
