package com.chervon.iot.middle.api.pojo.usage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * 61001割草机设备日使用数据
 * <AUTHOR>
 * @since 2024-02-26 10:52
 **/
@AllArgsConstructor
@Data
@ApiModel(value = "61001割草机设备使用数据", description = "61001割草机设备日使用数据")
public class UsageKeyValue implements Serializable {
    /**
     * 数据key
     */
    @ApiModelProperty("数据key")
    private String dataKey;
    /**
     * 使用数据值
     */
    @ApiModelProperty("使用数据值")
    private Long dataValue;

}