package com.chervon.iot.middle.api.vo.device;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @className IotDeviceQueryListVo
 * @description 设备查询列表
 * @date/3/4 10:31
 */
@Data
public class IotDeviceQueryListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<String> deviceIds;

    private String nextToken;

}
