package com.chervon.iot.middle.api.vo.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @className DeviceTopologyVo
 * @description 设备拓扑关系
 * @date 2022/7/11
 */
@Data
@ApiModel(description = "设备拓扑关系")
public class DeviceTopologyVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @ApiModelProperty(value = "设备类型")
    private String deviceType;

    @ApiModelProperty(value = "数据上下行类型 0上行 1下行")
    private Integer type;

    @ApiModelProperty(value = "通讯方式")
    private String communicationType;

    @ApiModelProperty(value = "最后通讯时间")
    private Date lastCommunicationTime;
}
