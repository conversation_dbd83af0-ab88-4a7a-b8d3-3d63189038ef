package com.chervon.iot.middle.api.pojo.thingmodel;

import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @className ParamData
 * @description 事件或服务出参入参
 * @date 2022/5/9 14:30
 */
@Data
public class ParamData extends BaseThingModelItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 参数数据类型
     **/
    private DataType dataType;

    /**
     * 描述
     */
    private String Desc;
}
