package com.chervon.iot.middle.api.pojo.thingmodel;

import lombok.Data;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @className InsertModelVo
 * @description 添加产品物模型属性
 * @date 2022/2/18 18:15
 */
@Data
public class ThingModelRuleVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;
    /**
     * 产品标识
     **/
    private String productKey;

    /**
     * 封板状态
     **/
    private String status;
}
