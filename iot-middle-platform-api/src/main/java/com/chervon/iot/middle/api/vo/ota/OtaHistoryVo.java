package com.chervon.iot.middle.api.vo.ota;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

/**
 * <AUTHOR>
 * @className OtaHistoryVo
 * @description
 * @date 2022/7/13 18:17
 */
@ApiModel("升级历史vo")
public class OtaHistoryVo {
    /**
     * 原始固件版本
     **/
    @ApiModelProperty("原始固件版本")
    private String oldVersion;

    /**
     * 目标固件版本
     **/
    @ApiModelProperty("目标固件版本")
    private Integer newVersion;

    /**
     * 升级结果
     **/
    @ApiModelProperty("升级结果")
    private String result;

    /**
     * 操作用户id
     **/
    @ApiModelProperty("操作用户id")
    private Long userId;

    /**
     * 升级时间
     **/
    @ApiModelProperty("升级时间")
    private Date upgradeTime;
}
