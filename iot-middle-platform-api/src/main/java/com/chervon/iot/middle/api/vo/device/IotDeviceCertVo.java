package com.chervon.iot.middle.api.vo.device;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @className InsertDeviceVO
 * @description
 * @date 2022/2/14 15:55
 */
@Data
public class IotDeviceCertVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("证书aws资源名称")
    private String deviceArn;

    @ApiModelProperty("pem格式证书")
    private String certificatePem;

    @ApiModelProperty("证书私钥")
    private String privateKey;

    @ApiModelProperty("json格式的证书-aes加密")
    private String certJSONStr;


}
