package com.chervon.iot.middle.api.enums;

/**
 * 日志通信类型
 *
 * <AUTHOR>
 * @date 2022年9月24日
 **/
public enum ThingLogTransferType {
	/**
	 * 上行
	 **/
	UP(1),

	/**
	 * 下行
	 **/
	DOWN(0),

	/**
	 * 未知
	 **/
	UNKNOWN(2);

	private final Integer status;

	ThingLogTransferType(Integer status) {
		this.status = status;
	}

	public static ThingLogTransferType getType(Integer status) {
		switch (status) {
			case 0:
				return DOWN;
			case 1:
				return UP;
			default:
				return UNKNOWN;
		}
	}

	public Integer getStatus() {
		return status;
	}

}
