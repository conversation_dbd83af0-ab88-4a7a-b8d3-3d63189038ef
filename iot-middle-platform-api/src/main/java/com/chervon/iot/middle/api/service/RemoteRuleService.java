package com.chervon.iot.middle.api.service;

import com.chervon.iot.middle.api.vo.rule.IotConfirmDestinationVo;
import com.chervon.iot.middle.api.vo.rule.IotDestinationListVo;
import com.chervon.iot.middle.api.vo.rule.IotRuleListVo;
import com.chervon.iot.middle.api.vo.rule.IotRuleVo;

import java.util.List;

/**
 * 设备规则引擎 服务类
 * <AUTHOR>
 * @since 2022-02-14
 */
public interface RemoteRuleService {

    /**
     * 创建规则引擎
     * <AUTHOR>
     * @date 10:44 2022/3/4
     * @param iotRuleVo:
     * @return: void
     **/
    void createRule(IotRuleVo iotRuleVo);

    /**
     * 批量创建规则引擎
     *
     * @param iotRuleVoList iot规则引擎创建Vo列表
     */
    void createRuleList(List<IotRuleVo> iotRuleVoList);

    /**
     * 更新规则引擎
     * <AUTHOR>
     * @date 11:08 2022/3/4
     * @param iotRuleVo:
     * @return: void
     **/
    void updateRule(IotRuleVo iotRuleVo);

    /**
     * 更新规则引擎状态
     * <AUTHOR>
     * @date 11:38 2022/3/4
     * @param ruleName: 规则引擎名称
     * @param enable: 是否启用
     * @return: void
     **/
    void updateRuleStatus(String ruleName, Boolean enable);

    /**
     * 删除规则引擎
     * <AUTHOR>
     * @date 11:39 2022/3/4
     * @param ruleName:
     * @return: void
     **/
    void deleteRule(String ruleName);

    /**
     * 批量删除规则引擎
     *
     * @param ruleNameList 规则引擎名称列表
     */
    void deleteRuleList(List<String> ruleNameList);

    /**
     * 确认并启用目标地址
     * <AUTHOR>
     * @date 13:58 2022/3/4
     * @param iotConfirmDestinationVo:
     * @return: void
     **/
    void confirmDestination(IotConfirmDestinationVo iotConfirmDestinationVo);

    /**
     * 获取规则引擎列表
     *
     * @param max:       最大结果数量，默认25
     * @param nextToken: 上一次请求的返回值，用于标识请求下一页
     * <AUTHOR>
     * @date 15:27 2022/3/4
     * @return: com.positec.common.domain.vo.iot.RuleVo
     **/
    IotRuleListVo listRules(Integer max, String nextToken, String topic);

    /**
     * 获取规则引擎详情
     * <AUTHOR>
     * @date 15:42 2022/3/4
     * @param ruleName:规则名称
     * @return: com.positec.common.domain.vo.iot.RuleVo
     **/
    IotRuleVo getRule(String ruleName);

    /**
     * 查询规则是否存在
     *
     * @param ruleName 规则名称
     * @return 是否存在
     */
    Boolean checkRuleExists(String ruleName);

    /**
     * 删除目标地址
     * <AUTHOR>
     * @date 16:08 2022/3/4
     * @param arn:
     * @return: void
     **/
    void deleteDestination(String arn);

    /**
     * 获取目标地址列表
     * <AUTHOR>
     * @date 9:48 2022/3/8
     * @param max: 最大结果数量，默认25
     * @param nextToken: 上一次请求的返回值，用于标识请求下一页
     * @return: RuleListVo
     **/
    IotDestinationListVo listDestinations(Integer max, String nextToken);
}
