package com.chervon.iot.middle.api.dto.log;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @className TopologyPageDto
 * @description
 * @date 2022/7/11 15:05
 */
@ApiModel(description = "拓扑分页请求dto")
@Data
public class LogPageDto extends PageRequest {
    @ApiModelProperty(value = "设备id", required = true)
    String deviceId;

    @ApiModelProperty(value = "开始时间")
    Date startTime;

    @ApiModelProperty(value = "结束时间")
    Date endTime;

    /**
     * 对应数据库  isReported
     */
    @ApiModelProperty(value = "数据上下行类型 0上行 1下行")
    Integer type;

    /**
     * 对应数据字段 succeed
     */
    @ApiModelProperty(value = "日志类型 0失败 1成功")
    Integer status;

    /**
     * 日志状态
     *
     * @see com.chervon.iot.middle.api.enums.ThingLogStatus
     */
    @ApiModelProperty("日志状态")
    private String logStatus;

    /**
     * 日志通信类型
     *
     * @see com.chervon.iot.middle.api.enums.ThingLogTransferType
     */
    @ApiModelProperty("日志通信类型")
    private String logTransferType;
}
