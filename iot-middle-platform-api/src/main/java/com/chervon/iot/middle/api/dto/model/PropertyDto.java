package com.chervon.iot.middle.api.dto.model;

import com.chervon.iot.middle.api.pojo.thingmodel.Property;
import java.io.Serializable;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @className PropertyAddDto
 * @description 添加产品物模型属性
 * @date 2022/2/18 18:15
 */
@Data
public class PropertyDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品标识
     **/
    @Length(min = 1, max = 128)
    private String productKey;

    /**
     * 物模型属性
     **/
    private Property property;

}
