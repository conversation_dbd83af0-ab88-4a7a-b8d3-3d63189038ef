package com.chervon.iot.middle.api.vo.device;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @className InsertDeviceVO
 * @description
 * @date 2022/2/14 15:55
 */
@Data
public class IotDeviceCertP12Vo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("证书aws资源名称")
    private String deviceArn;

    @ApiModelProperty("P12格式证书,接口返回base64编码,app需要base64解码后写入文件使用")
    private byte[] certificateP12;

    @ApiModelProperty("证书私钥")
    private String privateKey;

    @ApiModelProperty("json格式的证书-aes加密")
    private String certJSONStr;
}
