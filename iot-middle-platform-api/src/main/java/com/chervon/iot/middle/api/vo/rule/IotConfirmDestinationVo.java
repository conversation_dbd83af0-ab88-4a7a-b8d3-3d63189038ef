package com.chervon.iot.middle.api.vo.rule;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @className confirmDestinationVo
 * @description 目标地址确认VO
 * @date/3/4 10:31
 */
@Data
public class IotConfirmDestinationVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用于确认URL的所有权或访问权的token", required = true)
    private String token;

    @ApiModelProperty(value = "目标地址ARN", required = true)
    private String arn;

}
