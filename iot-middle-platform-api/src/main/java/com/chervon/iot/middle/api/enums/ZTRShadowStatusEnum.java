package com.chervon.iot.middle.api.enums;

import java.util.Arrays;

/**
 * 61001割草机设备状态：
 * 0 - 空闲
 * 1 - 自走（主动驾驶）
 * 2 - 割草
 * 3 - 充电
 * 4 - 运输（被其他工具载运）
 */
public enum ZTRShadowStatusEnum {
    IDLE(0, "Idle"),
    DRIVING(1, "Driving"),
    MOWING(2, "Mowing"),
    CHARGING(3, "Charging"),
    TRANSPORTING(4, "Transporting")
    ;

    /**
     * 类型
     */
    private final int type;
    /**
     * 描述
     */
    private final String desc;

    ZTRShadowStatusEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取类型
     */
    public int getType() {
        return type;
    }

    /**
     * 获取描述
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 获取描述
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(ZTRShadowStatusEnum::getDesc)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举
     */
    public static ZTRShadowStatusEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
