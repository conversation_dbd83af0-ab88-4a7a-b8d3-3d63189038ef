package com.chervon.iot.middle.api.vo.product;

import com.chervon.iot.middle.api.pojo.thingmodel.Event;
import com.chervon.iot.middle.api.pojo.thingmodel.Property;
import com.chervon.iot.middle.api.pojo.thingmodel.Service;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @className IotThingModelPropertyVo
 * @description 添加产品物模型属性
 * @date 2022/2/18 18:15
 */
@Data
public class IotThingModelDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品标识
     **/
    @Length(min = 1, max = 128)
    private String productKey;
    /**
     * 物模型属性
     **/
    private List<Property> properties;
    /**
     * 物模型事件
     **/
    private List<Event> events;
    /**
     * 物模型服务
     **/
    private List<Service> services;
}
