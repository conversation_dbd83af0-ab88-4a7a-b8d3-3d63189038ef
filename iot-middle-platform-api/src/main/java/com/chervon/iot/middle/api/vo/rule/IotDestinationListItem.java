package com.chervon.iot.middle.api.vo.rule;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @className RuleListItem
 * @description 规则引擎VO
 * @date/3/4 10:31
 */
@Data
public class IotDestinationListItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "aws资源名称")
    private String arn;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    private Date lastUpdatedAt;

    @ApiModelProperty(value = "状态描述")
    private String statusReason;

    @ApiModelProperty(value = "确认地址url")
    private String confirmationUrl;
}
