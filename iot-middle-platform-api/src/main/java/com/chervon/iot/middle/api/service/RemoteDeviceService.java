package com.chervon.iot.middle.api.service;

import com.chervon.iot.middle.api.dto.device.IotInsertDeviceDto;
import com.chervon.iot.middle.api.vo.device.IotDeviceCertVo;

import java.util.Map;

/**
 * <p>
 * 设备管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-11
 */
public interface RemoteDeviceService {
    boolean checkAwsDeviceNameExisted(String deviceId);
    /**
     * 创建设备、证书、影子等信息
     **/
    void awsCreateDeviceInfo(IotInsertDeviceDto iotInsertDeviceDto,boolean createCert);

    /**
     * 将设备插入AWS IoT core
     *
     * @param iotInsertDeviceDto:
     * <AUTHOR>
     * @date 16:50 2022/2/14
     * @return: com.positec.common.domain.vo.DeviceCertVO
     **/
    IotDeviceCertVo insertDeviceToAws(IotInsertDeviceDto iotInsertDeviceDto);

    /**
     * 更新设备证书
     * <AUTHOR>
     * @date 10:26 2022/9/22
     * @param deviceId: 
     * @return com.chervon.iot.middle.api.vo.device.IotDeviceCertVo
     **/
    IotDeviceCertVo updateDeviceCert(String deviceId);

    /**
     * 更新设备绑定证书状态
     * <AUTHOR>
     * @date 11:18 2022/7/16
     * @param deviceId: 设备id
     * @param active: 证书状态 true激活 false禁用
     * @return void
     **/
    void updateCertStatus(String deviceId, Boolean active);

    /**
     * 通过设备id获取产品标识
     * <AUTHOR>
     * @date 10:27 2022/7/18
     * @param deviceId:
     * @return java.lang.String
     **/
    String getProductKey(String deviceId);

    /**
     * 更新设备属性
     * <AUTHOR>
     * @date 10:19 2022/9/20
     * @param deviceId:
     * @param newAttributes:
     * @return void
     **/
    void updateAttributes(String deviceId, Map<String, String> newAttributes);

    /**
     * 获取设备属性
     *
     * @param deviceId:
     * @param attributeName: 属性名称
     * @return void
     * <AUTHOR>
     * @date 10:19 2022/9/20
     **/
    String getAttributes(String deviceId, String attributeName);


}
