package com.chervon.iot.middle.api.pojo.thingmodel;

import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @className Property
 * @description 物模型属性
 * @date 2022/5/9 13:59
 */
@Data
public class Property extends BaseThingModelItem implements Serializable {

    private static final long serialVersionUID = 1L;

     /**
     * r上报/w下发/rw上报&下发
     **/
    private String accessMode;

    /**
     * 数据类型
     **/
    @NotNull(message = "dataType不能为空")
    private DataType dataType;

    /**
     * 备注信息
     **/
    private String comments;

}
