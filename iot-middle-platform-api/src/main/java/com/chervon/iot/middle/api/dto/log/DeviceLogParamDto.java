package com.chervon.iot.middle.api.dto.log;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2022年12月6日
 */
@ApiModel(description = "设备日志查询")
@Data
public class DeviceLogParamDto implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "设备id", required = true)
	@NotBlank
	String deviceId;

	@ApiModelProperty(value = "开始时间,默认当前时间")
	Long startTime;
}
