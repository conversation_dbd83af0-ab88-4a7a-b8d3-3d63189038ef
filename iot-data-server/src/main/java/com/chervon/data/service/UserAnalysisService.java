package com.chervon.data.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.data.api.enums.UserAnalysisTypeEnum;
import com.chervon.data.domain.dataobject.user.analysis.UserAnalysis;
import com.chervon.data.domain.dto.DateQueryDto;
import com.chervon.data.domain.vo.DefectionUserAnalysisVo;
import com.chervon.data.domain.vo.OverviewVo;
import com.chervon.data.domain.vo.UserAnalysisVo;

/**
 * <AUTHOR>
 * @since 2023-04-11 19:41
 **/
public interface UserAnalysisService extends IService<UserAnalysis> {
    /**
     * 日增用户概览
     *
     * @param dateQueryDto 日期
     * @return 概览数据
     */
    OverviewVo dailyIncreaseUserOverview(DateQueryDto dateQueryDto);

    /**
     * 日活用户概览
     *
     * @param dateQueryDto 日期
     * @return 概览数据
     */
    OverviewVo dailyActiveUserOverview(DateQueryDto dateQueryDto);

    /**
     * 月增用户概览
     *
     * @param dateQueryDto 日期
     * @return 概览数据
     */
    OverviewVo monthlyIncreaseUserOverview(DateQueryDto dateQueryDto);

    /**
     * 月活用户概览
     *
     * @param dateQueryDto 日期
     * @return 概览数据
     */
    OverviewVo monthlyActiveUserOverview(DateQueryDto dateQueryDto);

    /**
     * 累计新增用户总数概览
     *
     * @param dateQueryDto 日期
     * @return 概览数据
     */
    OverviewVo totalIncreaseUserOverview(DateQueryDto dateQueryDto);

    /**
     * 用户流失数概览
     *
     * @param dateQueryDto 日期
     * @return 概览数据
     */
    OverviewVo defectionUserOverview(DateQueryDto dateQueryDto);

    /**
     * 获取用户分析列表
     *
     * @param dto 日期+查询类型
     * @return 图表VO列表
     */
    UserAnalysisVo listUserAnalysis(UserAnalysisTypeEnum typeEnum, DateQueryDto dto);

    /**
     * 获取日流失用户分析
     *
     * @param dto 日期
     * @return 流失用户Vo
     */
    DefectionUserAnalysisVo listDefectionUserAnalysis(DateQueryDto dto);
}
