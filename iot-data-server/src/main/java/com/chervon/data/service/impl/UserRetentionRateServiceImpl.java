package com.chervon.data.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.data.api.exception.DataErrorCode;
import com.chervon.data.config.ExceptionMessageUtil;
import com.chervon.data.domain.dataobject.user.analysis.UserRetentionRate;
import com.chervon.data.domain.dto.DateQueryDto;
import com.chervon.data.domain.vo.UserRetentionRateVo;
import com.chervon.data.mapper.UserRetentionRateMapper;
import com.chervon.data.service.UserRetentionRateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-04-12 19:03
 **/
@Service
@Slf4j
public class UserRetentionRateServiceImpl extends ServiceImpl<UserRetentionRateMapper, com.chervon.data.domain.dataobject.user.analysis.UserRetentionRate>
        implements UserRetentionRateService {
    @Override
    public UserRetentionRateVo getByDate(String date) {
        UserRetentionRate userRetentionRate = this.getOne(new LambdaQueryWrapper<UserRetentionRate>()
                .eq(UserRetentionRate::getDataTime, date).last("limit 1"));
        if (null == userRetentionRate) {
            log.warn("UserRetentionRateServiceImpl#getByDate -> data not exist:{}", date);
            return null;
        }
        return UserRetentionRateVo.convertFromUserRetentionRate(userRetentionRate);
    }

    @Override
    public List<UserRetentionRateVo> listByDate(DateQueryDto dateQueryDto) {
        List<UserRetentionRate> userRetentionRates = this.list(new LambdaQueryWrapper<UserRetentionRate>()
                .ge(UserRetentionRate::getDataTime, dateQueryDto.getStartTime())
                .le(UserRetentionRate::getDataTime, dateQueryDto.getEndTime())
                .orderByAsc(UserRetentionRate::getDataTime));
        if (CollectionUtils.isEmpty(userRetentionRates)) {
            log.warn("UserRetentionRateServiceImpl#getByDate -> data not exist: [{},{}]",
                    dateQueryDto.getStartTime(), dateQueryDto.getEndTime());
            return null;
        }
        UserRetentionRate averageRetentionUser = new UserRetentionRate();
        averageRetentionUser.setDataTime("平均");
        averageRetentionUser.setDailyIncreaseUsers(CommonConstant.ZERO);
        averageRetentionUser.setT1RetentionRate(BigDecimal.valueOf(0));
        averageRetentionUser.setT2RetentionRate(BigDecimal.valueOf(0));
        averageRetentionUser.setT3RetentionRate(BigDecimal.valueOf(0));
        averageRetentionUser.setT4RetentionRate(BigDecimal.valueOf(0));
        averageRetentionUser.setT5RetentionRate(BigDecimal.valueOf(0));
        averageRetentionUser.setT6RetentionRate(BigDecimal.valueOf(0));
        averageRetentionUser.setT7RetentionRate(BigDecimal.valueOf(0));
        averageRetentionUser.setT30RetentionRate(BigDecimal.valueOf(0));
        averageRetentionUser.setT60RetentionRate(BigDecimal.valueOf(0));
        averageRetentionUser.setT90RetentionRate(BigDecimal.valueOf(0));
        int c1 = 0;
        int c2 = 0;
        int c3 = 0;
        int c4 = 0;
        int c5 = 0;
        int c6 = 0;
        int c7 = 0;
        int c30 = 0;
        int c60 = 0;
        int c90 = 0;
        for (UserRetentionRate retentionRate : userRetentionRates) {
            averageRetentionUser.setDailyIncreaseUsers(averageRetentionUser.getDailyIncreaseUsers() + retentionRate.getDailyIncreaseUsers());
            if (retentionRate.getT1RetentionRate() != null) {
                averageRetentionUser.setT1RetentionRate(averageRetentionUser.getT1RetentionRate().add(retentionRate.getT1RetentionRate()));
                c1++;
            }
            if (retentionRate.getT2RetentionRate() != null) {
                averageRetentionUser.setT2RetentionRate(averageRetentionUser.getT2RetentionRate().add(retentionRate.getT2RetentionRate()));
                c2++;
            }
            if (retentionRate.getT3RetentionRate() != null) {
                averageRetentionUser.setT3RetentionRate(averageRetentionUser.getT3RetentionRate().add(retentionRate.getT3RetentionRate()));
                c3++;
            }
            if (retentionRate.getT4RetentionRate() != null) {
                averageRetentionUser.setT4RetentionRate(averageRetentionUser.getT4RetentionRate().add(retentionRate.getT4RetentionRate()));
                c4++;
            }
            if (retentionRate.getT5RetentionRate() != null) {
                averageRetentionUser.setT5RetentionRate(averageRetentionUser.getT5RetentionRate().add(retentionRate.getT5RetentionRate()));
                c5++;
            }
            if (retentionRate.getT6RetentionRate() != null) {
                averageRetentionUser.setT6RetentionRate(averageRetentionUser.getT6RetentionRate().add(retentionRate.getT6RetentionRate()));
                c6++;
            }
            if (retentionRate.getT7RetentionRate() != null) {
                averageRetentionUser.setT7RetentionRate(averageRetentionUser.getT7RetentionRate().add(retentionRate.getT7RetentionRate()));
                c7++;
            }
            if (retentionRate.getT30RetentionRate() != null) {
                averageRetentionUser.setT30RetentionRate(averageRetentionUser.getT30RetentionRate().add(retentionRate.getT30RetentionRate()));
                c30++;
            }
            if (retentionRate.getT60RetentionRate() != null) {
                averageRetentionUser.setT60RetentionRate(averageRetentionUser.getT60RetentionRate().add(retentionRate.getT60RetentionRate()));
                c60++;
            }
            if (retentionRate.getT90RetentionRate() != null) {
                averageRetentionUser.setT90RetentionRate(averageRetentionUser.getT90RetentionRate().add(retentionRate.getT90RetentionRate()));
                c90++;
            }
        }
        averageRetentionUser.setDailyIncreaseUsers(averageRetentionUser.getDailyIncreaseUsers() / userRetentionRates.size());
        if (c1 > 0) {
            averageRetentionUser.setT1RetentionRate(averageRetentionUser.getT1RetentionRate().divide(BigDecimal.valueOf(c1), RoundingMode.HALF_UP));
        }
        if (c2 > 0) {
            averageRetentionUser.setT2RetentionRate(averageRetentionUser.getT2RetentionRate().divide(BigDecimal.valueOf(c2), RoundingMode.HALF_UP));
        }
        if (c3 > 0) {
            averageRetentionUser.setT3RetentionRate(averageRetentionUser.getT3RetentionRate().divide(BigDecimal.valueOf(c3), RoundingMode.HALF_UP));
        }
        if (c4 > 0) {
            averageRetentionUser.setT4RetentionRate(averageRetentionUser.getT4RetentionRate().divide(BigDecimal.valueOf(c4), RoundingMode.HALF_UP));
        }
        if (c5 > 0) {
            averageRetentionUser.setT5RetentionRate(averageRetentionUser.getT5RetentionRate().divide(BigDecimal.valueOf(c5), RoundingMode.HALF_UP));
        }
        if (c6 > 0) {
            averageRetentionUser.setT6RetentionRate(averageRetentionUser.getT6RetentionRate().divide(BigDecimal.valueOf(c6), RoundingMode.HALF_UP));
        }
        if (c7 > 0) {
            averageRetentionUser.setT7RetentionRate(averageRetentionUser.getT7RetentionRate().divide(BigDecimal.valueOf(c7), RoundingMode.HALF_UP));
        }
        if (c30 > 0) {
            averageRetentionUser.setT30RetentionRate(averageRetentionUser.getT30RetentionRate().divide(BigDecimal.valueOf(c30), RoundingMode.HALF_UP));
        }
        if (c60 > 0) {
            averageRetentionUser.setT60RetentionRate(averageRetentionUser.getT60RetentionRate().divide(BigDecimal.valueOf(c60), RoundingMode.HALF_UP));
        }
        if (c90 > 0) {
            averageRetentionUser.setT90RetentionRate(averageRetentionUser.getT90RetentionRate().divide(BigDecimal.valueOf(c90), RoundingMode.HALF_UP));
        }
        userRetentionRates.add(0, averageRetentionUser);
        return UserRetentionRateVo.convertListFromUserRetentionRateList(userRetentionRates);
    }
}
