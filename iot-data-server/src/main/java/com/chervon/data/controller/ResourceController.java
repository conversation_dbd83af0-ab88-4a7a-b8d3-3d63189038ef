package com.chervon.data.controller;

import com.chervon.authority.api.core.ResourceTreeElementVo;
import com.chervon.authority.api.service.RemoteResourceService;
import com.chervon.common.core.domain.SingleInfoReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/1 14:33
 */
@RestController
@RequestMapping("/resource")
@AllArgsConstructor
@Api(value = "菜单资源接口", tags = {"菜单资源接口"})
public class ResourceController {

    @DubboReference
    private RemoteResourceService remoteResourceService;

    /**
     * 获取当前登录用户有权限资源树
     *
     * @return List<ResourceTreeElementVo> 资源树List
     */
    @ApiOperation("获取当前登录用户有权限资源树")
    @PostMapping("/list")
    public List<ResourceTreeElementVo> list(@RequestBody SingleInfoReq<String> appIdReq) {
        return remoteResourceService.listTree(LocaleContextHolder.getLocale().getLanguage(), appIdReq.getReq());
    }

}
