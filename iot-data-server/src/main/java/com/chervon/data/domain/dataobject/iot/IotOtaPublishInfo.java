package com.chervon.data.domain.dataobject.iot;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 5.1 IOT数仓表设计
 * 5.1.7 OTA发布信息表（iot_ota_publish_info）
 *
 * <AUTHOR>
 * @since 2023-04-10 17:42
 **/
@Data
@TableName("iot_ota_publish_info")
public class IotOtaPublishInfo implements Serializable {
    /**
     * ID，自增
     */
    private Long id;
    /**
     * 品类ID
     */
    private String categoryId;
    /**
     * 品类名
     */
    private String categoryName;
    /**
     * 产品ID
     */
    private String productId;
    /**
     * 产品名
     */
    private String productName;
    /**
     * 产品型号
     */
    private String model;
    /**
     * 技术版本号
     */
    private String technologyVersion;
    /**
     * ota发布时间
     */
    private String publishTime;
}