package com.chervon.data.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-04-13 16:40
 **/
@Data
public class DefectionUserAnalysisVo implements Serializable {
    /**
     * 总流失用户数
     */
    @ApiModelProperty("总流失用户数")
    private Long total;
    /**
     * 流失用户图表Vo列表(多一列流失率)
     */
    @ApiModelProperty("流失用户图表Vo列表(多一列流失率)")
    private List<DefectionUserGraphVo> list;
}
