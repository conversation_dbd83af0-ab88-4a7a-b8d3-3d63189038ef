package com.chervon.data.domain.dataobject.device.service.analysis;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 5.2.7 设备指令下发分析
 * 2、品类-服务使用量占比（t_device_category_service_dist_d）
 *
 * <AUTHOR>
 * @since 2023-04-11 11:38
 **/
@Data
@TableName("t_device_category_service_dist_d")
public class DeviceCategoryServiceDist implements Serializable {
    /**
     * ID，自增
     */
    private Long id;
    /**
     * 品类ID
     */
    private Long categoryId;
    /**
     * 品类名称
     */
    private String categoryName;
    /**
     * 服务使用量
     */
    private Long categoryUseCn;
    /**
     * 服务使用量占比
     */
    private String categoryUsePer;
    /**
     * 数据时间，yyyy-mm-dd
     */
    private String dataTime;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;
}
