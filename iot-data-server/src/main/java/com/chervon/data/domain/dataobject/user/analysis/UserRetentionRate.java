package com.chervon.data.domain.dataobject.user.analysis;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 5.2.1 用户分析
 * 2、用户留存率日表（t_user_retention_rate_d）
 *
 * <AUTHOR>
 * @since 2023-04-10 18:23
 **/
@Data
@TableName("t_user_retention_rate_d")
public class UserRetentionRate implements Serializable {
    /**
     * ID，自增
     */
    private Long id;
    /**
     * 数据日期t，yyyy-mm-dd
     */
    private String dataTime;
    /**
     * 新增用户数
     */
    private Integer dailyIncreaseUsers;
    /**
     * t+1留存量
     */
    private Integer t1RetentionCn;
    /**
     * t+1留存率
     */
    private BigDecimal t1RetentionRate;
    /**
     * t+2留存量
     */
    private Integer t2RetentionCn;
    /**
     * t+2留存率
     */
    private BigDecimal t2RetentionRate;
    /**
     * t+3留存量
     */
    private Integer t3RetentionCn;
    /**
     * t+3留存率
     */
    private BigDecimal t3RetentionRate;
    /**
     * t+4留存量
     */
    private Integer t4RetentionCn;
    /**
     * t+4留存率
     */
    private BigDecimal t4RetentionRate;
    /**
     * t+5留存量
     */
    private Integer t5RetentionCn;
    /**
     * t+5留存率
     */
    private BigDecimal t5RetentionRate;
    /**
     * t+6留存量
     */
    private Integer t6RetentionCn;
    /**
     * t+6留存率
     */
    private BigDecimal t6RetentionRate;
    /**
     * t+7留存量
     */
    private Integer t7RetentionCn;
    /**
     * t+7留存率
     */
    private BigDecimal t7RetentionRate;
    /**
     * t+30留存量
     */
    private Integer t30RetentionCn;
    /**
     * t+30留存率
     */
    private BigDecimal t30RetentionRate;
    /**
     * t+60留存量
     */
    private Integer t60RetentionCn;
    /**
     * t+60留存率
     */
    private BigDecimal t60RetentionRate;
    /**
     * t+90留存量
     */
    private Integer t90RetentionCn;
    /**
     * t+90留存率
     */
    private BigDecimal t90RetentionRate;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;
}
