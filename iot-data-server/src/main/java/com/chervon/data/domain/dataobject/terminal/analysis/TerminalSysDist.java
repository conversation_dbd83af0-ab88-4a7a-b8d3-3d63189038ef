package com.chervon.data.domain.dataobject.terminal.analysis;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 5.2.2 移动终端分析
 * 2、终端系统版本分布日表（t_terminal_sys_dist_d）
 *
 * <AUTHOR>
 * @since 2023-04-10 18:41
 **/
@Data
@TableName("t_terminal_sys_dist_d")
public class TerminalSysDist implements Serializable {
    /**
     * ID，自增
     */
    private Long id;
    /**
     * 系统版本
     */
    private String phoneOsVersion;
    /**
     * 系统版本量
     */
    private Integer phoneOsVersionCn;
    /**
     * 系统版本分布占比
     */
    private String phoneOsVersionPer;
    /**
     * 数据时间，yyyy-mm-dd
     */
    private String dataTime;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
