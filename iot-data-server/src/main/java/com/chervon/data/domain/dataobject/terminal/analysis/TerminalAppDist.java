package com.chervon.data.domain.dataobject.terminal.analysis;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 5.2.2 移动终端分析
 * 3、终端App版本分布日表（t_terminal_app_dist_d）
 *
 * <AUTHOR>
 * @since 2023-04-11 09:37
 **/
@Data
@TableName("t_terminal_app_dist_d")
public class TerminalAppDist implements Serializable {
    /**
     * 主键ID,自增
     */
    private Long id;
    /**
     * APP版本
     */
    private String appVersion;
    /**
     * APP版本量
     */
    private Integer appVersionCn;
    /**
     * APP版本分布占比
     */
    private String appVersionPer;
    /**
     * 数据时间，yyyy-mm-dd
     */
    private String dataTime;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}