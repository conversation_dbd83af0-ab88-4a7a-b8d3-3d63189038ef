package com.chervon.data.domain.dataobject.device.ota.analysis;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 5.2.9 OTA分析
 * 1、设备OTA执行结果分析日表（t_device_ota_res_analysis_d）
 *
 * <AUTHOR>
 * @since 2023-04-11 15:15
 **/
@Data
@TableName("t_device_ota_res_analysis_d")
public class DeviceOtaResAnalysis implements Serializable {
    /**
     * ID，自增
     */
    private Long id;
    /**
     * 品类ID
     */
    private Long categoryId;
    /**
     * 品类名称
     */
    private String categoryName;
    /**
     * 产品ID
     */
    private Long productId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * OTA执行总次数
     */
    private Long otaExecuteTotalCn;
    /**
     * OTA执行成功次数
     */
    private Long otaExecuteSuccCn;
    /**
     * OTA执行成功占比
     */
    private String otaExecuteSuccPer;
    /**
     * OTA执行失败次数
     */
    private Long otaExecuteFailCn;
    /**
     * OTA执行失败占比
     */
    private String otaExecuteFailPer;
    /**
     * 数据时间，yyyy-mm-dd
     */
    private String dataTime;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;
}
