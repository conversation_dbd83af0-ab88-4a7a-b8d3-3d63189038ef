package com.chervon.data.domain.dataobject.device.ota.analysis;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023-04-11 17:31
 **/
@Data
@TableName("t_device_firmware_dist_d")
public class DeviceFirmwareDist implements Serializable {
    /**
     * ID，自增
     */
    private Long id;
    /**
     * 品类ID
     */
    private Long categoryId;
    /**
     * 品类名称
     */
    private String categoryName;
    /**
     * 产品ID
     */
    private Long productId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 技术固件版本号
     */
    private String technologyVersion;
    /**
     * 技术固件版本量
     */
    private Integer technologyVersionCn;
    /**
     * 技术固件版本占比
     */
    private String technologyVersionPer;
    /**
     * 数据时间，yyyy-mm-dd
     */
    private String dataTime;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;
}
