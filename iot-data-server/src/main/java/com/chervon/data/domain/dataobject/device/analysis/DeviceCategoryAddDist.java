package com.chervon.data.domain.dataobject.device.analysis;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 5.2.6 设备分析
 * 2、设备品类接入量分布日表（t_device_category_add_dist_d）
 *
 * <AUTHOR>
 * @since 2023-04-11 11:15
 **/
@Data
@TableName("t_device_category_add_dist_d")
public class DeviceCategoryAddDist implements Serializable {
    /**
     * ID，自增
     */
    private Long id;
    /**
     * 品类
     */
    private String categoryName;
    /**
     * 接入量
     */
    private Long categoryAddCn;
    /**
     * 品类接入量分布占比
     */
    private String categoryAddPer;
    /**
     * 数据时间，yyyy-mm-dd
     */
    private String dataTime;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;
}
