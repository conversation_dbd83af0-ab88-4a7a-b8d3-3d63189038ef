package com.chervon.data.domain.dataobject.user.access.analysis;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 5.2.3 用户访问分析
 * 2、用户访问停留分布日表（t_user_access_duration_dist_d）
 *
 * <AUTHOR>
 * @since 2023-04-11 10:18
 **/
@Data
@TableName("t_user_access_duration_dist_d")
public class UserAccessDurationDist implements Serializable {
    /**
     * ID，自增
     */
    private Long id;
    /**
     * 停留时长区间，按照：0-3s, 3s-10s,11s-20s, 21s-30s, 31s-40s,41s-60s, 61s- 120s,121s-300s,301s-600s,600s-1800s, 1800s以上
     */
    private String durationSection;
    /**
     * 区间量
     */
    private Integer sectionCn;
    /**
     * 分布占比
     */
    private String durationSectionPer;
    /**
     * 数据时间，yyyy-mm-dd
     */
    private String dataTime;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;
}
