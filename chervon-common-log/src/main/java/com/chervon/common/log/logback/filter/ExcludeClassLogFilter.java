package com.chervon.common.log.logback.filter;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.filter.Filter;
import ch.qos.logback.core.spi.FilterReply;
import lombok.*;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.stream.Collectors;


/**
 * 排除Class打印日志的拦截器
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class ExcludeClassLogFilter extends Filter<ILoggingEvent> {
    /**
     * 排除的Class
     */
    String excludeClassName;

    /**
     * 排除的Class Set.
     */
    @Setter(value = AccessLevel.NONE)
    @Getter(value = AccessLevel.NONE)
    private HashSet<String> excludeSet = new HashSet<>();

    @Override
    public FilterReply decide(ILoggingEvent event) {
        if (excludeSet.isEmpty()) {
            return FilterReply.NEUTRAL;
        }

        StackTraceElement[] cda = event.getCallerData();
        if (cda != null && cda.length > 0 && this.excludeSet.contains(cda[0].getClassName())) {
            return FilterReply.DENY;
        }

        return FilterReply.NEUTRAL;
    }


    @Override
    public void start() {
        if (this.excludeClassName != null) {
            String[] arr = this.excludeClassName.split(",");
            this.excludeSet.addAll(Arrays.stream(arr).filter(StringUtils::hasText).collect(Collectors.toSet()));
        }

        super.start();
    }
}