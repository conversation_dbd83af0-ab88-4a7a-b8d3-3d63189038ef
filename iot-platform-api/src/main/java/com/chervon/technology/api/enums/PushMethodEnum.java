package com.chervon.technology.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022-09-09 19:16
 **/
@Getter
@AllArgsConstructor
public enum PushMethodEnum {
    /**
     * 墓碑
     */
    TOMBSTONE("TOMBSTONE", 0, 1),
    /**
     * APP 弹窗
     */
    POPUP("POPUP", 1, 1),
    /**
     * APP BANNER
     */
    BANNER("BANNER", 2, 1),
    /**
     * APP 消息管理
     */
    MESSAGE_MANAGE("MESSAGE_MANAGE", 3, 1),
    /**
     * 短信
     */
    MESSAGE("MESSAGE", 4, 1),
    /**
     * 邮箱
     */
    MAIL("MAIL", 5, 1),
    /**
     * fleet web推送
     */
    FLEET_WEB("FLEET_WEB", 6, 2),
    /**
     * fleet邮件
     */
    FLEET_MAIL("FLEET_MAIL", 7, 2),
    /**
     * 电话语音
     */
    PHONE_VOICE("PHONE_VOICE",8,1),
    /**
     * 消息清除
     */
    MESSAGE_REMOVE("MESSAGE_REMOVE", 9, 1)
    ;

    private String value;
    /**
     * 消息推送方式：0墓碑消息，1APP弹窗，2APP banner, 3消息管理
     * 用于给消息中心，推送消息方法调用
     * 其中站内消息是1,2
     */
    private Integer pushTypes;

    private Integer businessType;
}
