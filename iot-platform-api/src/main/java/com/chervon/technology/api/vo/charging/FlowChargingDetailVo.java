package com.chervon.technology.api.vo.charging;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/2/25 14:18
 */
@Data
@ApiModel(description = "流量计费详情对象")
public class FlowChargingDetailVo implements Serializable {

    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("SIM ID")
    private String iccid;

    @ApiModelProperty("计费开始时间")
    private String chargingStartTime;

    @ApiModelProperty("计费结束时间")
    private String chargingEndTime;

    @ApiModelProperty("单台设备流量总计（MB）")
    private String totalFlow;

}
