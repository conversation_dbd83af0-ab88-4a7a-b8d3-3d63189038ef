package com.chervon.technology.api.dto.charging;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/2/25 14:35
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "流量计费详情分页请求对象")
public class FlowChargingDetailPageDto extends PageRequest implements Serializable {

    @ApiModelProperty("计费id")
    private Long chargingId;

    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("SIM ID")
    private String iccid;

    @ApiModelProperty("计费开始时间 yyyy-MM-dd")
    private String chargingStartTime;

    @ApiModelProperty("计费结束时间 yyyy-MM-dd")
    private String chargingEndTime;
}
