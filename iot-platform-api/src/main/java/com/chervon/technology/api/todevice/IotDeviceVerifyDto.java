package com.chervon.technology.api.todevice;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-07-21 16:11
 **/
@Data
public class IotDeviceVerifyDto implements Serializable {
    /**
     * 设备Id
     */
    @NotEmpty(message = "deviceId is empty")
    private String deviceId;
    /**
     * 签名
     */
    @NotEmpty(message = "sign is empty")
    private String sign;
}
