package com.chervon.technology.api.vo;

import com.chervon.common.core.annotation.Sensitive;
import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.chervon.common.core.enums.SensitiveStrategy;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/7/6 10:26
 */
@Data
public class OperationComponentOtaVo implements Serializable {

    /**
     * 原始固件版本
     **/
    @ApiModelProperty("原始固件版本")
    private String oldVersion;

    /**
     * 目标固件版本
     **/
    @ApiModelProperty("目标固件版本")
    private String newVersion;

    /**
     * 升级状态
     **/
    @ApiModelProperty("总成固件升级状态  0: 等待升级 初始状态  1:下载中  2:升级中  3:升级成功  4:升级失败")
    private Integer status;

    /**
     * 失败原因
     **/
    @ApiModelProperty("失败原因")
    private String detail;

    /**
     * 操作用户id
     **/
    @ApiModelProperty("操作用户id")
    @Sensitive(strategy = SensitiveStrategy.USER_ID)
    private String userId;

    /**
     * 升级时间
     **/
    @ApiModelProperty("升级时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime upgradeTime;
}
