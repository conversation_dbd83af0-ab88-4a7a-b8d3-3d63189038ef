package com.chervon.technology.api.vo;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.chervon.technology.api.enums.DeviceOnlineStatusEnum;
import com.chervon.technology.api.enums.DeviceStatusEnum;
import com.chervon.technology.api.enums.DeviceUsageStatusEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/6 10:16
 */
@Data
@ApiModel(description = "运营平台设备管理-设备列表对象")
public class OperationDevicePageVo implements Serializable {

    @ApiModelProperty("设备ID")
    private String deviceId;

    @ApiModelProperty("SN")
    private String sn;

    @ApiModelProperty("PID")
    private Long pid;

    @ApiModelProperty("品类id")
    private Long categoryId;

    @ApiModelProperty("品牌id")
    private Long brandId;

    @ApiModelProperty("产品型号")
    private String productModel;

    @ApiModelProperty("商品型号/Model #")
    private String commodityModel;

    @ApiModelProperty("设备类型")
    private String productType;

    @ApiModelProperty("注册手机号")
    private String devicePhoneNum;

    @ApiModelProperty("在线状态")
    private DeviceOnlineStatusEnum isOnline;

    @ApiModelProperty("设备状态：DISABLE 停用 NORMAL 正常")
    private DeviceStatusEnum status;

    @ApiModelProperty("当前绑定适用app类型 1 ego 2 fleet")
    private Integer currentBindBusinessType;

    @ApiModelProperty("当前绑定ego用户id")
    private List<String> currentBindEgoUserId;

    @ApiModelProperty("当前绑定fleet租户id")
    private String currentBindFleetCompanyId;

    @ApiModelProperty("使用状态")
    private DeviceUsageStatusEnum usageStatus;

    @ApiModelProperty("首次绑定适用app类型 1 ego 2 fleet")
    private Integer firstBindBusinessType;

    @ApiModelProperty("首次绑定ego用户id")
    private String firstBindEgoUserId;

    @ApiModelProperty("首次绑定fleet租户id")
    private String firstBindFleetCompanyId;

    @ApiModelProperty("首次绑定fleet操作账号id")
    private String firstBindFleetUserId;

    @ApiModelProperty("设备激活时间，设备注册的时候设置设备为激活状态")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime activationTime;

    @ApiModelProperty("首次绑定时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime firstBindTime;

    @ApiModelProperty("设备最后一次上线时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime lastLoginTime;

}
