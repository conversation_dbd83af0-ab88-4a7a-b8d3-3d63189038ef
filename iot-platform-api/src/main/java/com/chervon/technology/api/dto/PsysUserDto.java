package com.chervon.technology.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/11/12 14:47
 */
@Data
@ApiModel(description = "系统用户信息对象")
public class PsysUserDto implements Serializable {

    @ApiModelProperty(value = "用户id，删除不用填")
    private Long sysUserId;

    @ApiModelProperty(value = "用户guid，删除不用填")
    private String sysUserGuid;

    @ApiModelProperty(value = "用户工号")
    private String sysUserEmployeeNumber;

    @ApiModelProperty(value = "用户名，删除不用填")
    private String sysUsername;

}
