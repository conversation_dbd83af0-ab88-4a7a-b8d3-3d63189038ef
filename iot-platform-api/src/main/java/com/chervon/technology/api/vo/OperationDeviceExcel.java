package com.chervon.technology.api.vo;

import cn.hutool.core.annotation.Alias;
import cn.hutool.core.util.DesensitizedUtil;
import com.chervon.common.core.utils.CsvUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/6 10:19
 */
@Data
public class OperationDeviceExcel implements Serializable {

    @Alias("设备ID")
    private String deviceId;

    @Alias("SN")
    private String sn;

    @Alias("PID")
    private Long pid;

    @Alias("品类名称")
    private String categoryName;

    @Alias("品牌名称")
    private String brandName;

    @Alias("产品型号")
    private String productModel;

    @Alias("商品型号/Model #")
    private String commodityModel;

    @Alias("设备类型")
    private String productType;

    @Alias("注册手机号")
    private String devicePhoneNum;

    @Alias("在线状态")
    private String isOnline;

    @Alias("设备状态")
    private String status;

    @Alias("当前绑定APP")
    private String currentBindBusinessType;

    @Alias("当前绑定EGO用户ID")
    private List<Long> currentBindEgoUserId;

    @Alias("当前绑定Fleet租户ID")
    private Long currentBindFleetCompanyId;

    @Alias("使用状态")
    private String usageStatus;

    @Alias("首次绑定APP")
    private String firstBindBusinessType;

    @Alias("首次绑定EGO用户ID")
    private String firstBindEgoUserId;

    @Alias("首次绑定Fleet租户ID")
    private String firstBindFleetCompanyId;

    @Alias("首次绑定Fleet操作帐户ID")
    private String firstBindFleetUserId;

    @Alias("激活时间")
    private String activationTime;

    @Alias("首次绑定时间")
    private String firstBindTime;

    @Alias("最后上线时间")
    private String lastLoginTime;

    public String getDeviceId() {
        return CsvUtil.format(this.deviceId);
    }

    public String getSn() {
        return CsvUtil.format(this.sn);
    }

    public String getPid() {
        return CsvUtil.format(this.pid == null ? "" : this.pid + "");
    }

    public String getCategoryName() {
        return CsvUtil.format(this.categoryName);
    }

    public String getBrandName() {
        return CsvUtil.format(this.brandName);
    }

    public String getProductModel() {
        return CsvUtil.format(this.productModel);
    }

    public String getCommodityModel() {
        return CsvUtil.format(this.commodityModel);
    }

    public String getProductType() {
        return CsvUtil.format(this.productType);
    }

    public String getDevicePhoneNum() {
        return CsvUtil.format(DesensitizedUtil.mobilePhone(this.devicePhoneNum));
    }

    public String getIsOnline() {
        return CsvUtil.format(this.isOnline);
    }

    public String getStatus() {
        return CsvUtil.format(this.status);
    }

    public String getCurrentBindBusinessType() {
        return CsvUtil.format(this.currentBindBusinessType);
    }

    public String getCurrentBindEgoUserId() {
        String str = "";
        if (this.currentBindEgoUserId != null) {
            str = this.currentBindEgoUserId.stream().map(e -> DesensitizedUtil.idCardNum(e == null ? null : e + "", 7, 8))
                    .collect(Collectors.joining(",", "[", "]"));
        }
        return CsvUtil.format(str);
    }

    public String getCurrentBindFleetCompanyId() {
        return CsvUtil.format(DesensitizedUtil.idCardNum(this.currentBindFleetCompanyId == null ? null : this.currentBindFleetCompanyId + "", 7, 8));
    }

    public String getUsageStatus() {
        return CsvUtil.format(this.usageStatus);
    }

    public String getFirstBindBusinessType() {
        return CsvUtil.format(this.firstBindBusinessType);
    }

    public String getFirstBindEgoUserId() {
        return CsvUtil.format(DesensitizedUtil.idCardNum(this.firstBindEgoUserId, 7, 8));
    }

    public String getFirstBindFleetCompanyId() {
        return CsvUtil.format(DesensitizedUtil.idCardNum(this.firstBindFleetCompanyId, 7, 8));
    }

    public String getFirstBindFleetUserId() {
        return CsvUtil.format(DesensitizedUtil.idCardNum(this.firstBindFleetUserId, 7, 8));
    }

    public String getActivationTime() {
        return CsvUtil.format(this.activationTime);
    }

    public String getFirstBindTime() {
        return CsvUtil.format(this.firstBindTime);
    }

    public String getLastLoginTime() {
        return CsvUtil.format(this.lastLoginTime);
    }
}
