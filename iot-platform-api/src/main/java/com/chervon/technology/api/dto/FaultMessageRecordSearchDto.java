package com.chervon.technology.api.dto;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022年12月19日
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FaultMessageRecordSearchDto extends PageRequest implements Serializable {

	@ApiModelProperty(value = "消息id")
	private String msgId;

	@ApiModelProperty(value = "消息标题")
	private String title;

	@ApiModelProperty(value = "推送方式")
	private String pushMethod;

	@ApiModelProperty(value = "消息类型，0系统消息，1营销消息, 2设备消息 3反馈消息")
	private Integer messageType;
	/**
	 * 产品型号
	 */
	@ApiModelProperty("产品型号")
	private String model;

	/**
	 * 商品型号Model#
	 */
	@ApiModelProperty("商品型号Model#")
	private String commodityModel;

}
