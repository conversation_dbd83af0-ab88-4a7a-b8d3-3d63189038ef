package com.chervon.technology.api.vo.thingmodel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @className ParamData
 * @description 事件或服务出参入参
 * @date 2022/5/9 14:30
 */
@Data
public class ParamData extends BaseThingModelItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 参数数据类型
     **/
    @ApiModelProperty("参数数据类型")
    private DataType dataType;

}
