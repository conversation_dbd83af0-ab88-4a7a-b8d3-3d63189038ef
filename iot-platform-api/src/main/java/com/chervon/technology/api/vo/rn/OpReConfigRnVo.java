package com.chervon.technology.api.vo.rn;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/8 16:14
 */
@Data
@ApiModel(description = "rn发布配置")
public class OpReConfigRnVo implements Serializable {

    @ApiModelProperty(value = "productRnId")
    private Long productRnId;

    @ApiModelProperty(value = "应用id")
    private String appId;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "rn名称")
    private String rnName;

    @ApiModelProperty(value = "rn适配原生类型 android  ios")
    private String rnType;

    private String rnVersion;

    @ApiModelProperty(value = "适配原生最小版本")
    private String versionMin;

    @ApiModelProperty(value = "发布更新内容")
    private String releaseUpdateContent;

    @ApiModelProperty(value = "多语言id")
    private Long langId;

    @ApiModelProperty(value = "测试分组集合")
    private List<String> prdGroups;

    @ApiModelProperty(value = "测试分组集合")
    private List<String> testGroups;

    @ApiModelProperty(value = "开始时间 1：立即 2：定时")
    private Integer startType;

    @ApiModelProperty(value = "开始时间的时区")
    private String startZone;

    @ApiModelProperty(value = "开始时间，格式 yyyy-MM-dd HH:mm:ss")
    private String startTime;

    @ApiModelProperty(value = "结束时间 1：永不过期  2：定时")
    private Integer endType;

    @ApiModelProperty(value = "结束时间的时区")
    private String endZone;

    @ApiModelProperty(value = "结束时间，格式 yyyy-MM-dd HH:mm:ss")
    private String endTime;
}
