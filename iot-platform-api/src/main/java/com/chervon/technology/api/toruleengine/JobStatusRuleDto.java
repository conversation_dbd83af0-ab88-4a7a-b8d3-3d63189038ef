package com.chervon.technology.api.toruleengine;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @className JobAddDto
 * @description 新增任务DTO
 * @date 2022/7/29 11:06
 */
@ApiModel("规则引擎升级任务DTO")
@Data
public class JobStatusRuleDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * jobId
     **/
    @ApiModelProperty("jobId")
    private String jobId;

    /**
     * 升级任务发布状态
     **/
    @ApiModelProperty("升级任务发布状态")
    private String status;

    /**
     * 设备arn
     **/
    @ApiModelProperty("设备arn")
    private String thingArn;
}
