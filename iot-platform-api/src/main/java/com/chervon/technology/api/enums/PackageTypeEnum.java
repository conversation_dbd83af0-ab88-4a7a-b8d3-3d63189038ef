package com.chervon.technology.api.enums;

import java.util.Arrays;

/**
 * 固件包类型枚举
 * <AUTHOR>
 * @date 20:20 2022/7/28
 **/
public enum PackageTypeEnum {

    FULL_PACKAGE(0),
    DELTA_PACKAGE(1);


    private Integer value;

    PackageTypeEnum(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public static PackageTypeEnum getByValue(Integer value) {
        if (0 == value) {
            return PackageTypeEnum.FULL_PACKAGE;
        } else {
            return PackageTypeEnum.DELTA_PACKAGE;
        }
    }

    public static PackageTypeEnum getByName(String name) {
        return Arrays.stream(values())
                .filter(x -> x.name() .equals(name))
                .findFirst()
                .orElse(null);
    }
}
