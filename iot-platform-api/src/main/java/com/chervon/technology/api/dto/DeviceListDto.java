package com.chervon.technology.api.dto;

import com.chervon.common.core.domain.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022-07-01
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeviceListDto extends PageRequest implements Serializable {

    /**
     * 需要搜索的产品信息
     */
    private String name;

    /**
     * 产品对应的pid
     */
    private Long pid;

    /**
     * 设备通讯方式1：蓝牙，2：wifi，3：4G，4：全部设备
     */
    private Integer deviceCommunicateMode;
}
