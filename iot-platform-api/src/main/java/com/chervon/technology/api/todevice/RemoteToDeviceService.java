package com.chervon.technology.api.todevice;

import com.chervon.technology.api.dto.DeviceIdDto;
import com.chervon.technology.api.dto.DeviceRegisterDto;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2023/4/23 14:56
 */
public interface RemoteToDeviceService {


    /**
     * 设备验签
     *
     * @param dto 设备Id密文+私钥
     * @return token
     */
    IotDeviceVerifyVo deviceVerify(IotDeviceVerifyDto dto);

    /**
     * 设备注册
     *
     * @param deviceReg 设备注册Dto
     * @return 注册结果
     */
    IotDeviceCertVo deviceRegister(DeviceRegisterDto deviceReg);

    /**
     * 检查设备是否已注册
     *
     * @param deviceIdDto 设备Id
     * @return 是否已注册
     */
    Boolean checkRegistered(DeviceIdDto deviceIdDto);

    /**
     * 设备同步时间
     *
     * @param deviceTimeDto 设备同步时间信息
     * @return 设备同步时间Vo
     */
    DeviceTimeVo timeService(DeviceTimeDto deviceTimeDto);

    /**
     * 获取设备当月使用流量情况
     *
     * @param deviceFlowDto iccidDto
     * @return 流量Vo
     */
    DeviceFlowVo flow(DeviceFlowDto deviceFlowDto);

    /**
     * R项目上传地图
     *
     * @param req  入参
     * @param file 地图文件
     */
    void uploadMap(DeviceMapUploadDto req, MultipartFile file) throws Exception;
}
