package com.chervon.technology.api.vo.map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/10/26 16:13
 */
@Data
@ApiModel(description = "区域数据")
public class DeviceAreaVo implements Serializable {

    @ApiModelProperty(value = "区域id，一个deviceId下唯一")
    private String areaId;

    @ApiModelProperty(value = "区域名称")
    private String name;

    @ApiModelProperty(value = "区域级别：primary、secondary")
    private String level;
}
