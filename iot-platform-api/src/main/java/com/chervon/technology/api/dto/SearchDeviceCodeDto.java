package com.chervon.technology.api.dto;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 20220505
 * 设备搜索相关
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SearchDeviceCodeDto extends PageRequest implements Serializable {

    /**
     * 设备Id
     */
    @ApiModelProperty("设备Id")
    private String deviceId;

    /**
     * 设备Sn值
     */
    @ApiModelProperty("设备Sn值")
    private String sn;

    /**
     * 设备moCode
     */
    @ApiModelProperty("设备moCode")
    private String moCode;

    /**
     * MES #
     */
    @ApiModelProperty("MES #")
    private String mes;

    /**
     * 设备itemCode
     */
    @ApiModelProperty("设备itemCode")
    private String itemCode;

    /**
     * sim卡 iccid
     */
    @ApiModelProperty("sim卡 iccid")
    private String iccid;

    /**
     * 设备生成开始日期
     */
    @ApiModelProperty("设备生成开始日期")
    private String productionStartDate;
    /**
     * 设备生成结束日期
     */
    @ApiModelProperty("设备生成结束日期")
    private String productionEndDate;

    /**
     * 状态：0废弃 1正常
     */
    @ApiModelProperty("状态：0废弃 1正常")
    private Integer status;

    /**
     * 产品SnCode
     */
    @ApiModelProperty("产品SnCode")
    private String productSnCode;
}
