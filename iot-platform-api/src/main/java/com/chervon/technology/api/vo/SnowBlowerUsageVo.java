package com.chervon.technology.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/10/31 11:01
 */
@Data
@ApiModel(description = "使用情况")
public class SnowBlowerUsageVo implements Serializable {
    /**
     * 上次使用日期时间戳：1699351149000
     */
    @ApiModelProperty(value = "上次使用日期时间戳")
    private long startTimeStamp;
    /**
     * 上次使用日期时间戳：1699351149000
     */
    @ApiModelProperty(value = "上次使用日期时间戳")
    private long timeStamp;
    /**
     * 使用时长1016，单位：秒
     */
    @ApiModelProperty(value = "使用时长，单位：秒")
    private Integer workingTime;
    /**
     * 上次耗电量1025，单位：wh
     */
    @ApiModelProperty(value = "上次耗电量，单位：wh")
    private Integer consumePower;
    /**
     * 历次平均耗电量，单位：wh
     */
    @ApiModelProperty(value = "历次平均耗电量，单位：wh")
    private Integer averagePower;
    /**
     * 二氧化碳减排量跟实际使用时长相关，为380mg/s，22.8g/min，1.4kg/h
     */
    @ApiModelProperty(value = "二氧化碳减排量，单位：g")
    private Integer carbonDioxideReduced;

}
