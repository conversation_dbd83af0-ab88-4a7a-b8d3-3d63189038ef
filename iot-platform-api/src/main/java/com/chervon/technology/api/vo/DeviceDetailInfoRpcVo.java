package com.chervon.technology.api.vo;

import com.chervon.technology.api.enums.DeviceOnlineStatusEnum;
import com.chervon.technology.api.enums.DeviceStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备详情实体
 * <AUTHOR>
 */
@Data
public class DeviceDetailInfoRpcVo implements Serializable {
    /**
     * 设备所属产品ID
     */
    @ApiModelProperty("设备所属产品ID")
    private Long productId;
    /**
     * 设备出厂时烧录的ID，唯一标识：设备类型+mes码
     */
    @ApiModelProperty("设备出厂时烧录的ID，唯一标识：设备类型+mes码")
    private String deviceId;
    /**
     * 设备SN
     */
    @ApiModelProperty("设备SN")
    private String sn;
    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;
    /**
     * 设备昵称
     */
    @ApiModelProperty("设备昵称")
    private String nickName;
    /**
     * 设备版本号
     */
    @ApiModelProperty("设备版本号")
    private String version;

    /**
     * 是否在线：0离线 1在线
     */
    @ApiModelProperty("是否在线：0离线 1在线")
    private DeviceOnlineStatusEnum isOnline;

    /**
     * 上次离线时间
     */
    @ApiModelProperty("上次离线时间")
    private LocalDateTime lastLogoutTime;

    /**
     * 设备状态：DISABLE 停用 NORMAL 正常
     */
    @ApiModelProperty("设备状态：DISABLE 停用 NORMAL 正常")
    private DeviceStatusEnum status;

    /**
     * 设备多码状态 0：废弃 1：启用
     */
    @ApiModelProperty("设备多码状态 0：废弃 1：启用")
    private Integer deviceCodeStatus;
    /**
     * 设备图片地址在S3中存储Key
     */
    @ApiModelProperty("设备图片地址在S3中存储Key")
    private String deviceIcon;
    /**
     * 设备类型，wifiAndBle：Wifi+BLE，4GAndBle：4G+BLE，ble：BLE，DOrT：D/T，wifi：WIFI，4G：4G，lan：LAN，notNetworked：不联网
     */
    @ApiModelProperty("通讯方式:wifi > 4G > BLE > DT> LAN >noNetworked")
    private String communicateMode;
    /**
     * 设备类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，gatewaySubDevice：网关子设备，notIotDevice：非Iot设备，oldIotDevice：老iot设备
     */
    @ApiModelProperty("设备类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，gatewaySubDevice：网关子设备，notIotDevice：非Iot设备，oldIotDevice：老iot设备")
    private String productType;

    @ApiModelProperty("rnBundleName")
    private String rnBundleName;
    /**
     * 总成零件序列号列表
     */
    @ApiModelProperty("总成零件序列号列表")
    private List<String> assemblySnList;

    @ApiModelProperty("配件数量")
    private Long accessoryQuantity;
}
