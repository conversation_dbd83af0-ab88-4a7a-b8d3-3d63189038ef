package com.chervon.configuration.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.configuration.entity.MultiLanguage;
import com.chervon.configuration.entity.MultiLanguageContent;
import com.chervon.configuration.service.MultiLanguageContentService;
import com.chervon.configuration.service.MultiLanguageService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/9 11:51
 */
@Component
@Slf4j
public class MultiLanguageTask {
    @Autowired
    private MultiLanguageService multiLanguageService;
    @Autowired
    private MultiLanguageContentService multiLanguageContentService;

    @XxlJob("redisLanguage")
    public void redisLanguage() {
        log.info("MultiLanguageTask#redisLanguage -> xxl-job init multiLanguage redis start...");
        List<MultiLanguageContent> contents = multiLanguageContentService.list(new LambdaQueryWrapper<MultiLanguageContent>()
                .isNotNull(MultiLanguageContent::getLangCode)
                .ne(MultiLanguageContent::getLangCode, "")
                .select(MultiLanguageContent::getLangCode, MultiLanguageContent::getLang, MultiLanguageContent::getContent));
        List<String> codes = contents.stream().map(MultiLanguageContent::getLangCode).distinct().collect(Collectors.toList());
        List<MultiLanguage> languages = new ArrayList<>();
        if (!codes.isEmpty()) {
            languages = multiLanguageService.list(new LambdaQueryWrapper<MultiLanguage>()
                    .in(MultiLanguage::getLangCode, codes)
                    .select(MultiLanguage::getSysCode, MultiLanguage::getLangCode));
        }
        Map<String, List<MultiLanguageContent>> langCodeGroup = contents.stream().collect(Collectors.groupingBy(MultiLanguageContent::getLangCode));

        // 多语言规划出两个大部分，页面元素，用于页面初始化时候调用；全部，包含页面元素和中间创建的
        // 页面部分逻辑，多语言code非数字
        languages.forEach(e -> {
            boolean f;
            try {
                Long.parseLong(e.getLangCode());
                f = true;
            } catch (Exception ex) {
                f = false;
            }
            List<MultiLanguageContent> contentList = langCodeGroup.get(e.getLangCode());
            if (!f) {
                // 非数字，页面元素
                // 按语言存入redis
                contentList.forEach(i -> RedisUtils.setCacheMapValue("multiLanguage:page:" + e.getSysCode() + ":" + i.getLang(), i.getLangCode(), i.getContent()));
            }
            contentList.forEach(i -> RedisUtils.setCacheMapValue(i.getLangCode(), i.getLang(), i.getContent()));
        });
        log.info("MultiLanguageTask#redisLanguage -> xxl-job init multiLanguage redis end...");
    }
}
