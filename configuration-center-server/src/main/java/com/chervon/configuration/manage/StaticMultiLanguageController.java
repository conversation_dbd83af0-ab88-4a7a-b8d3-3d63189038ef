package com.chervon.configuration.manage;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.configuration.api.exception.ConfigurationErrorCode;
import com.chervon.configuration.config.ExceptionMessageUtil;
import com.chervon.configuration.entity.MultiLanguageRead;
import com.chervon.configuration.req.StaticMultiLanguageExportDto;
import com.chervon.configuration.service.StaticMultiLanguageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/3/13 14:42
 * @desc 静态多语言管理
 */
@RestController
@RequestMapping("/static/language")
@AllArgsConstructor
@Api(value = "静态多语言接口", tags = {"静态多语言接口"})
@Slf4j
public class StaticMultiLanguageController {

    @Autowired
    private StaticMultiLanguageService appMultiLanguageService;

    @ApiOperation(value = "导入模板下载")
    @PostMapping("/template")
    public void template(HttpServletResponse response) throws IOException {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("language_import_template", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream(), MultiLanguageRead.class).autoCloseStream(Boolean.FALSE).sheet("sheet1")
                    .doWrite(new ArrayList<MultiLanguageRead>() {{
                        add(new MultiLanguageRead());
                    }});
        } catch (Exception e) {
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(R.fail("Download template failed ！")));
        }
    }

    @ApiOperation(value = "导入静态多语言")
    @PostMapping("/import")
    public void importMultiLanguage(@RequestParam(value = "file") MultipartFile file) {
        List<MultiLanguageRead> data;
        InputStream inputStream;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            throw ExceptionMessageUtil.getException(ConfigurationErrorCode.CONFIGURATION_IO_ERROR);
        }
        data = EasyExcel.read(inputStream, new AnalysisEventListener<MultiLanguageRead>() {
            @Override
            public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                int count = 0;
                Field[] fields = MultiLanguageRead.class.getDeclaredFields();
                for (Field field : fields) {
                    ExcelProperty fieldAnnotation = field.getAnnotation(ExcelProperty.class);
                    if (fieldAnnotation != null) {
                        ++count;
                        String headName = headMap.get(fieldAnnotation.index());
                        if (StringUtils.isEmpty(headName) || !headName.equals(fieldAnnotation.value()[0])) {
                            throw ExceptionMessageUtil.getException(ConfigurationErrorCode.MULTI_LANGUAGE_IMPORT_HEAD_ERROR);
                        }
                    }
                }
                // 判断用户导入表格的标题头是否完全符合模板
                if (count != headMap.size()) {
                    throw ExceptionMessageUtil.getException(ConfigurationErrorCode.MULTI_LANGUAGE_IMPORT_HEAD_ERROR);
                }
            }
            @Override
            public void invoke(MultiLanguageRead dealerNaRead, AnalysisContext analysisContext) {
            }
            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }
        }).head(MultiLanguageRead.class).sheet().doReadSync();

        if (CollectionUtils.isEmpty(data)) {
            throw ExceptionMessageUtil.getException(ConfigurationErrorCode.MULTI_LANGUAGE_EXCEL_EMPTY);
        }
        appMultiLanguageService.importMultiLanguage(data);
    }

    @ApiOperation(value = "导出语言包")
    @PostMapping("/export")
    public void export(@RequestBody StaticMultiLanguageExportDto req, HttpServletResponse response) throws IOException {
        boolean ok = appMultiLanguageService.exportMultiLanguage(response, req);
        if(!ok){
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(R.fail("Download failed !")));
        }
    }

    @ApiOperation(value = "按条件导出静态多语言，默认全量导出")
    @PostMapping("/export/xlsx")
    public void exportExcel(@RequestBody StaticMultiLanguageExportDto req, HttpServletResponse response) throws IOException {
        boolean ok = appMultiLanguageService.exportMultiLanguageExcel(response, req);
        if(!ok){
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(R.fail("Download failed !")));
        }
    }

}
