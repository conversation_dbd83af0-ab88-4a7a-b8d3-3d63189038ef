package com.chervon.configuration.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.configuration.entity.MultiLanguageContent;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/27 17:05
 */
public interface MultiLanguageContentService extends IService<MultiLanguageContent> {

    /**
     * 将页面元素多语言内容写入缓存
     * @param sysCode 系统code
     */
    void cachePageContent(String sysCode);
    /**
     * 根据语言id获取对应的内容
     * @param langId 语言id
     * @return 内容
     */
    String getByLangIdFromDb(Long langId);

    /**
     * 根据langId获取多语种key,valueJson结果字符串；
     * @param langId
     * @return json内容（多个语种的内容）
     */
    String selectJsonResultByLangId(Long langId);
    /**
     * 根据语言编码获取对应的内容
     * @param langCode 语言编码
     * @param lang 语言
     * @return 内容
     */
    String getByLangCode(String langCode, String lang);

    /**
     * 批量获取
     * @param langCodes 语言编码列表
     * @param lang 语言
     * @return 内容
     */
    Map<String, String> batchGetByLangCode(Collection<String> langCodes, String lang);
}
