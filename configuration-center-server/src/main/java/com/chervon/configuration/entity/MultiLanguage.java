package com.chervon.configuration.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/7/10 20:29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("multi_language")
public class MultiLanguage extends BaseDo {

    private String langCode;

    private String sysCode;

    private String page;

    private String func;

    private String elementCode;

    private String textCode;

    private String remark;

    private Integer isRich;

}
