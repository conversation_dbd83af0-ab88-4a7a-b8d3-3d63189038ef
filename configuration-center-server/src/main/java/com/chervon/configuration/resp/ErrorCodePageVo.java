package com.chervon.configuration.resp;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/7/10 20:12
 */
@Data
@ApiModel(description = "错误码分页查询出参对象")
public class ErrorCodePageVo {

    @ApiModelProperty(value = "错误码id")
    private Long errorCodeId;

    @ApiModelProperty(value = "错误码系统code")
    private String sysCode;

    @ApiModelProperty(value = "错误码模块code")
    private String modelCode;

    @ApiModelProperty(value = "错误码类型code")
    private String typeCode;

    @ApiModelProperty(value = "错误码")
    private String errorCode;

    @ApiModelProperty(value = "完整错误码")
    private String completeErrorCode;

    @ApiModelProperty(value = "错误码原始信息")
    private String originErrorMessage;

    @ApiModelProperty(value = "错误码多语言id")
    private Long langId;

    @ApiModelProperty(value = "错误码默认信息")
    private String errorMessage;

    @ApiModelProperty(value = "错误码备注")
    private String remark;

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("更新者")
    private String updateBy;

    @ApiModelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;
}
