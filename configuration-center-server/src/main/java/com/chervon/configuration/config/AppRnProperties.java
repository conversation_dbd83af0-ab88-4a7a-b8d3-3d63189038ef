package com.chervon.configuration.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2024/3/13 14:20
 * @desc 描述
 */
@Data
@Component
@ConfigurationProperties(prefix = "app-rn.static-lang.template")
@RefreshScope
public class AppRnProperties {

    private String appMultiFileName;

    private String rnMultiFileName;

}
