package com.chervon.iot.app.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-09-16 09:53
 **/
@Data
public class UserSettingBo implements Serializable {
    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;
    /**
     * 用户Id
     */
    @ApiModelProperty("用户Id")
    private Long userId;
    /**
     * 智能开关是否开启：1开启，0不开启
     */
    @ApiModelProperty("智能开关是否开启：1开启，0不开启")
    private Integer smartSwitch;
    /**
     * 推送相关token
     */
    @ApiModelProperty("推送相关token")
    private String pushToken;
    /**
     * 系统消息开关是否开启：1开启，0不开启
     */
    @ApiModelProperty("系统消息开关是否开启：1开启，0不开启")
    private Integer systemMessageSwitch;
    /**
     * 设备消息开关是否开启：1开启，0不开启
     */
    @ApiModelProperty("设备消息开关是否开启：1开启，0不开启")
    private Integer deviceMessageSwitch;
    /**
     * 营销消息开关是否开启：1开启，0不开启
     */
    @ApiModelProperty("营销消息开关是否开启：1开启，0不开启")
    private Integer marketingMessageSwitch;
    /**
     * 用户的语言环境
     */
    @ApiModelProperty("用户的语言环境")
    private String language;
}
