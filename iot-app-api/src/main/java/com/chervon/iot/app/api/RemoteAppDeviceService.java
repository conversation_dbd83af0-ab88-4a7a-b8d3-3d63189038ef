package com.chervon.iot.app.api;

import com.chervon.common.core.domain.PageResult;
import com.chervon.iot.app.api.dto.AppUserDeviceDTO;
import com.chervon.iot.app.api.dto.BoundDeviceQueryDto;
import com.chervon.iot.app.api.vo.BoundDeviceRpcVo;
import com.chervon.iot.app.api.vo.TargetUserVo;
import com.chervon.iot.app.api.vo.UserIdDeviceIdVo;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022-07-18 15:41
 **/
public interface RemoteAppDeviceService {

    /**
     * 通过设备Id列表查询设备绑定用户Id列表集合，rpc接口
     *
     * @param deviceIds 设备Id
     * @return 设备Id与绑定用户Id列表的集合
     */
    Map<String, List<Long>> getBoundUserIdsMap(List<String> deviceIds);

    /**
     * 通过设备Id获取绑定用户Id列表
     *
     * @param deviceId 设备Id
     * @return 绑定用户Id列表
     */
    List<Long> getBoundUserId(String deviceId);

    /**
     * 通过设备Id获取目标用户列表
     * @param deviceId
     * @return
     */
    List<TargetUserVo> getDeviceAlarmUsers(String deviceId);

    /**
     * (废弃)通过用户ID分页获取用户设备绑定列表
     *
     * @param boundDeviceQueryDto 用户ID分页搜索Dto
     * @return 绑定设备分页结果
     */
    PageResult<BoundDeviceRpcVo> pageAppUserDeviceByUserId(BoundDeviceQueryDto boundDeviceQueryDto);

    /**
     * 根据给定userId和设备id集合查询userId集合
     *
     * @param userId    给定的用户id片段
     * @param deviceIds 设备id集合
     * @return 用户id分页
     */
    List<Long> listUserIdByUserIdAndDeviceIds(String userId, List<String> deviceIds);

    /**
     * 根据userIds查询设备id集合
     *
     * @param userIds 用户id集合
     * @return 设备id集合
     */
    List<UserIdDeviceIdVo> listDeviceIdByUserIds(@NotNull List<Long> userIds);

    /**
     * 根据用户id模糊查询设备id集合
     *
     * @param userId 用户id片段
     * @return 设备id集合
     */
    List<String> listDeviceIdLikeUserId(String userId);

    /**
     * 根据设备id查询用户数据，构建map
     *
     * @param deviceIds 设备id集合
     * @return 设备id和用户集合映射
     */
    Map<String, List<Long>> mapDeviceIdUserIds(List<String> deviceIds);

    /**
     * 根据设备Id查询用户和设备绑定关系信息
     * @param deviceId
     * @param userId
     * @return
     */
    List<AppUserDeviceDTO> getAppUserDeviceInfoByDeviceId(String deviceId,Long userId);

    /**
     * 根据设备Id查询用户和设备绑定关系信息
     * @param userId 用户id
     * @param deviceIds 设备计划
     * @param sourceCode
     * @return
     */
    List<Long> listUserIdByUserDeviceSource(String userId, List<String> deviceIds, Integer sourceCode);
}
