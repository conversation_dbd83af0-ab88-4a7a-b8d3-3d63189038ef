package com.chervon.iot.app.api.exception;

import com.chervon.common.core.exception.ErrorCodeI;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 系统：app前台服务 102
 *
 * <AUTHOR>
 * @date 2022/7/15 14:50
 */
@Getter
@AllArgsConstructor
public enum AppErrorCode implements ErrorCodeI {
    /**
     * 用户模块  001
     * 产品模块   002
     * 设备模块  003
     * 升级模块  004
     * 售后模块  005
     */

    /**
     * 用户模块 102001
     */
    APP_EMAIL_BLANK("1020012001", "user email blank", "user email blank"),

    APP_EMAIL_VERIFY_CODE_IS_NULL("1020012002", "verify code is not be null", "verify code is not be null"),

    APP_REGISTER_ERROR("1020012003", "user register error", "user register error"),

    APP_TOKEN_INVALID("1020012004", "user token invalid", "user token invalid"),

    APP_TOKEN_NOT_FOUND("1020012005", "user token not found", "user token not found"),

    APP_USER_ID_BLANK("1020012006", "user id blank", "user id blank"),

    APP_SET_SMART_FAIL("1020012007", "user set smart fail", "user set smart fail"),

    APP_REGISTER_CHECK_ERROR("1020012008", "user register check error", "user register check error"),

    APP_LOGIN_CHECK_ERROR("1020012009", "user login check error", "user login check error"),

    USER_FIRST_NAME_TOO_LONG("1020012010", "user first name too long", "user first name too long, more than 36"),
    USER_LAST_NAME_TOO_LONG("1020012011", "user last name too long", "user last name too long"),
    USER_POST_CODE_TOO_LONG("1020012012", "user post code too long", "user post code too long, more than 10"),
    USER_COUNTRY_TOO_LONG("1020012013", "user country too long", "user country too long"),
    USER_ADDRESS_TOO_LONG("1020012014", "user address too long", "user address too long, more than 100"),
    EMAIL_ERROR("1020012015", "Please enter the correct email address", "email:[%s] , Please enter the correct email address"),
    APP_USER_IS_NOT_LOGIN("1020012016", "unLogin", "unLogin"),
    APP_GET_USER_FAIL("1020012017", "get user fail", "get user fail"),
    APP_SET_USER_STATE_FAIL("1020012018", "set app presence state fail", "set app presence state fail"),
    APP_USER_SETTING_IS_NULL("1020012019", "user setting info is null", "user: [%s] setting info is null"),
    APP_EMAIL_VERIFY_CODE_ERROR("1020012020", "user email verify code error", "user email verify code error"),
    APP_REGISTER_SF_PRODUCT_NOT_EXISTS("1020012021", "product not exists in salesforce", "product not exists in salesforce"),
    APP_VERSION_LOW_ERROR("1020012022", "Please update EGO Connect to latest version.", "Please update EGO Connect to latest version."),


    /**
     * 设备模块 102003
     */
    APP_DEVICE_NOT_EXIST("1020032001", "device not exist", "device not exist, deviceId|SN -> [%s]"),

    APP_USER_DEVICE_NOT_BOUND("1020032002", "user device not bound", "user device not bound, userId: [%s], deviceId: [%s]"),

    APP_USER_DEVICE_ALREADY_BOUND("1020032003", "user device already bound", "user[%s] device[%s] already bound"),

    APP_DEVICE_INFO_NOT_REGISTERED("1020032004", "device info not registered", "device[%s] info not registered"),
    APP_DEVICE_INFO_ALREADY_REGISTERED("1020032005", "device info already registered", "device[%s] info already registered"),
    APP_DEVICE_T_RESET_FAIL("1020032006", "device T reset fail", "device T reset fail"),

    APP_DEVICE_BIND_LOCKED("1020032007", "device bind locked", "device bind locked"),
    APP_DEVICE_ALREADY_BOUND_OTHER_APP("1020032008", "device already bound other app", "device already bound other app"),
    APP_DEVICE_ROBOT_SEND_EMAIL_CODE_ERROR("1020032009", "robot send email code error", "robot send email code error"),
    APP_DEVICE_ROBOT_EMAIL_CODE_EXPIRED("1020032010", "robot email code expired", "robot email code expired"),
    APP_DEVICE_ROBOT_EMAIL_CODE_ERROR("1020032011", "robot email code error", "robot email certificate is null"),
    APP_DEVICE_SN_NOT_FOUND("1020032012", "device sn is not found", "device[%s] info is not found"),
    APP_DEVICE_ID_BLANK("1020032013", "device id is blank", "device id is blank"),
    APP_DEVICE_ROBOT_EMAIL_IS_EMPTY("1020032014", "robot email is empty", "robot email is empty"),
    APP_DEVICE_INFO_REGISTER_ERROR("1020032015", "device info register error", "device info register to SF error, sync is not enabled"),
    APP_DEVICE_INFO_REGISTER_ERROR1("1020032016", "device info register error", "device info register to SF error, unable to get sf_user_id, userId: %s"),
    APP_DEVICE_INFO_NOT_REGISTERED1("1020032017", "device info not registered", "device[%s] has not the registration information"),
    APP_DEVICE_T_RESET_FAIL1("1020032018", "device T reset fail", "device[%s] info not exists"),
    APP_DEVICE_T_RESET_FAIL2("1020032019", "device T reset fail", "user[%s] not bound device[%s]"),
    APP_DEVICE_ROBOT_EMAIL_CODE_NOT_MATCH("1020032020", "robot email code error", "robot email code with redis cache code does not match"),

    APP_SN07_PARAM_ERROR("1020032021", "param error","param error"),
    APP_SN07_NOT_FOUND("1020032022", "not found","not found"),
    APP_PARAM_NOT_PROVIDED("1020032023", "param not provided","param not provided"),

    APP_DEVICE_INFO_REGISTER_FAILED("1020032024", "Invalid Serial Number", "Invalid Serial Number, device: [%s]"),
    TECHNOLOGY_DEVICE_CODE_SN_ERROR("1020032025", "device code sn error", "device code sn: [%s] check error"),
    APP_DEVICE_DEACTIVATED("1020032027", "The device has been deactivated", "The device has been deactivated"),

    DEVICE_INFO_PURCHASETIME_EXCEED("1020032026", "Purchase date cannot exceed the current date", "Purchase date cannot exceed the current date, datetime: [%s] "),
    DEVICE_INFO_PURCHASETIME_SN_ERROR("1020032028", "Invalid purchase date or serial number", "Invalid purchase date or serial number"),
    DEVICE_INFO_SN_ERROR("1020032029", "This serial number belongs to another device", "This serial number[%s] belongs to another device"),

    /**
     * 售后模块  005
     */
    APP_DEALER_COLLECTION_ID_NULL("1020052001", "dealer collection id is null", "dealer collection id is null"),
    APP_DEALER_ALREADY_COLLECTION("1020052002", "dealer already collection", "dealer already collection, dealerId: [%s]"),
    APP_DEALER_LAT_ILLEGAL("1020052003", "dealer lat is illegal", "dealer lat[%s] is illegal"),
    APP_DEALER_LNG_ILLEGAL("1020052004", "dealer lng is illegal", "dealer lng[%s] is illegal"),
    APP_DEALER_NOT_COLLECTION("1020052005", "dealer not collection", "dealer[%s] not collection"),
    APP_HELP_FAQ_PRAISING("1020052006", "help faq is praising", "help faq is praising"),

    /**
     * 分享模块  006
     */
    APP_SHARE_NOT_EXIST("1020062001", "share record not exist", "share record not exist"),
    APP_SHARE_STOPPED("1020062002", "This device has been stopped sharing", "This device has been stopped sharing"),
    APP_SHARE_STATUS_ERROR("1020062003", "This device has been accepted or expired", "This device has been accepted or expired"),
    APP_SHARE_AUTH_ERROR("1*********", "You are not authorized to share this device", "You are not authorized to share this device"),
    APP_SHARE_EMAIL_ERROR("**********", "This email is not registered", "This email[%s] is not registered"),
    APP_SHARE_PRODUCT_ERROR("**********", "This device cannot be shared", "This device cannot be shared"),
    APP_SHARE_REMOVE_AUTH_ERROR("**********", "You are not authorized to remove this device", "You are not authorized to remove this device"),
    APP_SHARE_EXIST_ERROR("**********", "This account has already been shared", "This account has already been shared"),
    APP_SHARE_MASTER_EXIST("**********", "1.This device belongs to %s, please request sharing from that account to use this device", "This device belongs to %s, please request sharing from that account to use this device"),

    /**
     * Explore模块  007
     */
    DEFAULT_PROMOTION_NOT_SET("**********", "Promotion product not set", "Promotion promotion not set"),
    HAVE_FUN_LIST_ERROR("**********", "have fun list is null", "have fun list is null"),
    ;

    private final String code;

    private final String defaultMessage;

    private final String errorMessage;

}
