package com.chervon.iot.app.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 为iot-platform DeviceService 获取设备列表接口 远程调用的Vo
 *
 * <AUTHOR>
 * @since 2022-08-17 15:00
 **/
@Data
public class BoundDeviceRpcVo implements Serializable {
    /**
     * 设备出厂时烧录的ID，唯一标识：设备类型+mes码
     */
    @ApiModelProperty("设备出厂时烧录的ID，唯一标识：设备类型+mes码")
    private String deviceId;
    /**
     * 被绑定用户Id列表
     */
    @ApiModelProperty("被绑定用户Id列表")
    private List<Long> userIdList;
}
