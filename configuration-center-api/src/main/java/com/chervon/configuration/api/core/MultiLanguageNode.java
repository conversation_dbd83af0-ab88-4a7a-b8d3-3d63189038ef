package com.chervon.configuration.api.core;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/7/10 20:41
 */
@Data
@ApiModel(description = "多语言项")
@AllArgsConstructor
@NoArgsConstructor
public class MultiLanguageNode implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "多语言种类code")
    private String langClassCode;

    @ApiModelProperty(value = "多语言内容")
    private String content;
}
