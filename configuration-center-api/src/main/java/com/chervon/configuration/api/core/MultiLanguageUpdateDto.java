package com.chervon.configuration.api.core;

import com.chervon.configuration.api.core.MultiLanguageCreateDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/7/10 20:43
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "多语言修改对象")
public class MultiLanguageUpdateDto extends MultiLanguageCreateDto {

    @ApiModelProperty(value = "多语言id")
    private Long multiLanguageId;
}
