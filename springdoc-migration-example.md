# SpringDoc OpenAPI迁移实施示例

本文档提供从Springfox Swagger迁移到SpringDoc OpenAPI的详细步骤和代码示例，用于解决Spring Boot 2.6.8与Swagger的兼容性问题。

## 1. 依赖变更

### 移除Springfox相关依赖

```xml
<!-- 移除以下依赖 -->
<dependency>
    <groupId>io.springfox</groupId>
    <artifactId>springfox-boot-starter</artifactId>
    <version>3.0.0</version>
</dependency>

<!-- 如果有使用knife4j，保留或更新到兼容SpringDoc的版本 -->
```

### 添加SpringDoc依赖

```xml
<!-- 添加SpringDoc依赖 -->
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-ui</artifactId>
    <version>1.6.9</version>
</dependency>

<!-- 如果使用WebFlux -->
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-webflux-ui</artifactId>
    <version>1.6.9</version>
</dependency>

<!-- 如果需要与security集成 -->
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-security</artifactId>
    <version>1.6.9</version>
</dependency>
```

## 2. 配置迁移

### 替换Swagger配置类

将当前的`SpringFoxSwaggerConfig.java`替换为:

```java
package com.chervon.swagger.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SpringdocOpenApiConfig {

    @Value("${swagger.enable:false}")
    private boolean enable;

    @Bean
    public OpenAPI apiInfo() {
        return new OpenAPI()
                .info(new Info()
                        .title("Swagger Chervon IoT App Restful API")
                        .description("swagger chervon iot app restful api")
                        .version("1.0")
                        .contact(new Contact()
                                .name("IoT技术平台")
                                .email("****@chervoncloud.com")
                                .url(""))
                );
    }

    @Bean
    public GroupedOpenApi publicApi() {
        return GroupedOpenApi.builder()
                .group("SwaggerGroupOneAPI")
                .pathsToMatch("/**")
                .packagesToScan("com.chervon")
                .addOpenApiCustomiser(openApi -> {
                    // 可以在这里添加自定义配置
                })
                .build();
    }
}
```

### 应用配置

在`application.yml`中添加SpringDoc配置:

```yaml
# SpringDoc配置
springdoc:
  api-docs:
    enabled: ${swagger.enable:false}  # 复用原有的开关配置
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    disable-swagger-default-url: true
    display-request-duration: true
```

## 3. 注解迁移

以下是主要注解的迁移对照:

| Springfox (swagger2) | SpringDoc (OpenAPI 3) |
|---------------------|----------------------|
| @Api                | @Tag                 |
| @ApiOperation       | @Operation           |
| @ApiParam          | @Parameter           |
| @ApiModel          | @Schema              |
| @ApiModelProperty  | @Schema              |
| @ApiResponse       | @ApiResponse         |
| @ApiIgnore         | @Parameter(hidden=true) 或 @Hidden |

### 示例迁移

#### 控制器类

**从Springfox:**
```java
@Api(tags = "用户管理")
@RestController
@RequestMapping("/api/users")
public class UserController {

    @ApiOperation(value = "获取用户列表", notes = "分页获取所有用户")
    @GetMapping
    public ResponseEntity<Page<UserDTO>> getUsers(
            @ApiParam(value = "页码", defaultValue = "0") @RequestParam(defaultValue = "0") int page,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") int size) {
        // 实现代码
    }
}
```

**迁移到SpringDoc:**
```java
@Tag(name = "用户管理")
@RestController
@RequestMapping("/api/users")
public class UserController {

    @Operation(
        summary = "获取用户列表", 
        description = "分页获取所有用户"
    )
    @GetMapping
    public ResponseEntity<Page<UserDTO>> getUsers(
            @Parameter(description = "页码", example = "0") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") int size) {
        // 实现代码
    }
}
```

#### 模型类

**从Springfox:**
```java
@ApiModel(description = "用户DTO")
public class UserDTO {
    
    @ApiModelProperty(value = "用户ID", example = "1", position = 1)
    private Long id;
    
    @ApiModelProperty(value = "用户名称", example = "张三", position = 2)
    private String name;
}
```

**迁移到SpringDoc:**
```java
@Schema(description = "用户DTO")
public class UserDTO {
    
    @Schema(description = "用户ID", example = "1")
    private Long id;
    
    @Schema(description = "用户名称", example = "张三")
    private String name;
}
```

## 4. 自定义配置

### 安全配置

如果有安全配置，例如OAuth2:

```java
@Bean
public OpenAPI customOpenAPI() {
    return new OpenAPI()
            .info(new Info().title("API 文档").version("1.0"))
            .components(new Components()
                    .addSecuritySchemes("bearer-jwt", 
                            new SecurityScheme()
                                    .type(SecurityScheme.Type.HTTP)
                                    .scheme("bearer")
                                    .bearerFormat("JWT")
                                    .in(SecurityScheme.In.HEADER)
                                    .name("Authorization")));
}
```

### 忽略特定端点

```java
@Bean
public GroupedOpenApi publicApi() {
    return GroupedOpenApi.builder()
            .group("public-api")
            .pathsToMatch("/**")
            .pathsToExclude("/actuator/**", "/error")
            .build();
}
```

## 5. 迁移策略

### 渐进式迁移

1. **创建概念验证项目**:
   - 使用一个小型模块进行测试
   - 验证API文档生成和功能

2. **并行运行**:
   - 保留Springfox配置
   - 添加SpringDoc配置
   - 配置不同的URL路径
   - 比较两种文档的差异

3. **全面迁移**:
   - 迁移所有注解
   - 删除Springfox相关代码和依赖
   - 全面测试新API文档

### 自动化工具

考虑使用IDE的批量替换功能或编写简单脚本来帮助转换大量的注解。

## 6. 测试验证

迁移后务必测试以下功能:

1. API文档界面能否正常访问和显示
2. 所有API端点是否都显示在文档中
3. API描述、参数和响应模型是否正确
4. API测试功能是否正常工作
5. 授权/认证机制是否正确集成

## 结论

SpringDoc OpenAPI提供了与Spring Boot 2.6.x的完全兼容性，并且不依赖于actuator，能够解决安全风险问题。虽然迁移需要一定的工作量，但长期来看是值得的投资。
