package com.chervon.common.web.header;

import org.springframework.boot.WebApplicationType;

import javax.servlet.*;
import java.io.IOException;

/**
 *
 * @date 2022/9/9
 */
public class MvcHeaderInitFilter implements Filter {
    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        try {
            //MVC项目直接通过RequestContextHolder获取信息
            if (HeaderContextHolder.getCtx() == null && HeaderContext.deduceFromClasspath() == WebApplicationType.SERVLET) {
                HeaderContextHolder.setCtx(HeaderContext.of());
            }
            filterChain.doFilter(servletRequest, servletResponse);
        } finally {
            HeaderContextHolder.remove();
        }
    }
}
