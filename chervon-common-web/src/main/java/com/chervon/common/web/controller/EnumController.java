package com.chervon.common.web.controller;

import com.chervon.common.core.domain.R;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.JsonUtils;
//import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.common.web.annotation.EnumsScan;
import com.chervon.common.web.entity.EnumDto;
import com.chervon.common.web.util.ClassScanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.lang.reflect.Method;
import java.util.*;

/**
 * @Description: 通用枚举控制器
 * @Date: 2023/05/11
 */
@Slf4j
@RestController
@RequestMapping(value = "/enum")
public class EnumController {

    private static final String BASE_PACKAGE = "com.chervon.**.enums";
    private static final String TYPE_ENUM_NAME = "com.chervon.beans.enums.TypeEnum";
    private static final String GET_TYPE = "getType";
    private static final String GET_CODE = "getCode";
    private static final String GET_DESC = "getDesc";
    private static final String DEFAULT_LANGUAGE = "en";
    private static final String REDIS_KEY_PREFIX = "commonEnumKey";
    private static final String VERTICAL_LINE = "|";

    private static final Map<String, List<EnumDto>> ENUM_MAP = new HashMap<>();

    //@ApiOperation("统一接口 枚举")
    @GetMapping("/list/{language}/{key}")
    public R<List<EnumDto>> list(@PathVariable(value = "language", required = false) String language, @PathVariable(value = "key") String key) {
        Assert.hasText(key, ErrorCode.PARAMETER_NOT_PROVIDED, "key");

//        Map<String, List<EnumDto>> map = RedisUtils.getCacheMap(REDIS_KEY_PREFIX + VERTICAL_LINE + key);
        if (CollectionUtils.isEmpty(ENUM_MAP)) {
            this.initEnums();
            //map = RedisUtils.getCacheMap(REDIS_KEY_PREFIX + VERTICAL_LINE + key);
        }

        if (CollectionUtils.isEmpty(ENUM_MAP)) {
            return R.ok();
        }

        if (StringUtils.isEmpty(language)) {
            language = DEFAULT_LANGUAGE;
        }

        String mapKey = language + VERTICAL_LINE + key;
        List<EnumDto> list = ENUM_MAP.get(mapKey);
        log.info("list->{}", JsonUtils.toJson(list));
        return R.ok(list);
    }

    /**
     * 初始化枚举数据
     */
    private void initEnums() {
        try {
            ENUM_MAP.clear();
            List<Class<?>> classList = ClassScanUtils.doScan(BASE_PACKAGE);
            if (CollectionUtils.isEmpty(classList)) {
                return;
            }

            for (Class<?> clazz : classList) {
                EnumsScan enumsScan = clazz.getAnnotation(EnumsScan.class);
                if (!clazz.isEnum() || Objects.isNull(enumsScan) || StringUtils.isEmpty(enumsScan.key())) {
                    continue;
                }

                Class<?>[] interfaces = clazz.getInterfaces();
                Class<?> typeEnum = Arrays.stream(interfaces).filter(inter -> inter.getName().equals(TYPE_ENUM_NAME)).findFirst().orElse(null);
                if (Objects.isNull(typeEnum)) {
                    return;
                }

                Object[] objects = clazz.getEnumConstants();
                Method getType = clazz.getMethod(GET_TYPE);
                Method getCode= clazz.getMethod(GET_CODE);
                Method getDesc = clazz.getMethod(GET_DESC);

                List<EnumDto> enumDtoList = new ArrayList<>(objects.length);
                for (Object obj : objects) {
                    EnumDto enumDto = new EnumDto();
                    enumDto.setType((Integer) getType.invoke(obj));
                    enumDto.setCode((String) getCode.invoke(obj));
                    enumDto.setDesc((String) getDesc.invoke(obj));
                    enumDtoList.add(enumDto);
                }

                //map  key: 语言标记|注解key（类的限定名） value: list value
                String mapKey = DEFAULT_LANGUAGE + VERTICAL_LINE + enumsScan.key();

                //key： 语言标记_枚举类的key  value：枚举类对应的值
                //Map<String, Object> enumMap = new HashMap<>();
                ENUM_MAP.put(mapKey, enumDtoList);

//                if (!CollectionUtils.isEmpty(enumMap)) {
//                    RedisUtils.setCacheMap(REDIS_KEY_PREFIX + VERTICAL_LINE + enumsScan.key(), enumMap);
//                }
            }
        } catch (Exception e) {
            log.error("initEnums init failed.", e);
        }
    }

}
