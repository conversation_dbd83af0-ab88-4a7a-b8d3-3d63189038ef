# Spring Boot Actuator依赖删除评估

## 当前使用情况

1. **依赖引用**：
   - 所有主要模块（user-center、message-center、iot-platform等）都引入了`spring-boot-starter-actuator`
   - Dubbo相关模块引入了`dubbo-spring-boot-actuator`和`dubbo-spring-boot-actuator-compatible`

2. **代码使用**：
   - 只在`chervon-common/chervon-common-swagger/src/main/java/com/chervon/swagger/config/SpringFoxSwaggerConfig.java`中找到了实际使用
   - 该使用是为了解决Spring Boot 2.6.x与Swagger 3.0.0的兼容性问题

3. **配置使用**：
   - 没有发现任何application.yml或properties文件中对actuator端点的配置
   - 没有发现任何自定义的Endpoint实现

## 删除影响评估

1. **安全提升**：
   - 删除后可消除由于暴露actuator端点带来的潜在安全风险

2. **功能影响**：
   - **监控功能丧失**：无法使用actuator提供的健康检查、指标监控等功能
   - **Swagger配置问题**：需修改SpringFoxSwaggerConfig.java以不使用actuator相关类
   - **应用启动**：移除后不会影响核心业务功能

3. **修改难度**：
   - **中等**：需要修改多个模块的pom.xml文件
   - **额外修改**：需要为Swagger提供替代配置

## 建议操作方案

1. **分阶段删除**：
   - 先修改SpringFoxSwaggerConfig.java，提供不依赖actuator的替代实现
   - 然后逐步从各模块pom.xml中移除actuator依赖

2. **替代Swagger配置**：
   - 创建不依赖actuator的Swagger配置类
   - 或升级到更新版本的Swagger库，其已解决与Spring Boot 2.6.x的兼容性问题

3. **测试策略**：
   - 先在非生产环境测试修改后的Swagger功能
   - 确保所有API文档正常工作后再完全移除依赖

## 总结
删除actuator依赖是可行的，主要影响在于Swagger配置需要调整。建议先解决这一依赖问题，然后再完全移除actuator相关依赖。这样可以消除安全风险，同时确保系统功能正常。
