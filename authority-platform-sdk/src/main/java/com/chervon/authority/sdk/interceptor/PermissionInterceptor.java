package com.chervon.authority.sdk.interceptor;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.lang.Validator;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.chervon.authority.api.exception.AuthorityErrorCode;
import com.chervon.authority.sdk.config.ExceptionMessageUtil;
import com.chervon.authority.sdk.config.PermissionCheckProperties;
import com.chervon.common.core.domain.LoginSysUser;
import com.chervon.common.core.exception.base.BaseException;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.redis.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/20 15:55
 */
@Slf4j
public class PermissionInterceptor implements HandlerInterceptor {

    private static final String PERMISSION_PATH = "/authority/center/resource/own/request/list";

    private final PermissionCheckProperties permissionCheckProperties;

    public PermissionInterceptor(@NotNull PermissionCheckProperties permissionCheckProperties) {
        this.permissionCheckProperties = permissionCheckProperties;
    }

    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) {
        // 如果enable为false，则跳过拦截器
        if (!permissionCheckProperties.isEnable()) {
            return true;
        }
        String url = request.getRequestURI();
        if (log.isDebugEnabled()) {
            log.debug("PermissionInterceptor url:{}", url);
        }
        if (permissionCheckProperties.getExcludeUrls() != null) {
            List<String> collect = permissionCheckProperties.getExcludeUrls()
                    .stream()
                    .map(e -> StringUtils.trim(String.valueOf(e), "/"))
                    .collect(Collectors.toList());
            if (collect.contains(StringUtils.trim(url, "/"))) {
                return true;
            }
        }
        LoginSysUser loginSysUser;
        try {
            // 超管角色放过
            loginSysUser = (LoginSysUser) StpUtil.getSession().get(StpUtil.getLoginIdAsString());
            if (loginSysUser.getUserRoles() != null && loginSysUser.getUserRoles().contains("Admin")) {
                return true;
            }
        } catch (Exception e) {
            throw ExceptionMessageUtil.getException(AuthorityErrorCode.AUTHORITY_GET_USERINFO_ERROR);
        }
        String appId = permissionCheckProperties.getAppId();
        if (StringUtils.isBlank(appId)) {
            throw ExceptionMessageUtil.getException(AuthorityErrorCode.AUTHORITY_CHECK_APPID_IS_BLANK, appId);
        }
        if (!Validator.isUrl(permissionCheckProperties.getPermissionCheckUrl())) {
            throw ExceptionMessageUtil.getException(AuthorityErrorCode.AUTHORITY_CHECK_URL_IS_INVALID, permissionCheckProperties.getPermissionCheckUrl());
        }

        String key = "permissions:" + appId + ":" + StpUtil.getTokenValue();
        List<String> permissions = RedisUtils.getCacheList(key);
        if (CollectionUtils.isEmpty(permissions)) {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/json; charset=UTF-8"));
            headers.add("AppId", appId);
            headers.add("EmployeeNumber", loginSysUser.getEmployeeNumber());
            headers.add("Accept", MediaType.APPLICATION_JSON.toString());
            HttpEntity<?> entity = new HttpEntity<>(headers);
            RestTemplate restTemplate = new RestTemplate();
            JSONObject res;
            try {
                String permissionUrl = permissionCheckProperties.getPermissionCheckUrl().endsWith("/") ?
                        permissionCheckProperties.getPermissionCheckUrl().substring(0, permissionCheckProperties.getPermissionCheckUrl().length() - 1) + PERMISSION_PATH :
                        permissionCheckProperties.getPermissionCheckUrl() + PERMISSION_PATH;
                res = restTemplate.postForObject(permissionUrl, entity, JSONObject.class);
            } catch (Exception e) {
                log.error(e.getMessage());
                throw ExceptionMessageUtil.getException(AuthorityErrorCode.NO_PERMISSION);
            }
            if (res == null || !StringUtils.equals(res.getString("responseCode"), "200")) {
                throw ExceptionMessageUtil.getException(AuthorityErrorCode.NO_PERMISSION);
            }
            JSONArray entry = res.getJSONArray("entry");
            if (entry == null) {
                permissions = new ArrayList<>();
            } else {
                permissions = entry.stream().filter(Objects::nonNull).map(e -> StringUtils.trim(String.valueOf(e), "/")).collect(Collectors.toList());
            }
            RedisUtils.setCacheList(key, permissions);
            RedisUtils.expire(key, permissionCheckProperties.getPermissionKeyTtl() == null ? Duration.ofMinutes(5) : permissionCheckProperties.getPermissionKeyTtl());
        }
        if (!permissions.contains(StringUtils.trim(url, "/"))) {
            throw ExceptionMessageUtil.getException(AuthorityErrorCode.NO_PERMISSION);
        }
        return true;
    }
}
