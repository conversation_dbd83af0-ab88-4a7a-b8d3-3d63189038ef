package com.chervon.tracking.platform.controller;

import com.chervon.tracking.platform.dto.TrackingEleInfoDto;
import com.chervon.tracking.platform.dto.QueryEleInfoPageDto;
import com.chervon.tracking.platform.entity.*;
import com.chervon.tracking.platform.service.ITrackingEleInfoService;
import com.chervon.tracking.platform.vo.TrackingEleInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/12
 * @dec 描述 坑位信息 Controller
 */
@RestController
@RequestMapping("/ele")
@Slf4j
public class EleInfoController {

    private final ITrackingEleInfoService trackingEleInfoService;

    public EleInfoController(ITrackingEleInfoService trackingEleInfoService) {
        this.trackingEleInfoService = trackingEleInfoService;
    }

    /**
     * 添加坑位接口
     *
     * @param trackingEleInfoDto
     * @return
     */
    @PostMapping("add")
    public void eleAdd(@RequestBody TrackingEleInfoDto trackingEleInfoDto) {
        trackingEleInfoService.addEle(trackingEleInfoDto);
    }

    /**
     * 查询坑位详情接口
     *
     * @param trackingEleInfoDto
     * @return
     */
    @PostMapping("detail")
    public TrackingEleInfoVo getDetail(@RequestBody TrackingEleInfoDto trackingEleInfoDto) {
        return trackingEleInfoService.getDetail(trackingEleInfoDto);
    }

    /**
     * 删除坑位接口
     *
     * @param trackingEleInfoDto
     * @return
     */
    @PostMapping("delete")
    public void eleDelete(@RequestBody TrackingEleInfoDto trackingEleInfoDto) {
        trackingEleInfoService.deleteEle(trackingEleInfoDto);
    }

    /**
     * 编辑坑位接口
     *
     * @param queryEventType
     * @return
     */
    @PostMapping("edit")
    public void eleEdit(@RequestBody TrackingEleInfoDto queryEventType) {
        trackingEleInfoService.editEle(queryEventType);
    }


    /**
     * 分页查询接口
     *
     * @param queryEventTypePageDto
     * @return
     */
    @PostMapping("page")
    public PageResult<TrackingEleInfoVo> page(@RequestBody QueryEleInfoPageDto queryEventTypePageDto) {

        return trackingEleInfoService.page(queryEventTypePageDto);
    }



    /**
     * 根据pageId查询坑位列表接口
     *
     * @param req
     * @return
     */
    @PostMapping("pList")
    public List<TrackingEleInfoVo> getEleListByPageId(@RequestBody ReqSingleField<String> req) {

        return trackingEleInfoService.getEleListByPageId(req.getId());
    }
}
