package com.chervon.tracking.platform.controller;

import com.chervon.tracking.platform.dto.TrackingEventTypeDto;
import com.chervon.tracking.platform.dto.QueryEventTypePageDto;
import com.chervon.tracking.platform.entity.*;
import com.chervon.tracking.platform.service.ITrackingEventTypeService;
import com.chervon.tracking.platform.vo.TrackingEventTypeVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/12
 * @dec 描述
 */
@RestController
@RequestMapping("/event/type")
@Slf4j
public class EventTypeController {

    private final ITrackingEventTypeService eventTypeService;

    public EventTypeController(ITrackingEventTypeService eventTypeService) {
        this.eventTypeService = eventTypeService;
    }

    /**
     * 添加事件类型接口
     *
     * @param queryEventType
     * @return
     */
    @PostMapping("add")
    public void eventTypeAdd(@RequestBody TrackingEventTypeDto queryEventType) {
        eventTypeService.addEventType(queryEventType);
    }

    /**
     * 查询事件类型详情接口
     *
     * @param req
     * @return
     */
    @PostMapping("detail")
    public TrackingEventTypeVo getDetail(@RequestBody ReqSingleField<String> req) {
        return eventTypeService.getDetail(req.getId());
    }

    /**
     * 删除事件类型接口
     *
     * @param req
     * @return
     */
    @PostMapping("delete")
    public void eventDelete(@RequestBody ReqSingleField<String> req) {
        eventTypeService.deleteEventType(req.getId());
    }

    /**
     * 编辑事件类型接口
     *
     * @param queryEventType
     * @return
     */
    @PostMapping("edit")
    public void eventTypeEdit(@RequestBody TrackingEventTypeDto queryEventType) {
        eventTypeService.editEventType(queryEventType);
    }


    /**
     * 分页查询接口
     *
     * @param queryEventTypePageDto
     * @return
     */
    @PostMapping("page")
    public PageResult<TrackingEventTypeVo> eventTypePage(@RequestBody QueryEventTypePageDto queryEventTypePageDto) {

        return eventTypeService.page(queryEventTypePageDto);
    }


    /**
     * 无参查询事件类型列表接口
     *
     * @return
     */
    @PostMapping("pList")
    public List<TrackingEventTypeVo> eventTypePage() {

        return eventTypeService.getEventTypeList();
    }


}
