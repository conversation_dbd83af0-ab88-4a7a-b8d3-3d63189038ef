package com.chervon.tracking.platform.controller;

import com.chervon.tracking.platform.dto.TrackingPageInfoDto;
import com.chervon.tracking.platform.dto.QueryPageInfoPageDto;
import com.chervon.tracking.platform.entity.*;
import com.chervon.tracking.platform.service.ITrackingPageInfoService;
import com.chervon.tracking.platform.vo.TrackingPageInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/12
 * @dec 描述
 */
@RestController
@RequestMapping("/page")
@Slf4j
public class PageInfoController {

    private final ITrackingPageInfoService pageInfoService;

    public PageInfoController(ITrackingPageInfoService pageInfoService) {
        this.pageInfoService = pageInfoService;
    }

    /**
     * 添加埋点页面接口
     *
     * @param trackingPageInfoDto
     * @return
     */
    @PostMapping("add")
    public void pageAdd(@RequestBody TrackingPageInfoDto trackingPageInfoDto) {
        pageInfoService.addPage(trackingPageInfoDto);
    }

    /**
     * 查询埋点页面详情接口
     *
     * @param req
     * @return
     */
    @PostMapping("detail")
    public TrackingPageInfoVo getDetail(@RequestBody ReqSingleField<String> req) {
        return pageInfoService.getDetail(req.getId());
    }

    /**
     * 删除埋点页面接口
     *
     * @param trackingPageInfoDto
     * @return
     */
    @PostMapping("delete")
    public void eventDelete(@RequestBody TrackingPageInfoDto trackingPageInfoDto) {
        pageInfoService.deletePage(trackingPageInfoDto);
    }

    /**
     * 编辑埋点页面接口
     *
     * @param trackingPageInfoDto
     * @return
     */
    @PostMapping("edit")
    public void eventEdit(@RequestBody TrackingPageInfoDto trackingPageInfoDto) {
        pageInfoService.editPage(trackingPageInfoDto);
    }


    /**
     * 分页查询埋点页面接口
     *
     * @param queryPageInfoPageDto
     * @return
     */
    @PostMapping("list")
    public PageResult<TrackingPageInfoVo> page(@RequestBody QueryPageInfoPageDto queryPageInfoPageDto) {

        return pageInfoService.page(queryPageInfoPageDto);
    }

    /**
     * 根据moduleId查询页面信息接口
     *
     * @param req
     * @return
     */
    @PostMapping("pList")
    public List<TrackingPageInfoVo> getPageListByModuleId(@RequestBody ReqSingleField<String> req) {

        return pageInfoService.getPageListByModuleId(req.getId());
    }

}
