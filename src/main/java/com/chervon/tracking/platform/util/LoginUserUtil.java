package com.chervon.tracking.platform.util;

import cn.dev33.satoken.stp.StpUtil;
import com.chervon.common.core.domain.LoginSysUser;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LoginUserUtil {

    public static String getSign() {
        try {
            Object o = StpUtil.getSession().get(StpUtil.getLoginIdAsString());
            if (o == null) {
                return StpUtil.getLoginIdAsString();
            }
            LoginSysUser login = (LoginSysUser) o;
            return login.getEmployeeNumber() + "/" + login.getUserName();
        } catch (Exception e) {
            log.warn("自动注入警告 => 用户未登录");
            return null;
        }
    }
}
