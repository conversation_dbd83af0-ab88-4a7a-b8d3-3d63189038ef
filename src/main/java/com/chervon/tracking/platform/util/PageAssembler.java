package com.chervon.tracking.platform.util;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chervon.tracking.platform.entity.PageResult;


public class PageAssembler {
    public static PageResult assemble(IPage page) {
        PageResult pageResult = new PageResult();
        pageResult.setList(page.getRecords());
        pageResult.setPageNum(page.getCurrent());
        pageResult.setPages(page.getPages());
        pageResult.setTotal(page.getTotal());
        pageResult.setPageSize(page.getSize());
        return pageResult;
    }
}
