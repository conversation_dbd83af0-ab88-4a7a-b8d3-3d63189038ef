package com.chervon.tracking.platform.util;

import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

/**
 */
public class SnowFlake {
    private final static long TWEPOCH = 12888349746579L;
    // 机器标识位数
    private final static long WORKERIDBITS = 5L;
    // 数据中心标识位数
    private final static long DATACENTERIDBITS = 5L;
    // 毫秒内自增位数
    private final static long SEQUENCEBITS = 12L;
    // 机器ID偏左移12位
    private final static long WORKERIDSHIFT = SEQUENCEBITS;
    // 数据中心ID左移17位
    private final static long DATACENTERIDSHIFT = SEQUENCEBITS + WORKERIDBITS;
    // 时间毫秒左移22位
    private final static long TIMESTAMPLEFTSHIFT = SEQUENCEBITS + WORKERIDBITS + DATACENTERIDBITS;
    // sequence掩码，确保sequnce不会超出上限
    private final static long SEQUENCEMASK = -1L ^ (-1L << SEQUENCEBITS);
    // 上次时间戳
    private static long LASTTIMESTAMP = -1L;
    // 序列
    private long SEQUENCE = 0L;
    // 服务器ID
    private long WORKERID = 1L;
    private static long WORKERMASK = -1L ^ (-1L << WORKERIDBITS);
    // 进程编码
    private long PROCESSID = 1L;
    private static long PROCESSMASK = -1L ^ (-1L << WORKERIDBITS);

    private static SnowFlake SNOWFLAKE = null;

    static {
        SNOWFLAKE = new SnowFlake();
    }

    public static synchronized long nextId() {
        return SNOWFLAKE.getNextId();
    }

    private SnowFlake() {

        //获取机器编码
        this.WORKERID = this.getMachineNum();
        //获取进程编码
        RuntimeMXBean runtimeMXBean = ManagementFactory.getRuntimeMXBean();
        this.PROCESSID = Long.valueOf(runtimeMXBean.getName().split("@")[0]).longValue();

        //避免编码超出最大值
        this.WORKERID = WORKERID & WORKERMASK;
        this.PROCESSID = PROCESSID & PROCESSMASK;
    }

    public synchronized long getNextId() {
        //获取时间戳
        long timestamp = timeGen();
        //如果时间戳小于上次时间戳则报错
        if (timestamp < LASTTIMESTAMP) {
            try {
                throw new Exception("Clock moved backwards.  Refusing to generate id for " + (LASTTIMESTAMP - timestamp) + " milliseconds");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        //如果时间戳与上次时间戳相同
        if (LASTTIMESTAMP == timestamp) {
            // 当前毫秒内，则+1，与sequenceMask确保sequence不会超出上限
            SEQUENCE = (SEQUENCE + 1) & SEQUENCEMASK;
            if (SEQUENCE == 0) {
                // 当前毫秒内计数满了，则等待下一秒
                timestamp = tilNextMillis(LASTTIMESTAMP);
            }
        } else {
            SEQUENCE = 0;
        }
        LASTTIMESTAMP = timestamp;
        // ID偏移组合生成最终的ID，并返回ID
        long nextId = ((timestamp - TWEPOCH) << TIMESTAMPLEFTSHIFT) | (PROCESSID << DATACENTERIDSHIFT) |
                (WORKERMASK << WORKERIDSHIFT) | SEQUENCE;
        return nextId;
    }

    /**
     * 再次获取时间戳直到获取的时间戳与现有的不同
     *
     * @param lastTimestamp
     * @return 下一个时间戳
     */
    private long tilNextMillis(final long lastTimestamp) {
        long timestamp = this.timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = this.timeGen();
        }
        return timestamp;
    }

    private long timeGen() {
        return System.currentTimeMillis();
    }

    /**
     * 获取机器编码
     *
     * @return
     */
    private long getMachineNum() {
        long machinePiece;
        StringBuilder sb = new StringBuilder();
        Enumeration<NetworkInterface> e = null;
        try {
            e = NetworkInterface.getNetworkInterfaces();
        } catch (SocketException e1) {
            e1.printStackTrace();
        }
        while (e.hasMoreElements()) {
            NetworkInterface ni = e.nextElement();
            sb.append(ni.toString());
        }
        machinePiece = sb.toString().hashCode();
        return machinePiece;
    }
}
