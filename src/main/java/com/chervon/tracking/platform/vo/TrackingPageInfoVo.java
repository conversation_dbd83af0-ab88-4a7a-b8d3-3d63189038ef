package com.chervon.tracking.platform.vo;

import com.chervon.tracking.platform.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2023/1/12
 * @desc 描述
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TrackingPageInfoVo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 模块ID
     */
    private Integer moduleId;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 页面ID
     */
    private Integer pageId;

    /**
     * 页面名称
     */
    private String pageName;

    private Integer appId;

    private Integer busBelongModuleId;

    private String appName;

    private String busBelongModule;

}
