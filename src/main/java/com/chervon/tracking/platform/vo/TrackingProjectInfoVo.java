package com.chervon.tracking.platform.vo;

import com.chervon.tracking.platform.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/2/10
 * @desc 描述
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TrackingProjectInfoVo extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer appId;

    private String appName;

    private Integer busBelongModuleId;

    private String busBelongModule;

}
