package com.chervon.tracking.platform.vo;

import com.chervon.tracking.platform.entity.BaseEntity;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/2/9
 * @desc 描述
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TrackingEleInfoVo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer appId;

    private Integer busBelongModuleId;

    private String appName;

    private String busBelongModule;

    /**
     * 模块ID
     */
    private Integer moduleId;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 页面ID
     */
    private Integer pageId;

    /**
     * 页面名称
     */
    private String pageName;


    /**
     * 坑位ID
     */
    private Integer eleId;


    /**
     * 坑位名称
     */
    private String eleName;

    private String  eventTypeCode;

}
