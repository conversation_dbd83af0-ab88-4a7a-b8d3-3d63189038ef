package com.chervon.tracking.platform.dto;

import com.chervon.tracking.platform.entity.PageRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/1/12
 * @dec 描述
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryEventInfoPageDto extends PageRequest {

    /**
     * 应用ID
     */
    private Integer appId;

    private String appName;

    /**
     * 项目（所属业务模块）
     */
    private Integer busBelongModuleId;

    private String busBelongModule;

    /**
     * 模块名称
     */
    private Integer moduleId;

    private String moduleName;

    /**
     * 页面ID
     */
    private Integer pageId;

    private String pageName;

    /**
     * 区块ID
     */
    private Integer modId  ;

    /**
     * 坑位ID
     */
    private Integer eleId ;

    private String eleName;

    private String eventCode;

    /**
     * 状态
     */
    private Integer state ;

    private String targetVersion;

    private String eventTypeCode;

    private String eventTypeName;

    private Integer isDeleted;

    /**
     * 事件名称英文名
     */
    private String eventNameEn;

    /**
     * 事件名称中文名
     */
    private String eventNameCn;

    /**
     * 事件属性英文名
     */
    private String eventAttrEn;

    /**
     * 事件属性中文名
     */
    private String eventAttrCn;

    /**
     * 埋点形式（WEB端、APP端）
     */
    private String devType;

}
