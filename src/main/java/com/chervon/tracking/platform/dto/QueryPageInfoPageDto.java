package com.chervon.tracking.platform.dto;

import com.chervon.tracking.platform.entity.PageRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/1/12
 * @dec 描述
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryPageInfoPageDto extends PageRequest {

    /**
     * 模块ID
     */
    private Integer moduleId;

    private String moduleName;

    private String pageName;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updatedBy;

}
