package com.chervon.tracking.platform.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.Assert;
import com.chervon.tracking.platform.dto.QueryPageInfoPageDto;
import com.chervon.tracking.platform.dto.TrackingPageInfoDto;
import com.chervon.tracking.platform.entity.*;
import com.chervon.tracking.platform.enums.TrackingErrorCodeEnum;
import com.chervon.tracking.platform.mapper.TrackingPageInfoMapper;
import com.chervon.tracking.platform.service.ITrackingPageInfoService;
import com.chervon.tracking.platform.util.ConvertUtil;
import com.chervon.tracking.platform.util.PageAssembler;
import com.chervon.tracking.platform.vo.TrackingPageInfoVo;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2023/1/12
 * @desc 埋点页面 Service 实现类
 */
@Service
@Slf4j
public class TrackingPageInfoServiceImpl extends MPJBaseServiceImpl<TrackingPageInfoMapper, TrackingPageInfo> implements ITrackingPageInfoService {

    @Value("${tracking.platform.page.sizeLimit}")
    private int pageSizeLimit;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addPage(TrackingPageInfoDto trackingPageInfoDto) {
        Assert.notNull(trackingPageInfoDto, ErrorCode.PARAMETER_NOT_PROVIDED, "trackingPageInfoDto");
        Assert.isId(Long.valueOf(trackingPageInfoDto.getModuleId()), ErrorCode.PARAMETER_NOT_PROVIDED, "moduleId");
        QueryWrapper<TrackingPageInfo> queryWrapper = new QueryWrapper<TrackingPageInfo>()
                .select("max(page_id) as pageId");
        int maxPageId = Integer.parseInt(getMap(queryWrapper).get("pageId").toString());
        trackingPageInfoDto.setPageId(maxPageId + 1);
        this.save(trackingPageInfoDto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePage(TrackingPageInfoDto trackingPageInfoDto) {
        Assert.notNull(trackingPageInfoDto, ErrorCode.PARAMETER_NOT_PROVIDED, "trackingPageInfoDto");
        Assert.isId(Long.valueOf(trackingPageInfoDto.getModuleId()), ErrorCode.PARAMETER_NOT_PROVIDED, "moduleId");
        Assert.isId(Long.valueOf(trackingPageInfoDto.getPageId()), ErrorCode.PARAMETER_NOT_PROVIDED, "pageId");
        Integer moduleId = trackingPageInfoDto.getModuleId();
        Integer pageId = trackingPageInfoDto.getPageId();
        this.baseMapper.deleteById(moduleId, pageId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editPage(TrackingPageInfoDto trackingPageInfoDto) {
        Assert.notNull(trackingPageInfoDto, ErrorCode.PARAMETER_NOT_PROVIDED, "trackingPageInfoDto");
        Assert.isId(Long.valueOf(trackingPageInfoDto.getModuleId()), ErrorCode.PARAMETER_NOT_PROVIDED, "moduleId");
        Assert.isId(Long.valueOf(trackingPageInfoDto.getPageId()), ErrorCode.PARAMETER_NOT_PROVIDED, "pageId");
        this.baseMapper.update(trackingPageInfoDto, Wrappers.<TrackingPageInfo>lambdaQuery()
                .eq(TrackingPageInfo::getPageId, trackingPageInfoDto.getPageId()));
    }

    @Override
    public TrackingPageInfoVo getDetail(String pageId) {
        Assert.isId(Long.valueOf(pageId), ErrorCode.PARAMETER_NOT_PROVIDED, "pageId");
        return this.baseMapper.selectJoinOne(
                TrackingPageInfoVo.class, new MPJLambdaWrapper<TrackingPageInfo>()
                        .selectAll(TrackingPageInfo.class)
                        .selectAll(TrackingModuleInfo.class)
                        .selectAll(TrackingAppInfo.class)
                        .selectAll(TrackingProjectInfo.class)
                        .leftJoin(TrackingModuleInfo.class, TrackingModuleInfo::getModuleId, TrackingPageInfo::getModuleId)
                        .leftJoin(TrackingProjectInfo.class, TrackingProjectInfo::getBusBelongModuleId, TrackingModuleInfo::getBusBelongModuleId)
                        .leftJoin(TrackingAppInfo.class, TrackingAppInfo::getAppId, TrackingProjectInfo::getAppId)
                        .eq(TrackingPageInfo::getPageId, pageId));


    }

    @Override
    public PageResult<TrackingPageInfoVo> page(QueryPageInfoPageDto queryPageInfoPageDto) {

        Assert.notNull(queryPageInfoPageDto, ErrorCode.PARAMETER_NOT_PROVIDED, "queryPageInfoPageDto");
        if (queryPageInfoPageDto.getPageSize() > pageSizeLimit) {
            throw new ServiceException(TrackingErrorCodeEnum.COMMON_PAGE_SIZE_LIMIT, String.valueOf(pageSizeLimit));
        }
        Integer moduleId = queryPageInfoPageDto.getModuleId();
        String moduleName = queryPageInfoPageDto.getModuleName();
        String pageName = queryPageInfoPageDto.getPageName();
        String createBy = queryPageInfoPageDto.getCreateBy();
        String updateBy = queryPageInfoPageDto.getUpdatedBy();
        LocalDateTime createStartTime = queryPageInfoPageDto.getCreateStartTime();
        LocalDateTime createEndTime = queryPageInfoPageDto.getCreateEndTime();
        LocalDateTime updateStartTime = queryPageInfoPageDto.getUpdateStartTime();
        LocalDateTime updateEndTime = queryPageInfoPageDto.getUpdateEndTime();

        IPage<TrackingPageInfoVo> page = this.baseMapper.selectJoinPage(new Page<>(queryPageInfoPageDto.getPageNum(), queryPageInfoPageDto.getPageSize()),
                TrackingPageInfoVo.class, new MPJLambdaWrapper<TrackingPageInfo>()
                        .selectAll(TrackingPageInfo.class)
                        .selectAll(TrackingModuleInfo.class)
                        .leftJoin(TrackingModuleInfo.class, TrackingModuleInfo::getModuleId, TrackingPageInfo::getModuleId)
                        .eq(!Objects.isNull(moduleId), TrackingPageInfo::getModuleId, moduleId)
                        .like(!Objects.isNull(pageName), TrackingPageInfo::getPageName, pageName)
                        .like(!Objects.isNull(moduleName), TrackingModuleInfo::getModuleName, moduleName)
                        .eq(!Objects.isNull(createBy), TrackingPageInfo::getCreateBy, createBy)
                        .eq(!Objects.isNull(updateBy), TrackingPageInfo::getUpdatedBy, updateBy)
                        .ge(!Objects.isNull(createStartTime), TrackingPageInfo::getCreateTime, createStartTime)
                        .le(!Objects.isNull(createEndTime), TrackingPageInfo::getCreateTime, createEndTime)
                        .ge(!Objects.isNull(updateStartTime), TrackingPageInfo::getUpdateTime, updateStartTime)
                        .le(!Objects.isNull(updateEndTime), TrackingPageInfo::getUpdateTime, updateEndTime));

        return PageAssembler.assemble(page);

    }

    @Override
    public List<TrackingPageInfoVo> getPageListByModuleId(String moduleId) {
        Assert.isId(Long.valueOf(moduleId), ErrorCode.PARAMETER_NOT_PROVIDED, "moduleId");
        List<TrackingPageInfo> list = list(Wrappers.<TrackingPageInfo>lambdaQuery()
                .select(TrackingPageInfo::getPageId, TrackingPageInfo::getPageName)
                .eq(TrackingPageInfo::getModuleId, moduleId));

        return ConvertUtil.convertList(list, TrackingPageInfoVo.class);
    }

}
