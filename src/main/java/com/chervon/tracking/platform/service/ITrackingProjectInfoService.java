package com.chervon.tracking.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.tracking.platform.dto.QueryProjectInfoPageDto;
import com.chervon.tracking.platform.dto.TrackingProjectInfoDto;
import com.chervon.tracking.platform.entity.PageResult;
import com.chervon.tracking.platform.entity.TrackingProjectInfo;
import com.chervon.tracking.platform.vo.TrackingProjectInfoVo;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/1/12
 * @dec 描述
 */
public interface ITrackingProjectInfoService extends IService<TrackingProjectInfo> {

    void addProject(TrackingProjectInfoDto trackingProjectInfoDto);

    void deleteProject(TrackingProjectInfoDto trackingProjectInfoDto);

    void editProject(TrackingProjectInfoDto trackingProjectInfoDto);

    TrackingProjectInfoVo getDetail(String id);

    PageResult<TrackingProjectInfoVo> page(QueryProjectInfoPageDto queryProjectInfoPageDto);

    List<TrackingProjectInfoVo> getProjectListByAppId(String appCode);


}
