package com.chervon.tracking.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.tracking.platform.dto.QueryAppInfoPageDto;
import com.chervon.tracking.platform.dto.QueryProjectInfoPageDto;
import com.chervon.tracking.platform.dto.TrackingAppInfoDto;
import com.chervon.tracking.platform.dto.TrackingProjectInfoDto;
import com.chervon.tracking.platform.entity.PageResult;
import com.chervon.tracking.platform.entity.TrackingAppInfo;
import com.chervon.tracking.platform.entity.TrackingProjectInfo;
import com.chervon.tracking.platform.vo.TrackingAppInfoVo;
import com.chervon.tracking.platform.vo.TrackingProjectInfoVo;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/1/12
 * @dec 描述
 */
public interface ITrackingAppInfoService extends IService<TrackingAppInfo> {

    void addApp(TrackingAppInfoDto trackingAppInfoDto);

    void deleteApp(String id);

    void editApp(TrackingAppInfoDto trackingAppInfoDto);

    TrackingAppInfoVo getDetail(String id);

    PageResult<TrackingAppInfoVo> page(QueryAppInfoPageDto queryAppInfoPageDto);

    List<TrackingAppInfoVo> getAppList();

}
