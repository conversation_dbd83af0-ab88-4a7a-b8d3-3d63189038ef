package com.chervon.tracking.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.tracking.platform.dto.TrackingModuleInfoDto;
import com.chervon.tracking.platform.dto.QueryModuleInfoPageDto;
import com.chervon.tracking.platform.entity.TrackingModuleInfo;
import com.chervon.tracking.platform.entity.PageResult;
import com.chervon.tracking.platform.vo.TrackingModuleInfoVo;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/1/12
 * @dec 描述
 */
public interface ITrackingModuleInfoService extends IService<TrackingModuleInfo> {

    void addModule(TrackingModuleInfoDto trackingModuleInfoDto);

    void deleteModule(TrackingModuleInfoDto trackingModuleInfoDto);

    void editModule(TrackingModuleInfoDto trackingModuleInfoDto);

    TrackingModuleInfoVo getDetail(String id);

    PageResult<TrackingModuleInfoVo> page(QueryModuleInfoPageDto queryModuleInfoPageDto);

    List<TrackingModuleInfoVo> getModuleListByBusId(String busBelongModuleId);


}
