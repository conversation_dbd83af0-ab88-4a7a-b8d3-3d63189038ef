package com.chervon.tracking.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.tracking.platform.dto.TrackingEleInfoDto;
import com.chervon.tracking.platform.dto.QueryEleInfoPageDto;
import com.chervon.tracking.platform.entity.PageResult;
import com.chervon.tracking.platform.entity.TrackingEleInfo;
import com.chervon.tracking.platform.vo.TrackingEleInfoVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/9
 * @dec 描述
 */
public interface ITrackingEleInfoService extends IService<TrackingEleInfo> {

    void addEle(TrackingEleInfoDto queryEventInfoDto);

    void deleteEle(TrackingEleInfoDto trackingEleInfoDto);

    void editEle(TrackingEleInfoDto queryEventInfoDto);

    TrackingEleInfoVo getDetail(TrackingEleInfoDto trackingEleInfoDto);

    PageResult<TrackingEleInfoVo> page(QueryEleInfoPageDto queryElePageDto);

    List<TrackingEleInfoVo> getEleListByPageId(String id);
}
