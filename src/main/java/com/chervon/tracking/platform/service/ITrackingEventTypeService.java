package com.chervon.tracking.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.tracking.platform.dto.TrackingEventTypeDto;
import com.chervon.tracking.platform.dto.QueryEventTypePageDto;
import com.chervon.tracking.platform.entity.TrackingEventType;
import com.chervon.tracking.platform.entity.PageResult;
import com.chervon.tracking.platform.vo.TrackingEventTypeVo;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/1/12
 * @dec 描述
 */
public interface ITrackingEventTypeService extends IService<TrackingEventType> {

    void addEventType(TrackingEventTypeDto trackingEventTypeDto);

    void deleteEventType(String id);

    void editEventType(TrackingEventTypeDto trackingEventTypeDto);

    TrackingEventTypeVo getDetail(String id);

    PageResult<TrackingEventTypeVo> page(QueryEventTypePageDto queryEventTypePageDto);

    List<TrackingEventTypeVo> getEventTypeList();


}
