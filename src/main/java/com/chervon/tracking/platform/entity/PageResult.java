package com.chervon.tracking.platform.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class PageResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 结果集
     */
    private List<T> list = new ArrayList<>();

    /**
     * 当前页
     */
    private long pageNum;

    /**
     * 每页的数量
     */
    private long pageSize;

    /**
     * 总页数
     */
    private long pages;

    /**
     * 总记录数
     */
    private long total;

    public PageResult(long pageNum, long pageSize) {
        this(pageNum, pageSize, 0L);
    }

    public PageResult(long pageNum, long pageSize, long total) {
        this.total = total;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.pages = getPages();
    }

    public long getPages() {
        if (this.getPageSize() == 0L) {
            return 0L;
        } else {
            long pages = this.getTotal() / this.getPageSize();
            if (this.getTotal() % this.getPageSize() != 0L) {
                ++pages;
            }

            return pages;
        }
    }

}
