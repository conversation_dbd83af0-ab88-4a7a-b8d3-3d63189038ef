package com.chervon.tracking.platform.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/2/9
 * @desc 描述
 */
@EqualsAndHashCode(callSuper = true)
@TableName("t_tracking_app_info")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TrackingAppInfo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Integer appId;

    /**
     * 应用编码
     */
    private String appCode;

    /**
     * 应用名称
     */
    private String appName;
}
