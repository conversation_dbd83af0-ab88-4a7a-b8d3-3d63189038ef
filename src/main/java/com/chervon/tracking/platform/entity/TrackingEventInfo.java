package com.chervon.tracking.platform.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/1/11
 * @desc 描述
 */
@EqualsAndHashCode(callSuper = true)
@TableName("t_tracking_event_info")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TrackingEventInfo extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     *APP ID
     */
    private Integer appId;

    /**
     * 业务所属模块
     */
    private Integer busBelongModuleId;

    /**
     * 模块ID
     */
    private Integer moduleId;

    /**
     * 页面ID
     */
    private Integer pageId;

    /**
     * 区块ID
     */
    private Integer modId = 0;

    /**
     * 坑位ID
     */
    private Integer eleId;

    /**
     * 事件编码
     */
    private String eventCode;

    /**
     * 事件扩展字段
     */
    private String expandFields;

    /**
     * 事件类型，曝光：exposure，点击：click，停留：duration
     */
    private String eventTypeCode;

    /**
     * 事件名称英文名
     */
    private String eventNameEn;

    /**
     * 事件名称中文名
     */
    private String eventNameCn;

    /**
     * 事件描述
     */
    private String eventDesc;

    /**
     * 事件属性英文名
     */
    private String eventAttrEn;

    /**
     * 事件属性中文名
     */
    private String eventAttrCn;

    /**
     * 事件属性描述
     */
    private String eventAttrDesc;

    /**
     * 事件状态，1：在用，0：停用
     */
    private Integer state;

//    /**
//     * 是否删除，1：删除，0：未删除
//     */
//    private Integer isDeleted;

    /**
     * 埋点形式（WEB端、APP端）
     */
    private String devType;

    /**
     * 目标应用版本
     */
    private String targetVersion;

    /**
     * 上线时间
     */
    private LocalDateTime releaseDate;

    /**
     * 备注
     */
    private String comments;



}
