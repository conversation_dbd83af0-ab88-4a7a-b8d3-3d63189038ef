package com.chervon.usercenter.application.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.core.utils.CommonUtil;
import com.chervon.common.oss.uitl.S3Util;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.usercenter.api.service.CommonService;
import com.chervon.usercenter.domain.constant.UserCenterConstant;
import com.chervon.usercenter.domain.model.user.Email;
import com.chervon.usercenter.domain.model.user.User;
import com.chervon.usercenter.domain.model.user.UserRepository;
import com.chervon.usercenter.infrastructure.entity.UserDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;

@DubboService
@Slf4j
@Service
public class CommonServiceImpl implements CommonService {

    @Autowired
    private S3Util s3Util;
    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AwsProperties awsProperties;

    @Override
    public String getAesPassword(String email, Integer time) {
        String key = UserCenterConstant.USER_AES_PASSWORD + email;
        String salt = RedisUtils.getCacheObject(key);
        if (!StringUtils.isEmpty(salt)) {
            return salt;
        }
        User user = userRepository.find(new Email(email));
        if (user == null) {
            // 如果不存在该用户则生成新的GUID
            salt = CommonUtil.getGUID(CommonConstant.SIXTEEN);
        } else if (!StringUtils.isEmpty(user.getPassword().getSalt())) {
            salt = user.getPassword().getSalt();
        } else {
            // 用户还未注册，生成新的16位随机UUID作为AES秘钥
            salt = CommonUtil.getGUID(CommonConstant.SIXTEEN);
            userRepository.update(new LambdaUpdateWrapper<UserDo>().eq(UserDo::getId, user.getUserId()).set(UserDo::getSalt, salt));
        }
        // 设置有效期
        RedisUtils.setCacheObject(key, salt, Duration.ofSeconds(time));
        log.info("getAesPassword --> AesPassword is:{}, user email：{}, ", salt, email);
        return salt;
    }

    @Override
    public String getFileUrl(String key) {
        return s3Util.getPreSignedGetPrivateUrl(awsProperties.getPictureBucket().getName(), key);
    }
}
