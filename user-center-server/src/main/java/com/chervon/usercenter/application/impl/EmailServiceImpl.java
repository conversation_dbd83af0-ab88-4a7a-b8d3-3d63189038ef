package com.chervon.usercenter.application.impl;

import com.alibaba.csp.sentinel.util.function.Tuple2;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.BaseRemoteReqDto;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.usercenter.api.dto.CheckCodeDto;
import com.chervon.usercenter.api.dto.InviteRegisterDto;
import com.chervon.usercenter.api.exception.UserCenterErrorCode;
import com.chervon.usercenter.api.service.EmailService;
import com.chervon.usercenter.config.ExceptionMessageUtil;
import com.chervon.usercenter.domain.constant.UserCenterConstant;
import com.chervon.usercenter.domain.model.user.Email;
import com.chervon.usercenter.domain.model.user.User;
import com.chervon.usercenter.domain.model.user.UserRepository;
import com.chervon.usercenter.domain.model.user.UserStatusEnum;
import com.chervon.usercenter.infrastructure.config.EmailCodeConfig;
import com.chervon.usercenter.infrastructure.entity.UserDo;
import com.chervon.usercenter.infrastructure.repository.SendEmailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022-07-06
 */
@DubboService
@Slf4j
@Service
@EnableConfigurationProperties({EmailCodeConfig.class})
public class EmailServiceImpl implements EmailService {

    @Value("${mail.from}")
    private String sendfrom;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private EmailCodeConfig emailCodeConfig;

    @Autowired
    private SendEmailService sendEmailService;
    private static final String FROM_DEFAULT = "<EMAIL>";

    private String getFrom() {
        if (StringUtils.isBlank(sendfrom) || !sendfrom.contains("@")) {
            return FROM_DEFAULT;
        }
        return sendfrom;
    }

    /**
     * 查看email发送的次数
     *
     * @param email 邮箱
     * @param type  发送验证邮箱的类型: 注册 忘记密码 验证邮箱
     * @return 发送的次数
     */
    private Integer checkEmailCount(String email, String type) {
        String key = UserCenterConstant.USER_EMAIL_CODE_COUNT + email + ":" + type;
        Integer count = RedisUtils.getCacheObject(key);
        if (null == count) {
            count = CommonConstant.ZERO;
        }
        return count;
    }

    /**
     * 发送email次数计数
     *
     * @param email 邮箱
     * @param type 发送验证邮箱的类型: 注册 忘记密码 验证邮箱
     */
    private void writeSendEmailCount(String email, String type) {
        String key = UserCenterConstant.USER_EMAIL_CODE_COUNT + email + ":" + type;
        Integer count = checkEmailCount(email, type);
        // 计算过期时间，截止到当天24时过期
        Calendar curDate = Calendar.getInstance();
        Calendar tommorowDate = new GregorianCalendar(curDate
                .get(Calendar.YEAR), curDate.get(Calendar.MONTH), curDate
                .get(Calendar.DATE) + CommonConstant.ONE, CommonConstant.ZERO, CommonConstant.ZERO, CommonConstant.ZERO);
        // 当天还剩多少s
        Long lasterTime = (tommorowDate.getTimeInMillis() - curDate.getTimeInMillis()) / CommonConstant.ONE_THOUSAND;
        RedisUtils.deleteObject(key);
        RedisUtils.setCacheObject(key, count + CommonConstant.ONE);
        // 计数此时当天24时清空
        RedisUtils.expire(key, Duration.ofSeconds(lasterTime));
    }

    /**
     * email ： 邮箱
     * type ：验证码的类型
     * @param email
     * @param type
     * @return Tuple2<String,Boolean> code，sendCodeFlag
     */
    private Tuple2<String,Boolean> handleCode(String email, String type) {
        String key = email + ":" + type;
        Map<String, Object> value = RedisUtils.getCacheMap(key);
        if (value.isEmpty()) {
            value = new HashMap<>(CommonConstant.FOUR);
        }
        int count = checkEmailCount(email, type);
        if (count >= emailCodeConfig.getOneDaySendTimes()) {
            // 如果发送次数大约5，提示稍后再试
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_OPERATE_FREQUENTLY, key, count);
        }
        long last = (long) value.getOrDefault(UserCenterConstant.CODE_LAST, CommonConstant.ZERO_L);
        long now = System.currentTimeMillis();
        if (now - last < emailCodeConfig.getTwoInterval()) {
            // 两次发送时间差小于1分钟，不生成新的验证码且邮箱不发送
            log.warn("user operate frequently,key is {}, timeinterval is {}",key, (now-last)/1000);
            return Tuple2.of("",false);
        }
        // 生产验证码
        String code = (int) ((Math.random() * CommonConstant.NINE + CommonConstant.ONE) *
                CommonConstant.ONE_HUNDRED_THOUSAND) + "";
        value.put(UserCenterConstant.CODE, code);
        value.put(UserCenterConstant.CODE_LAST, System.currentTimeMillis());
        RedisUtils.setCacheMap(key, value);
        // 验证码有效期3小时
        RedisUtils.expire(key, Duration.ofHours(emailCodeConfig.getCodeExpire()));
        return Tuple2.of(code,true);
    }

    private boolean handleCheck(CheckCodeDto dto, String type) {
        String code = dto.getCode();
        String emailTo = dto.getEmailTo();
        String key = emailTo + ":" + type;
        Object hashCode = RedisUtils.getCacheMapValue(key, UserCenterConstant.CODE);
        if (hashCode == null) {
            log.error("Illegal operation ==> redis key:{}", key);
            return false;
        }
        boolean check = Objects.equals(code, hashCode);
        return check;
    }

    @Override
    public String sendCodeForRegister(BaseRemoteReqDto<String> dto) {
        String emailTo = dto.getReq();
        String language = dto.getLanguage();
        // check user if exist
        User user = userRepository.find(new Email(emailTo));
        if (null != user) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_EMAIL_USED, emailTo);
        }
        Tuple2<String,Boolean> tuple2 = handleCode(emailTo, UserCenterConstant.REGISTER);
        String code=tuple2.r1;
        if(tuple2.r2){
            sendEmailService.sendCode(code, emailTo, language, UserCenterConstant.REGISTER);
            // 写邮件发送次数
            writeSendEmailCount(emailTo, UserCenterConstant.REGISTER);
        }

        return code;
    }

    @Override
    public boolean checkCodeForRegister(CheckCodeDto dto) {
        if (!handleCheck(dto, UserCenterConstant.REGISTER)) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_EMAIL_CODE_ERROR);
        }
        return true;
    }

    @Override
    public String sendCodeForForgetPassword(BaseRemoteReqDto<String> dto) {
        String emailTo = dto.getReq();
        String language = dto.getLanguage();
        // check user if exist
        User user = userRepository.find(new Email(emailTo));
        if (null == user) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_USER_NOT_FOUND, emailTo);
        }
        if (UserStatusEnum.DISABLE.getValue().equals(user.getStatus().getValue())) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_ACCOUNT_DISABLE, emailTo);
        }

        Tuple2<String,Boolean> tuple2 = handleCode(emailTo, UserCenterConstant.FORGET_PASSWORD);
        String code=tuple2.r1;
        if(tuple2.r2){
            sendEmailService.sendCode(code, emailTo, language, UserCenterConstant.FORGET_PASSWORD);
            // 写邮件发送次数
            writeSendEmailCount(emailTo, UserCenterConstant.FORGET_PASSWORD);
        }
        return code;

    }

    @Override
    public boolean checkCodeForForgetPassword(CheckCodeDto dto) {
        if (!handleCheck(dto, UserCenterConstant.FORGET_PASSWORD)) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_EMAIL_CODE_ERROR);
        }
        return true;
    }

    @Override
    public String sendCodeForVerify(BaseRemoteReqDto<String> dto) {
        String emailTo = dto.getReq();
        String language = dto.getLanguage();
        // check user if exist
        User user = userRepository.find(new Email(emailTo));
        if (null == user) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_OTHER_PLATFORM_USER_NOT_EXIST, emailTo);
        }
        if ("ego".equals(user.getUserSourceCode())) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_EGO_USER_ERROR);
        }
        Tuple2<String,Boolean> tuple2 = handleCode(emailTo, UserCenterConstant.FIRST_LOGIN_VERIFY);
        String code=tuple2.r1;
        if(tuple2.r2){
            sendEmailService.sendCode(code, emailTo, language, UserCenterConstant.FIRST_LOGIN_VERIFY);
            // 写邮件发送次数
            writeSendEmailCount(emailTo, UserCenterConstant.FIRST_LOGIN_VERIFY);
        }
        return code;
    }

    @Override
    public boolean checkCodeForVerify(CheckCodeDto dto) {
        if (!handleCheck(dto, UserCenterConstant.FIRST_LOGIN_VERIFY)) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_EMAIL_CODE_ERROR);
        }
        // 更新user表isValidated是否已校验标志位
        userRepository.update(new LambdaUpdateWrapper<UserDo>().eq(UserDo::getEmail, dto.getEmailTo())
            .set(UserDo::getIsEmailValidated, CommonConstant.ONE));
        return true;
    }

    @Override
    public void sendInviteRegister(InviteRegisterDto dto) {
        sendEmailService.sendInviteRegister(dto);
    }
}
