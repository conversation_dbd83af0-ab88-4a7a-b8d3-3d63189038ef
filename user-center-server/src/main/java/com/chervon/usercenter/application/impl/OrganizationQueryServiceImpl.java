package com.chervon.usercenter.application.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.LdapUtils;
import com.chervon.common.oss.uitl.S3Util;
import com.chervon.usercenter.api.service.OrganizationQueryService;
import com.chervon.usercenter.api.vo.*;
import com.chervon.usercenter.domain.model.organization.user.tree.OrganizationUserTreeRepository;
import com.chervon.usercenter.infrastructure.entity.OrganizationDo;
import com.chervon.usercenter.infrastructure.entity.SysUserDo;
import com.chervon.usercenter.infrastructure.mapper.OrganizationMapper;
import com.chervon.usercenter.infrastructure.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-06-07 09:59
 **/
@DubboService
@Slf4j
@Service
public class OrganizationQueryServiceImpl implements OrganizationQueryService {

    @Autowired
    private OrganizationMapper orgMapper;

    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private S3Util s3Util;
    @Autowired
    private AwsProperties awsProperties;
    @Autowired
    private OrganizationUserTreeRepository organizationUserTreeRepository;

    @Override
    public OrganizationTreeVo getOrganizationTree(String guid) {
        // 获取下属部门列表
        List<OrganizationDetailVo> orgs = orgMapper.getChildOrgs(guid);
        // 获取部门下面的用户
        LambdaQueryWrapper<SysUserDo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserDo::getOrganizationGuid, guid);
        List<SysUserDo> sysUsers = sysUserMapper.selectList(queryWrapper);
        List<SysUserVo> users = ConvertUtil.convertList(sysUsers, SysUserVo.class);
        return new OrganizationTreeVo(orgs, users);
    }

    @Override
    public OrganizationVo getOrganizationByGuid(String guid) {
        LambdaQueryWrapper<OrganizationDo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrganizationDo::getLdapOrgGuid, guid);
        OrganizationDo organizationDo = orgMapper.selectOne(wrapper);
        return ConvertUtil.convert(organizationDo, OrganizationVo.class);
    }

    @Override
    public OrgUserNodeVo initTreeBySelected(List<String> orgGuids, List<String> userGuids) {
        if (orgGuids == null) {
            orgGuids = new ArrayList<>();
        }
        if (userGuids == null) {
            userGuids = new ArrayList<>();
        }
        // 构建虚拟的root节点
        OrgUserNodeVo root = new OrgUserNodeVo();
        root.setNodeType(1);
        root.setNodeGuid("root");
        root.setNodeName("root");

        List<String> newOrgGuids = new ArrayList<>(orgGuids);

        Map<String, List<SysUserDo>> sysUserGroup = new HashMap<>();
        if (!userGuids.isEmpty()) {
            List<SysUserDo> sysUserList = sysUserMapper.selectList(new LambdaQueryWrapper<SysUserDo>().in(SysUserDo::getLdapUserGuid, userGuids));
            newOrgGuids.addAll(sysUserList.stream().map(SysUserDo::getOrganizationGuid).collect(Collectors.toList()));
            sysUserGroup = sysUserList.stream().collect(Collectors.groupingBy(SysUserDo::getOrganizationGuid));
        }
        newOrgGuids = newOrgGuids.stream().distinct().collect(Collectors.toList());

        List<List<OrganizationDo>> list = new ArrayList<>();
        newOrgGuids.forEach(e -> list.add(handleParent(e)));

        for (List<OrganizationDo> e : list) {
            OrgUserNodeVo current = root;
            for (OrganizationDo m : e) {
                if (current.getChildren().stream().noneMatch(r -> StringUtils.equals(r.getNodeGuid(), m.getLdapOrgGuid()))) {
                    current = handleBrothers(current, m);
                } else {
                    // 选择出已存在的节点
                    current = current.getChildren().stream().filter(r -> StringUtils.equals(r.getNodeGuid(), m.getLdapOrgGuid())).findFirst().orElse(new OrgUserNodeVo());
                }
            }
        }

        // 遍历树，判断添加员工
        handleTree(root, sysUserGroup);
        return root;
    }

    @Override
    public List<String> treeOrgGuids(String userGuid) {
        List<String> res = new ArrayList<>();
        if (userGuid == null) {
            return res;
        }
        res.add(userGuid);
        SysUserDo user = sysUserMapper.selectOne(new LambdaQueryWrapper<SysUserDo>().eq(SysUserDo::getLdapUserGuid, userGuid));
        if (user == null || user.getOrganizationGuid() == null) {
            return res;
        }
        res.add(user.getOrganizationGuid());
        boolean flag = true;
        String orgGuid = user.getOrganizationGuid();
        while (flag) {
            OrganizationDo org = orgMapper.selectOne(new LambdaQueryWrapper<OrganizationDo>().eq(OrganizationDo::getLdapOrgGuid, orgGuid));
            if (org == null || StringUtils.isBlank(org.getLdapParentOrgGuid())) {
                flag = false;
            } else {
                orgGuid = org.getLdapParentOrgGuid();
                res.add(orgGuid);
            }
        }
        return res;
    }

    @Override
    public OrgUserNodeVo initTreeBySearch(String search) {
        List<String> orgIds = orgMapper.selectList(new LambdaQueryWrapper<OrganizationDo>().like(OrganizationDo::getOrgName, search).select(OrganizationDo::getLdapOrgGuid))
                .stream().filter(Objects::nonNull).map(OrganizationDo::getLdapOrgGuid).collect(Collectors.toList());
        List<String> userIds = sysUserMapper.selectList(new LambdaQueryWrapper<SysUserDo>().like(SysUserDo::getUserName, search).or().like(SysUserDo::getEmployeeNumber, search).select(SysUserDo::getLdapUserGuid))
                .stream().filter(Objects::nonNull).map(SysUserDo::getLdapUserGuid).collect(Collectors.toList());
        if (orgIds.isEmpty() && userIds.isEmpty()) {
            return new OrgUserNodeVo();
        }
        return initTreeBySelected(orgIds, userIds);
    }

    private void handleTree(OrgUserNodeVo current, Map<String, List<SysUserDo>> sysUserGroup) {
        if (current.isHasChild()) {
            // 将这个组织下的人也查询出来
            List<SysUserDo> sysUserDos = sysUserMapper.selectList(new LambdaQueryWrapper<SysUserDo>().eq(SysUserDo::getOrganizationGuid, current.getNodeGuid()));
            sysUserDos.forEach(i -> {
                OrgUserNodeVo userNode = new OrgUserNodeVo();
                userNode.setNodeType(0);
                userNode.setNodeGuid(i.getLdapUserGuid());
                String employeeNumber = i.getEmployeeNumber() == null ? "" : i.getEmployeeNumber();
                String userName = i.getUserName() == null ? "" : i.getUserName();
                userNode.setNodeName(employeeNumber + "/" + userName);
                current.getChildren().add(userNode);
            });
            current.getChildren().forEach(e -> handleTree(e, sysUserGroup));
        } else if (sysUserGroup.get(current.getNodeGuid()) != null) {
            // 将这个组织下的组织查出来
            List<OrganizationDo> organizationDos = orgMapper.selectList(new LambdaQueryWrapper<OrganizationDo>().eq(OrganizationDo::getLdapParentOrgGuid, current.getNodeGuid()));
            organizationDos.forEach(e -> {
                OrgUserNodeVo node = new OrgUserNodeVo();
                node.setNodeType(1);
                node.setNodeGuid(e.getLdapOrgGuid());
                node.setNodeName(e.getOrgName());
                current.getChildren().add(node);
            });
            // 将这个组织下的人也查询出来
            List<SysUserDo> sysUserDos = sysUserMapper.selectList(new LambdaQueryWrapper<SysUserDo>().eq(SysUserDo::getOrganizationGuid, current.getNodeGuid()));
            sysUserDos.forEach(i -> {
                OrgUserNodeVo userNode = new OrgUserNodeVo();
                userNode.setNodeType(0);
                userNode.setNodeGuid(i.getLdapUserGuid());
                String employeeNumber = i.getEmployeeNumber() == null ? "" : i.getEmployeeNumber();
                String userName = i.getUserName() == null ? "" : i.getUserName();
                userNode.setNodeName(employeeNumber + "/" + userName);
                current.getChildren().add(userNode);
            });
        }
    }

    private OrgUserNodeVo handleBrothers(OrgUserNodeVo current, OrganizationDo m) {
        OrgUserNodeVo res = new OrgUserNodeVo();
        LambdaQueryWrapper<OrganizationDo> queryWrapper = new LambdaQueryWrapper<>();
        if (m.getLdapParentOrgGuid() == null) {
            queryWrapper.isNull(OrganizationDo::getLdapParentOrgGuid);
        } else {
            queryWrapper.eq(OrganizationDo::getLdapParentOrgGuid, m.getLdapParentOrgGuid());
        }
        List<OrganizationDo> organizationDos = orgMapper.selectList(queryWrapper);
        for (OrganizationDo e : organizationDos) {
            OrgUserNodeVo node = new OrgUserNodeVo();
            node.setNodeType(1);
            node.setNodeGuid(e.getLdapOrgGuid());
            node.setNodeName(e.getOrgName());
            current.getChildren().add(node);
            if (StringUtils.equals(e.getLdapOrgGuid(), m.getLdapOrgGuid())) {
                res = node;
            }
        }
        return res;
    }


    private List<OrganizationDo> handleParent(String orgGuid) {
        List<OrganizationDo> res = new ArrayList<>();
        OrganizationDo one = orgMapper.selectOne(new LambdaQueryWrapper<OrganizationDo>().eq(OrganizationDo::getLdapOrgGuid, orgGuid));
        if (one == null) {
            return res;
        }
        res.add(one);
        while (StringUtils.isNotBlank(one.getLdapParentOrgGuid())) {
            one = orgMapper.selectOne(new LambdaQueryWrapper<OrganizationDo>().eq(OrganizationDo::getLdapOrgGuid, one.getLdapParentOrgGuid()));
            res.add(one);
        }
        Collections.reverse(res);
        return res;
    }

    @Override
    public void createAllOrgUserFile() throws IOException {
        List<OrganizationDo> organizationDos = orgMapper.selectList(Wrappers.emptyWrapper());
        organizationDos.forEach(organizationDo -> {
            if (organizationDo.getLdapParentOrgGuid() == null) {
                organizationDo.setLdapParentOrgGuid("0");
            }
        });
        List<SysUserDo> sysUserDos = sysUserMapper.selectList(Wrappers.emptyWrapper());
        Map<String, List<OrganizationDo>> orgGuidMap = organizationDos.stream().collect(Collectors.groupingBy(OrganizationDo::getLdapParentOrgGuid));
        Map<String, List<SysUserDo>> sysUserMap = sysUserDos.stream().collect(Collectors.groupingBy(SysUserDo::getOrganizationGuid));
        // 构建虚拟的root节点
        OrgUserNodeVo root = new OrgUserNodeVo();
        root.setNodeType(1);
        root.setNodeGuid("root");
        root.setNodeName("root");
        OrgUserNodeVo result = handleAllTree(root, orgGuidMap, sysUserMap);
        System.out.println(result);
        File file = new File("static/uploadFile/allOrgUserTree.json");
        FileUtils.write(file, JSON.toJSONString(result));
        String key = UUID.randomUUID() + ".json";
        s3Util.uploadFile(awsProperties.getPictureBucket().getName(), "allOrgUserTree/" + key, file, true);
        organizationUserTreeRepository.addNewKeyRemoveOld(key);
    }

    private OrgUserNodeVo handleAllTree(OrgUserNodeVo current, Map<String, List<OrganizationDo>> orgGuidMap, Map<String, List<SysUserDo>> sysUserMap) {
        if ("root".equals(current.getNodeGuid())) {
            // 将这个组织下的组织查出来
            List<OrganizationDo> organizationDos = orgGuidMap.get("0");
            if (!CollectionUtils.isEmpty(organizationDos)) {
                organizationDos.forEach(e -> {
                    OrgUserNodeVo node = new OrgUserNodeVo();
                    node.setNodeType(1);
                    node.setNodeGuid(e.getLdapOrgGuid());
                    node.setNodeName(e.getOrgName());
                    current.getChildren().add(handleAllTree(node, orgGuidMap, sysUserMap));
                });
            }
            return current;
        }
        // 将这个组织下的人也查询出来
        List<SysUserDo> sysUserDos = sysUserMap.get(current.getNodeGuid());
        if (!CollectionUtils.isEmpty(sysUserDos)) {
            sysUserDos.forEach(i -> {
                OrgUserNodeVo userNode = new OrgUserNodeVo();
                userNode.setNodeType(0);
                userNode.setNodeGuid(i.getLdapUserGuid());
                String employeeNumber = i.getEmployeeNumber() == null ? "" : i.getEmployeeNumber();
                String userName = i.getUserName() == null ? "" : i.getUserName();
                userNode.setNodeName(employeeNumber + "/" + userName);
                current.getChildren().add(userNode);
            });
        }
        // 将这个组织下的组织查出来
        List<OrganizationDo> organizationDos = orgGuidMap.get(current.getNodeGuid());
        if (!CollectionUtils.isEmpty(organizationDos)) {
            organizationDos.forEach(e -> {
                OrgUserNodeVo node = new OrgUserNodeVo();
                node.setNodeType(1);
                node.setNodeGuid(e.getLdapOrgGuid());
                node.setNodeName(e.getOrgName());
                current.getChildren().add(handleAllTree(node, orgGuidMap, sysUserMap));
            });
        }
        return current;
    }
}
