package com.chervon.usercenter.application.impl;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.LdapUtils;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.redis.constant.RedisConstant;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.usercenter.api.service.OrganizationCommandService;
import com.chervon.usercenter.application.*;
import com.chervon.usercenter.domain.model.organization.Organization;
import com.chervon.usercenter.domain.model.organization.OrganizationRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ldap.core.AttributesMapper;
import org.springframework.ldap.core.ContextMapper;
import org.springframework.ldap.core.DirContextAdapter;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.core.NameClassPairMapper;
import org.springframework.ldap.filter.AndFilter;
import org.springframework.ldap.filter.EqualsFilter;
import org.springframework.ldap.query.LdapQuery;
import org.springframework.ldap.query.LdapQueryBuilder;
import org.springframework.ldap.query.SearchScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.naming.NameClassPair;
import javax.naming.NamingException;
import javax.naming.directory.Attributes;
import javax.naming.directory.DirContext;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-06-07 09:59
 **/
@DubboService
@Slf4j
@Service
public class OrganizationCommandServiceImpl implements OrganizationCommandService {
    @Autowired
    private OrganizationRepository organizationRepository;
    @Autowired
    private LdapTemplate ldapTemplate;

    /**
     * 从Ldap获取OrganizationSyncDto列表，并同步OrgName-Guid到Redis中
     *
     * @return 组织同步Dto列表
     */
    public List<OrganizationSyncDto> getOrganizationSyncDtoListFromLdap() {
        return listChildren(StringUtil.EMPTY, "cn", null);
    }

    /**
     * 获取子部门树
      * @param baseName 父级部门路径
     * @return
     */
    private List<OrganizationSyncDto> listChildren(String baseName, String parentOrgName, String parentGuid) {
        LdapQuery query = LdapQueryBuilder.query()
                .base(baseName).searchScope(SearchScope.ONELEVEL).filter("objectClass=organizationalUnit");
        Map cacheMap = new HashMap();
        List<OrganizationSyncDto> returnList = new ArrayList<>();
        List<OrganizationSyncDto> list = ldapTemplate.search(query, (AttributesMapper<OrganizationSyncDto>) attrs -> {
            OrganizationSyncDto organizationSyncDto = new OrganizationSyncDto();
            organizationSyncDto.setOu((String) attrs.get("ou").get());
            byte[] bytes = (byte[]) attrs.get("objectGUID").get();
            organizationSyncDto.setLdapOrgGuid(LdapUtils.getObjectGuid(bytes));
            organizationSyncDto.setOrgName((String) attrs.get("name").get());
            organizationSyncDto.setParentOrgName(parentOrgName);
            organizationSyncDto.setLdapParentOrgGuid(parentGuid);
            cacheMap.put(RedisConstant.USER_CENTER_LDAP_ROOT_ORGANIZATION + organizationSyncDto.getLdapOrgGuid(),
                    organizationSyncDto.getOrgName());
            String distinguishedName = (String) attrs.get("distinguishedName").get();
            organizationSyncDto.setDistinguishedName(distinguishedName);
            // log.info("distinguishedName:{}", distinguishedName);
            String childBase = distinguishedName.replaceAll(",DC=cn,DC=chervongroup,DC=net","");
            List<OrganizationSyncDto> children
                    = listChildren(childBase, organizationSyncDto.getOrgName(), organizationSyncDto.getLdapOrgGuid());
            returnList.addAll(children);
            return organizationSyncDto;
        });
        if (CollectionUtils.isEmpty(list)) {
            return returnList;
        }
        returnList.addAll(list);
        RedisUtils.setCacheObjects(cacheMap, CommonConstant.ONE, TimeUnit.DAYS);
        return returnList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean fullSyncOrganization() {
        log.info("开始同步组织");
        boolean flag = true;
        long startTime = System.currentTimeMillis();
        List<OrganizationSyncDto> organizationSyncDtoList = getOrganizationSyncDtoListFromLdap();

        //对比数据库中当前的organizationFormDbList与我们获取到的organizationSyncDtoList
        List<Organization> organizationFromDbList = organizationRepository.listAll();
        HashMap<String, Organization> organizationFromDbHashMap = new HashMap<>(CommonConstant.SIXTEEN);
        for (Organization organization : organizationFromDbList) {
            organizationFromDbHashMap.put(organization.getLdapOrgGuid(), organization);
        }

        //LDAP新增、更新组织
        List<Organization> organizationFromLdapList = ConvertUtil.convertList(organizationSyncDtoList,
                Organization.class);
        List<Organization> organizationSaveList = new ArrayList<>();
        List<Organization> organizationUpdateList = new ArrayList<>();
        for (Organization organization : organizationFromLdapList) {
            Organization organizationFromDb = organizationFromDbHashMap.get(organization.getLdapOrgGuid());
            //如果数据库中不存在说明新增
            if (organizationFromDb == null) {
                organizationSaveList.add(organization);
            }
            //数据库中存在，对比属性值
            else {
                //如果属性值有不同，需要更新数据库
                if (!organization.isSameWith(organizationFromDb)) {
                    organization.setId(organizationFromDb.getId());
                    organizationUpdateList.add(organization);
                }
                //如果相同则跳过
            }
        }
        log.info("新增组织列表：organizationSaveList[" + organizationSaveList.size() + "]:");
        log.info("修改组织列表：organizationUpdateList[" + organizationUpdateList.size() + "]");
        if (!organizationSaveList.isEmpty()) {
            organizationRepository.saveList(organizationSaveList);
            flag = false;
        }
        if (!organizationUpdateList.isEmpty()) {
            organizationRepository.updateList(organizationUpdateList);
            flag = false;
        }
        //对Ldap与Db求差值获取需要删除的组织列表
        //List stream流操作，根据Guid字段筛选出ListDb有但是ListLdap没有的Organization
        List<Organization> organizationDiffList = organizationFromDbList.stream().filter(organizationFromDb ->
                organizationFromLdapList.stream().map(Organization::getLdapOrgGuid).noneMatch(guid ->
                        Objects.equals(organizationFromDb.getLdapOrgGuid(), guid))).collect(Collectors.toList());
        log.info("删除组织列表：organizationDiffList[" + organizationDiffList.size() + "]");
        if (!organizationDiffList.isEmpty()) {
            organizationRepository.deleteList(organizationDiffList);
            flag = false;
        }
        long runTime = System.currentTimeMillis() - startTime;
        log.info("同步组织方法执行时间：{}ms", runTime);
        return flag;
    }
}
