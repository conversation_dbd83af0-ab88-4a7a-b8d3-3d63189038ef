package com.chervon.usercenter.domain.model.user;

import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.ValueObject;
import com.chervon.usercenter.api.exception.UserCenterErrorCode;
import com.chervon.usercenter.api.exception.UserCenterException;
import com.chervon.usercenter.config.ExceptionMessageUtil;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * 手机
 *
 * <AUTHOR>
 * @date 2022-06-14
 **/
@Getter
@ToString
public final class Phone implements ValueObject<Phone> {

    private String phone;

    /**
     * 有效性正则
     */

    private static final Pattern VALID_PATTERN = Pattern.compile(CommonConstant.TEL_CHECK);

    /**
     * Constructor.
     *
     * @param phone
     */
    public Phone(final String phone) {
//        if (!StringUtils.isEmpty(phone) && !VALID_PATTERN.matcher(phone).matches()) {
//            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_PHONE_INVALID);
//        }
        this.phone = phone;
    }

    @Override
    public boolean sameValueAs(Phone other) {
        return other != null && this.phone.equals(other.phone);
    }

}
