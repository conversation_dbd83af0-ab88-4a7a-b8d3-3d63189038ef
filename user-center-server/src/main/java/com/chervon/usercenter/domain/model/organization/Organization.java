package com.chervon.usercenter.domain.model.organization;

import com.chervon.common.core.domain.Entity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022-06-06 17:55
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Organization implements Entity<Organization> {

    private Long id;

    /**
     * 组织名称
     */
    private String orgName;
    /**
     * LDAP组织OU
     */
    private String ou;
    /**
     * LDAP组织GUID
     */
    private String ldapOrgGuid;
    /**
     * LDAP父组织GUID
     */
    private String ldapParentOrgGuid;
    /**
     * 父组织名称
     */
    private String parentOrgName;
    /**
     * 层级关系
     */
    private String distinguishedName;

    /**
     * Organization的基本属性是否与另一个相同
     *
     * @param other 另一个Organization
     * @return 是否相等
     */
    @Override
    public boolean sameIdentityAs(Organization other) {
        return other != null && ldapOrgGuid.equals(other.ldapOrgGuid);
    }

    /**
     * Organization的基本属性是否与另一个相同
     *
     * @param anotherOrganization 另一个Organization
     * @return 是否相等
     */
    public Boolean isSameWith(Organization anotherOrganization) {
        return (Objects.equals(this.getOrgName(), anotherOrganization.getOrgName()) &&
                Objects.equals(this.getOu(), anotherOrganization.getOu()) &&
                Objects.equals(this.getLdapOrgGuid(), anotherOrganization.getLdapOrgGuid()) &&
                Objects.equals(this.getLdapParentOrgGuid(), anotherOrganization.getLdapParentOrgGuid()));
    }
}
