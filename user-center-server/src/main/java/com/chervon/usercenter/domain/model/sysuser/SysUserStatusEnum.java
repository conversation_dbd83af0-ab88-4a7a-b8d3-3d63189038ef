package com.chervon.usercenter.domain.model.sysuser;

import com.chervon.common.core.domain.ValueObject;

/**
 * <AUTHOR>
 * @since 2022-06-23 11:39
 **/
public enum SysUserStatusEnum implements ValueObject<SysUserStatusEnum> {

    /**
     * 禁用 0 disable
     */
    DISABLE(0),

    /**
     * 正常 1 activation
     */
    ACTIVATION(1);


    SysUserStatusEnum(int i) {

    }

    @Override
    public boolean sameValueAs(SysUserStatusEnum other) {
        return this.equals(other);
    }
}
