package com.chervon.usercenter.config;

import cn.hutool.extra.spring.SpringUtil;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.operation.api.exception.OperationErrorCode;
import com.chervon.operation.api.exception.OperationException;
import com.chervon.usercenter.api.exception.UserCenterErrorCode;
import com.chervon.usercenter.api.exception.UserCenterException;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/17 11:18
 */
public class ExceptionMessageUtil {

    private static final RemoteMultiLanguageService remoteMultiLanguageService = SpringUtil.getBean("remoteMultiLanguageService");

    public static UserCenterException getException(UserCenterErrorCode errorCode, Object... args) {
        UserCenterException exception = new UserCenterException(errorCode, args);
        String code = errorCode.getCode();
        if (code != null) {
            String language = remoteMultiLanguageService.simpleFindMultiLanguageByCode(code);
            if (language != null) {
                exception.setTempMessage(language);
            }
        }
        return exception;
    }

    public static OperationException getException(OperationErrorCode errorCode, Object... args) {
        OperationException exception = new OperationException(errorCode, args);
        String code = errorCode.getCode();
        if (code != null) {
            String language = remoteMultiLanguageService.simpleFindMultiLanguageByCode(code);
            if (language != null) {
                exception.setTempMessage(language);
            }
        }
        return exception;
    }

}
