package com.chervon.usercenter;

import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.domain.SingleInfoResp;
import com.chervon.usercenter.api.service.CommonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/common")
@Validated
@Api(tags = "用户中心通用接口")
public class CommonController {

    @Autowired
    private CommonService commonService;

    /**
     * 获取临时aes密码
     * 不需要登录鉴权
     *
     * @return
     */
    @PostMapping("/get/file")
    @ApiModelProperty("获取临时aes密码")
    public SingleInfoResp<String> getEncryptSecret(@RequestBody SingleInfoReq<String> key) {
        return new SingleInfoResp<>(commonService.getFileUrl(key.getReq()));
    }

}
