package com.chervon.usercenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2022-06-06 17:39
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("organization")
public class OrganizationDo extends BaseDo {
    /**
     * 组织名称
     */
    private String orgName;
    /**
     * LDAP组织OU
     */
    private String ou;
    /**
     * LDAP组织GUID
     */
    private String ldapOrgGuid;
    /**
     * LDAP父组织GUID
     */
    private String ldapParentOrgGuid;

    /**
     * LDAP层级关系
     */
    private String distinguishedName;
    /**
     * 同步时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime syncTime;
}
