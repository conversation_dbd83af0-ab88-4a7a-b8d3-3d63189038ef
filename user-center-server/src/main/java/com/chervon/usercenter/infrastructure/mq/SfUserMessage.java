package com.chervon.usercenter.infrastructure.mq;

import com.chervon.usercenter.domain.model.user.User;
import lombok.Data;
import lombok.Getter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-03-24 15:32
 **/
@Data
public class SfUserMessage implements Serializable {
    /**
     * 操作类型
     */
    @NotNull
    private TypeEnum type;
    /**
     * 用户聚合根
     */
    private User user;

    @Getter
    public enum TypeEnum {
        /**
         * 增加
         */
        ADD,
        /**
         * 更新
         */
        UPDATE,
        /**
         * 删除
         */
        DELETE
    }
}
