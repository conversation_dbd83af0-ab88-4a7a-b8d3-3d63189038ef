package com.chervon.usercenter.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user")
public class UserDo extends BaseDo {

    /**
     * 名字
     */
    private String firstName;

    /**
     * 姓
     */
    private String lastName;
    /**
     * 邮箱地址
     */
    private String email;
    /**
     * 密码，MD5(原始密码+盐)
     */
    private String password;
    /**
     * 密码盐
     */
    private String salt;
    /**
     * 对陈加密密码
     */
    private String aesPassword;
    /**
     * 对称加密秘钥
     */
    private String aesSalt;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 用户状态：1 激活，2禁用
     */
    private Integer status;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 所属国家
     */
    private String country;

    /**
     * 性别：1男，2女，0未知
     */
    private Integer gender;

    /**
     * 头像存储在S3中的相对路径
     */
    private String photo;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 地址1
     */
    private String addressLine;

    /**
     * 城市
     */
    private String city;

    /**
     * 县
     */
    private String county;

    /**
     * 街道
     */
    private String street;


    /**
     * 手机系统版本
     **/
    @ApiModelProperty("手机系统版本")
    private String phoneOsVersion;

    /**
     * 手机型号
     **/
    @ApiModelProperty("手机型号")
    private String phoneModel;

    /**
     * APP宿主包版本
     **/
    @ApiModelProperty("APP宿主包版本")
    private String appVersion;

    /**
     * RN包版本
     **/
    @ApiModelProperty("RN包版本")
    private String rnVersion;

    /**
     * ip地址
     **/
    @ApiModelProperty("ip地址")
    private String ip;

    /**
     * app类型
     **/
    @ApiModelProperty("app类型")
    private String appTypeCode;

    /**
     * 用户类型; new
     */
    @ApiModelProperty("用户类型")
    private String userTypeCode;

    /**
     * 用户来源： ego,sf
     **/
    @ApiModelProperty("用户来源")
    private String userSourceCode;

    /**
     * 邮箱是否被验证 0否 1是
     * 仅当非EGO用户来源时需要判断
     */
    @ApiModelProperty("邮箱是否被验证 0否 1是")
    private Integer isEmailValidated;

    /**
     * app在线状态
     **/
    @ApiModelProperty("app在线状态")
    private String appPresenceCode;

    /**
     * 用户SaleForceId
     */
    @ApiModelProperty("用户SaleForceId")
    private String sfUserId;

    /**
     * 最近一次同步时间
     */
    @ApiModelProperty("最近一次同步时间")
    private LocalDateTime lastSyncTime;

    /**
     * 最近一次登录时间
     */
    @ApiModelProperty("最近一次登录时间")
    private LocalDateTime lastLoginTime;

    public UserDo(Long userId, Integer age, String nickName) {
        this.setId(userId);
        this.age = age;
        this.nickName = nickName;
    }

    public UserDo() {

    }
}
