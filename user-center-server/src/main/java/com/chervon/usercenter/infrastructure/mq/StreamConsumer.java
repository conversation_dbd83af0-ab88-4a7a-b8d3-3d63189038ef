package com.chervon.usercenter.infrastructure.mq;

import com.chervon.common.core.utils.StringUtils;
import com.chervon.usercenter.api.dto.sf.SfUserAddDto;
import com.chervon.usercenter.api.dto.sf.SfUserEditDto;
import com.chervon.usercenter.api.service.SaleForceService;
import com.chervon.usercenter.domain.model.user.UserRepository;
import com.chervon.usercenter.infrastructure.converter.UserConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @since 2023-03-24 11:42
 **/
@Slf4j
@Component
public class StreamConsumer {

    @Resource(name = "${sf.direction}")
    private SaleForceService saleForceService;
    @Autowired
    private UserRepository userRepository;

    @Bean
    Consumer<SfUserMessage> sfUser() {
        log.info("初始化sfUser RocketMQ订阅");
        return msg -> {
            log.info("StreamConsumer#sfUser -> stream消费到消息:{}", msg);
            switch (msg.getType()) {
                case ADD:
                    SfUserAddDto sfUserAddDto = UserConverter.userToSfUserAddDto(msg.getUser());
                    String sfUserId = saleForceService.addSfUser(sfUserAddDto);
                    if (StringUtils.isEmpty(sfUserId)) {
                        log.error("StreamConsumer#sfUser -> 增加用户到SF异常,返回sfId为空");
                        break;
                    }
                    userRepository.updateSfUserIdByEmail(msg.getUser().getEmail(), sfUserId);
                    break;
                case UPDATE:
                    SfUserEditDto sfUserEditDto = UserConverter.userToSfUserEditDto(msg.getUser());
                    saleForceService.editSfUser(msg.getUser().getSfUserId(), sfUserEditDto);
                    break;
                case DELETE:
                    saleForceService.deleteSfUser(msg.getUser().getSfUserId());
                    break;
                default:
                    log.error("StreamConsumer#sfUser -> 非法操作类型");
                    // 非法操作类型
                    break;
            }
        };
    }
}
