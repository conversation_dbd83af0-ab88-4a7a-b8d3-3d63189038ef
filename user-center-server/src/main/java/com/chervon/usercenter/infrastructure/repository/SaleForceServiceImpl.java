package com.chervon.usercenter.infrastructure.repository;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.DateUtils;
import com.chervon.common.core.utils.JsonUtils;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.usercenter.api.dto.sf.SfSurveySubmitDto;
import com.chervon.usercenter.api.dto.sf.SfUserAddDto;
import com.chervon.usercenter.api.dto.sf.SfUserEditDto;
import com.chervon.usercenter.api.dto.sf.SfWarrantyRegisterDto;
import com.chervon.usercenter.api.exception.UserCenterErrorCode;
import com.chervon.usercenter.api.service.SaleForceService;
import com.chervon.usercenter.api.vo.sf.*;
import com.chervon.usercenter.config.ExceptionMessageUtil;
import com.chervon.usercenter.domain.constant.UserCenterConstant;
import com.chervon.usercenter.infrastructure.config.SfUserConfig;
import com.chervon.usercenter.infrastructure.entity.UserDo;
import com.chervon.usercenter.infrastructure.mapper.UserMapper;
import com.google.common.base.Strings;
import com.mysql.cj.x.protobuf.MysqlxExpr;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.http.MediaType;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/8 13:52
 */
@Service
@DubboService
@Slf4j
public class SaleForceServiceImpl implements SaleForceService {
    protected static final String SF_DATETIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'Z'";

    private static final String INSTANCE_URL_KEY = "sale:force:instance_url:";
    private static final String ACCESS_TOKEN_KEY = "sale:force:access_token:";

    @Autowired
    private SfUserConfig sfUserConfig;
    @Autowired
    private UserMapper userMapper;

    @Value("${sf.direction}")
    private String direction;

    @Override
    public SfTokenVo getSfToken() {
        return getSfToken(false);
    }

    @Override
    public SfTokenVo getSfToken(Boolean refresh) {
        SfTokenVo result = new SfTokenVo();
        if (refresh.equals(Boolean.FALSE)) {
            String instanceUrl = RedisUtils.getCacheObject(INSTANCE_URL_KEY + direction);
            String accessToken = RedisUtils.getCacheObject(ACCESS_TOKEN_KEY + direction);
            if (StringUtils.hasLength(instanceUrl) && StringUtils.hasLength(accessToken)) {
                result.setAccess_token(accessToken);
                result.setInstance_url(instanceUrl);
                return result;
            }
        }
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            MultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
            map.add("username", sfUserConfig.getUsername());
            map.add("password", sfUserConfig.getPassword());
            map.add("grant_type", sfUserConfig.getGrantType());
            map.add("client_id", sfUserConfig.getClientId());
            map.add("client_secret", sfUserConfig.getClientSecret());
            HttpEntity<MultiValueMap<String, Object>> req = new HttpEntity<>(map, headers);
            RestTemplate restTemplate = getRestTemplate();
            ResponseEntity<SfTokenVo> response = restTemplate.postForEntity(sfUserConfig.getBaseUrl() + sfUserConfig.getOauth2TokenPath(), req, SfTokenVo.class);
            result = response.getBody();
        } catch (Exception e) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_GET_TOKEN_ERROR);
        }
        if (null == result) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_GET_TOKEN_ERROR);
        }
        // 将SF登录获取到的token以及url存入redis
        RedisUtils.setCacheObject(ACCESS_TOKEN_KEY + direction, result.getAccess_token(), Duration.ofHours(2L));
        RedisUtils.setCacheObject(INSTANCE_URL_KEY + direction, result.getInstance_url(), Duration.ofHours(2L));
        return result;
    }

    @Override
    public List<SfHelpFaqRecord> listKnowledgeKav(LocalDateTime syncTime) {
        SfTokenVo sfTokenVo = getSfToken(false);
        RestTemplate restTemplate = getRestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.add("Authorization", "Bearer " + sfTokenVo.getAccess_token());
        try {
            String url = sfTokenVo.getInstance_url() + "/services/data/v54.0/query?q=" +
                    "SELECT Id,Model__c,Title,Solution__c,CreatedDate,LastModifiedDate " +
                    "FROM Knowledge__kav " +
                    "WHERE (Brand__C='EGO' OR BRAND__C=NULL) AND IsVisibleInPkb=TRUE and PublishStatus = 'Online' ";
            if (Objects.nonNull(syncTime)) {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
                simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT+00:00"));
                String syncTimeStr = simpleDateFormat.format(DateUtils.toDate(syncTime));
                url = url + "AND LastModifiedDate >=" + syncTimeStr;
            }

            ParameterizedTypeReference<SfQueryVo<SfHelpFaqRecord>> responseBodyType = new ParameterizedTypeReference<SfQueryVo<SfHelpFaqRecord>>() {
            };
            ResponseEntity<SfQueryVo<SfHelpFaqRecord>> response = restTemplate.exchange(url,
                    HttpMethod.GET,
                    new HttpEntity<String>(headers),
                    responseBodyType);
            List<SfHelpFaqRecord> result = new ArrayList<>();
            if (response.getBody() == null) {
                return result;
            }
            result = ConvertUtil.convertList(response.getBody().getRecords(), SfHelpFaqRecord.class);
            while (!response.getBody().getDone()) {
                String nextRecordsUrl = sfTokenVo.getInstance_url() + response.getBody().getNextRecordsUrl();
                response = restTemplate.exchange(nextRecordsUrl,
                        HttpMethod.GET,
                        new HttpEntity<String>(headers),
                        responseBodyType);
                result.addAll(ConvertUtil.convertList(Objects.requireNonNull(response.getBody()).getRecords(), SfHelpFaqRecord.class));
            }
            return result;
        } catch (Exception e) {
            log.error("获取SF知识库失败: {}", e.getMessage(), e);
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_LIST_KNOWLEDGE_ERROR);
        }
    }

    @Override
    public SfUserRecord getSfUserBySfUserId(String sfUserId) {
        List<SfUserRecord> sfUserRecords = listSfUser("+WHERE+Id='" + sfUserId + "'");
        if (CollectionUtils.isEmpty(sfUserRecords)) {
            return null;
        }
        return sfUserRecords.get(CommonConstant.ZERO);
    }

    @Override
    public SfUserRecord getSfUserBySfEmail(String email) {
        List<SfUserRecord> sfUserRecords = listSfUser("+WHERE+EGO_username__c='" + email + "'");
        if (CollectionUtils.isEmpty(sfUserRecords)) {
            return null;
        }
        return sfUserRecords.get(CommonConstant.ZERO);
    }

    @Override
    public String getUserSyncTime() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT+00:00"));
        Long queryTime = RedisUtils.getCacheObject(UserCenterConstant.SF_SYNC_TIME_USER);
        if (queryTime == null) {
            UserDo userDo = userMapper.selectOne(new LambdaQueryWrapper<UserDo>()
                    .select(UserDo::getLastSyncTime, UserDo::getId)
                    .orderByDesc(UserDo::getLastSyncTime).last("LIMIT 1"));
            if (userDo != null && userDo.getLastSyncTime() != null) {
                queryTime = userDo.getLastSyncTime().toEpochSecond(ZoneOffset.UTC) * 1000L;
            } else {
                queryTime = System.currentTimeMillis() - 300000L;
            }
        }
        return simpleDateFormat.format(new Date(queryTime));
    }

    @Override
    public List<SfUserRecord> listSfUserUpdatedIn5Min() {
        String userSyncTime = getUserSyncTime();
        String whereStr = "+WHERE+EGO_username__c+!=+NULL+and+EGO_password__c+!=+NULL+and+LastModifiedDate+>+" + userSyncTime;
        return listSfUser(whereStr);
    }

    @Override
    public List<SfUserRecord> listSfUser(String whereStr) {
        SfTokenVo sfTokenVo = getSfToken(false);
        RestTemplate restTemplate = getRestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.add("Authorization", "Bearer " + sfTokenVo.getAccess_token());
        String url = sfTokenVo.getInstance_url() + "/services/data/" + sfUserConfig.getApiVersion() + "/query?q=" +
                "SELECT+Id,Name,FirstName,LastName,PersonEmail,EGO_username__c,EGO_password__c,Site_Origin__pc,Trade__c,Trade_Other__c,PersonMobilePhone,Phone,ShippingStreet,ShippingCity,ShippingState,ShippingPostalCode,ShippingCountry,Company__pc,CreatedDate,LastModifiedDate" +
                "+FROM+Account" + whereStr;
        ParameterizedTypeReference<SfQueryVo<SfUserRecord>> responseBodyType = new ParameterizedTypeReference<SfQueryVo<SfUserRecord>>() {
        };
        ResponseEntity<SfQueryVo<SfUserRecord>> response = restTemplate.exchange(url,
                HttpMethod.GET,
                new HttpEntity<String>(headers),
                responseBodyType);
        List<SfUserRecord> result = new ArrayList<>();
        if (null == response.getBody()) {
            return result;
        }
        if (null != response.getBody().getRecords()) {
            result = response.getBody().getRecords();
        }
        while (!response.getBody().getDone()) {
            String nextRecordsUrl = sfTokenVo.getInstance_url() + response.getBody().getNextRecordsUrl();
            response = restTemplate.exchange(nextRecordsUrl,
                    HttpMethod.GET,
                    new HttpEntity<String>(headers),
                    responseBodyType);
            result.addAll(Objects.requireNonNull(response.getBody()).getRecords());
        }
        return result.stream()
                .filter(i -> (com.chervon.common.core.utils.StringUtils.isNotEmpty(i.getUsername())
                        && Pattern.matches("^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$", i.getUsername())))
                .sorted(Comparator.comparing(SfUserRecord::getLastModifiedDate, Comparator.reverseOrder()))
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(SfUserRecord::getUsername))), ArrayList::new));
    }

    @Override
    public String addSfUser(SfUserAddDto sfUserAddDto) {
        if (!StringUtils.hasText(sfUserAddDto.getLastName()) || !StringUtils.hasText(sfUserAddDto.getFirstName())) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_ADD_USER_ERROR, "LastName or firstName are missing");
        }
        //先发起查询sf有没有该用户
        SfUserRecord sfUserRecord = getSfUserBySfEmail(sfUserAddDto.getUsername__c());
        if (Objects.nonNull(sfUserRecord)) {
            return sfUserRecord.getSfUserId();
        }
        final ResponseEntity<SfUserAddVo> response = getSfUserAddVoResponseEntity(sfUserAddDto, false);
        if (response == null) {
            return null;
        }
        return response.getBody().getId();
    }

    @Nullable
    private ResponseEntity<SfUserAddVo> getSfUserAddVoResponseEntity(SfUserAddDto sfUserAddDto, Boolean refresh) {
        try {
            SfTokenVo sfTokenVo = getSfToken(refresh);
            RestTemplate restTemplate = getRestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("Authorization", "Bearer " + sfTokenVo.getAccess_token());
            String url = sfTokenVo.getInstance_url() + "/services/data/" + sfUserConfig.getApiVersion() + "/sobjects/Account";
            ResponseEntity<SfUserAddVo> response = restTemplate.postForEntity(url, new HttpEntity<>(sfUserAddDto, headers), SfUserAddVo.class);
            if (null == response.getBody()) {
                log.error("SaleForceServiceImpl#addSfUser -> failed to create user: return body is empty");
                return null;
            }
            if (!response.getBody().getSuccess()) {
                log.error("SaleForceServiceImpl#addSfUser -> failed to create user: {}", response.getBody().getErrors());
                return null;
            }
            return response;
        } catch (Exception e) {
            if (!refresh && (e.getMessage().contains("INVALID_SESSION_ID") || e.getMessage().contains("Unauthorized"))) {
                return getSfUserAddVoResponseEntity(sfUserAddDto, true);
            }
            return null;
        }
    }

    @Override
    public void editSfUser(String sfUserId, SfUserEditDto sfUserEditDto) {
        SfTokenVo sfTokenVo = getSfToken(false);
        OkHttpClient client = new OkHttpClient.Builder()
                .callTimeout(Duration.ofMillis(UserCenterConstant.SF_USER_SYNC_TIMEOUT))
                .build();
        Headers headers = new Headers.Builder()
                .set("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .add("Authorization", "Bearer " + sfTokenVo.getAccess_token())
                .build();
        String url = sfTokenVo.getInstance_url() + "/services/data/" + sfUserConfig.getApiVersion() + "/sobjects/Account/" + sfUserId;
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse(MediaType.APPLICATION_JSON_VALUE);
        RequestBody body = RequestBody.create(mediaType, JSON.toJSONString(sfUserEditDto));
        Request request = new Request.Builder()
                .url(url).headers(headers)
                .method("PATCH", body)
                .build();
        try {
            client.newCall(request).execute();
        } catch (IOException e) {
            log.error("SaleForceServiceImpl#editSfUser -> failed to edit user: {}", e.getMessage(), e);
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_EDIT_FAIL);
        }

    }

    @Override
    public void deleteSfUser(@NotEmpty String sfUserId) {
        SfTokenVo sfTokenVo = getSfToken(false);
        OkHttpClient client = new OkHttpClient.Builder()
                .callTimeout(Duration.ofMillis(UserCenterConstant.SF_USER_SYNC_TIMEOUT))
                .build();
        Headers headers = new Headers.Builder()
                .set("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .add("Authorization", "Bearer " + sfTokenVo.getAccess_token())
                .build();
        String url = sfTokenVo.getInstance_url() + "/services/data/" + sfUserConfig.getApiVersion() + "/sobjects/Account/" + sfUserId;
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse(MediaType.APPLICATION_JSON_VALUE);
        RequestBody body = RequestBody.create(mediaType, "{\"PersonEmail\": null, \"EGO_username__c\": null , \"EGO_password__c\": null}");
        Request request = new Request.Builder()
                .url(url).headers(headers)
                .method("PATCH", body)
                .build();
        try {
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                log.info("SaleForceServiceImpl#deleteSfUser -> succeed to delete user");
            }
        } catch (IOException e) {
            log.error("SaleForceServiceImpl#deleteSfUser -> failed to delete user: {}", e.getMessage(), e);
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_DELETE_FAIL);
        }
    }

    /**
     * 检查用户是否有关联sf数据，有则删除
     *
     * @param sfUserId
     * @param sfTokenVo
     */
    private void checkAndDeleteSfRelationData(String sfUserId, SfTokenVo sfTokenVo) {
        List<String> sfIds = new ArrayList<>();
        //检查是否有绑定产品质保关系数据
        List<SfCustomerProductQueryVo> sfCustomerProductQueryVoList = listSfCustomerProduct(sfUserId, sfTokenVo);
        sfIds.addAll(sfCustomerProductQueryVoList
                .stream()
                .map(x -> x.getId())
                .collect(Collectors.toList())
        );
        //检查是否有绑定设备关系数据
        List<SfProductInAppQueryVo> sfProductInAppQueryVos = listSfProductInApp(sfUserId, sfTokenVo);
        sfIds.addAll(sfProductInAppQueryVos
                .stream()
                .map(x -> x.getId())
                .collect(Collectors.toList())
        );
        log.info("sfUserId:{} relation data id:{}", sfUserId, sfIds);
        //存在关联数据删除关联数据
        if (!CollectionUtils.isEmpty(sfIds)) {
            deleteCompositeSobjects(sfIds, sfTokenVo);
        }
    }

    private void deleteCompositeSobjects(List<String> ids, SfTokenVo sfTokenVo) {
        RestTemplate restTemplate = getRestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer " + sfTokenVo.getAccess_token());
        String url = sfTokenVo.getInstance_url() + "/services/data/" + sfUserConfig.getApiVersion() + "/composite/sobjects?ids=" + String.join(",", ids);

        try {
            ResponseEntity<String> response = restTemplate.exchange(url,
                    HttpMethod.DELETE,
                    new HttpEntity<String>(headers),
                    String.class);
            if (!response.getStatusCode().is2xxSuccessful()) {
                throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_COMPOSITE_SOBJECTS_DELETE_ERROR);
            }
        } catch (Exception e) {
            log.error("deleteCompositeSobjects error: {}",e.getMessage(), e);
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_COMPOSITE_SOBJECTS_DELETE_ERROR);
        }
    }

    /**
     * 用户产品质保关系数据
     *
     * @param sfUserId
     * @param sfTokenVo
     * @return
     */
    private List<SfCustomerProductQueryVo> listSfCustomerProduct(String sfUserId, SfTokenVo sfTokenVo) {
        RestTemplate restTemplate = getRestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer " + sfTokenVo.getAccess_token());
        String url = sfTokenVo.getInstance_url() + "/services/data/" + sfUserConfig.getApiVersion() + "/query?q=" +
                "SELECT+Id+FROM+Customer_Product__c+WHERE+Customer__c+=+'" + sfUserId + "'";
        try {
            ResponseEntity<SfQueryVo<SfCustomerProductQueryVo>> response = restTemplate.exchange(url,
                    HttpMethod.GET,
                    new HttpEntity<String>(headers),
                    new ParameterizedTypeReference<SfQueryVo<SfCustomerProductQueryVo>>() {
                    });
            List<SfCustomerProductQueryVo> list = response.getBody().getRecords();
            if (!CollectionUtils.isEmpty(list)) {
                return list;
            }
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("SaleForceServiceImpl#listSfCustomerProduct -> error:{}", e.getMessage(), e);
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_LIST_CUSTOMER_PRODUCT_ERROR);
        }
    }


    /**
     * 用户设备关系数据
     *
     * @param sfUserId
     * @param sfTokenVo
     * @return
     */
    private List<SfProductInAppQueryVo> listSfProductInApp(String sfUserId, SfTokenVo sfTokenVo) {
        RestTemplate restTemplate = getRestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer " + sfTokenVo.getAccess_token());
        String url = sfTokenVo.getInstance_url() + "/services/data/" + sfUserConfig.getApiVersion() + "/query?q=" +
                "SELECT+Id,Customer__c,ProductCode__c,ProductName__c,CreatedDate,SerialNumber__c+FROM+Product_in_APP__c+where +Customer__c +=+'" + sfUserId + "'";
        try {
            ResponseEntity<SfQueryVo<SfProductInAppQueryVo>> response = restTemplate.exchange(url,
                    HttpMethod.GET,
                    new HttpEntity<String>(headers),
                    new ParameterizedTypeReference<SfQueryVo<SfProductInAppQueryVo>>() {
                    });
            List<SfProductInAppQueryVo> list = response.getBody().getRecords();
            if (!CollectionUtils.isEmpty(list)) {
                return list;
            }
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("listSfProductInApp error {}", e.getMessage(), e);
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_LIST_PRODUCT_IN_APP_ERROR);
        }
    }


    @Override
    public SfWarrantyRegisterVo registerSfWarranty(SfWarrantyRegisterDto sfWarrantyRegisterDto) {
        sfWarrantyRegisterDto.getRecords().get(0).setSource__c("IOT");
        SfTokenVo sfTokenVo = getSfToken(false);
        RestTemplate restTemplate = getRestTemplate();
        // 请求地址
        String url = sfTokenVo.getInstance_url() + "/services/data/" + sfUserConfig.getApiVersion() + "/composite/tree/Warranty__c";
        // 请求头设置,x-www-form-urlencoded格式的数据
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer " + sfTokenVo.getAccess_token());
        //提交参数设置
        // 组装请求体
        log.info("registerSfWarranty request body is: {}", JsonUtils.toJsonString(sfWarrantyRegisterDto));
        HttpEntity<SfWarrantyRegisterDto> request = new HttpEntity<>(sfWarrantyRegisterDto, headers);
        try {
            SfWarrantyRegisterVo sfWarrantyRegisterVoResult = restTemplate.postForObject(url, request, SfWarrantyRegisterVo.class);
            String sn = sfWarrantyRegisterDto.getRecords().get(0).getWarranty_Items__r().getRecords().get(0).getSerial_Number__c();
            Optional.ofNullable(sfWarrantyRegisterVoResult)
                    .ifPresent(result -> {
                        String message = JsonUtils.toJsonString(result);
                        if (!result.getHasErrors()) {
                            log.info("device sn: {}, registerSfWarranty success: {}", sn, message);
                        } else {
                            log.error("device sn: {}, registerSfWarranty error: {}", sn, message);
                        }
                    });
            return sfWarrantyRegisterVoResult;
        } catch (Exception e) {
            log.error("registerSfWarranty error: {}", e.getMessage(), e);
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_REGISTER_WARRANTY_ERROR, request.toString());
        }
    }

    @Override
    public SfWarrantyInfoVo getSfWarrantyInfo(String sn) {
        SfTokenVo sfTokenVo = getSfToken(false);
        RestTemplate restTemplate = getRestTemplate();
        // 请求地址
        String url = sfTokenVo.getInstance_url() + "/services/data/" + sfUserConfig.getApiVersion() + "/query";
        url += "?q=SELECT Expiration_Date__c,Purchase_Date__c,Place_of_Purchase__c, Lost_Receipt__c, Gift__c," +
                "Product_Use_Type__c,Product_Use_Type2__c  WHERE Id IN (SELECT Warranty__c FROM Warranty_Item__c" +
                " WHERE Serial_Number__c = '";
        url += sn;
        url += "')";

        // 请求头设置,x-www-form-urlencoded格式的数据
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", "Bearer " + sfTokenVo.getAccess_token());
        HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(headers);
        try {
            ResponseEntity<SfWarrantyInfoVo> response = restTemplate.exchange(url, HttpMethod.GET, request, SfWarrantyInfoVo.class);
            log.info(response.getBody().toString());
            return response.getBody();
        } catch (Exception exception) {
            log.error("getSfWarrantyInfo error: {}", exception.getMessage(), exception);
            return null;
        }
    }

    @Override
    public List<SfProductQueryVo> listSfProduct(String productModel, String Country_of_Origin__c) {
        SfTokenVo sfTokenVo = getSfToken();
        RestTemplate restTemplate = getRestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.add("Authorization", "Bearer " + sfTokenVo.getAccess_token());
        String url = sfTokenVo.getInstance_url() + "/services/data/" + sfUserConfig.getApiVersion() + "/query?q=" +
                "SELECT Id,NAME,ProductCode,RecordType.Name,CreatedDate,Country_of_Origin__c " +
                "FROM Product2 " +
                "WHERE ProductCode='" + productModel + "' AND " +
                "IsActive = TRUE AND " +
                "Brand_Name__c = 'EGO' AND " +
                "Source__c = 'EBS' AND " +
//                "RecordType.Name = 'Product' AND " +
                "Country_of_Origin__c = '" + Country_of_Origin__c + "'";
        ParameterizedTypeReference<SfQueryVo<SfProductQueryVo>> responseBodyType = new ParameterizedTypeReference<SfQueryVo<SfProductQueryVo>>() {
        };
        try {
            ResponseEntity<SfQueryVo<SfProductQueryVo>> response = restTemplate.exchange(url,
                    HttpMethod.GET,
                    new HttpEntity<String>(headers),
                    responseBodyType);
            List<SfProductQueryVo> list = ConvertUtil.convertList(Objects.requireNonNull(response.getBody()).getRecords(), SfProductQueryVo.class);
            if (!CollectionUtils.isEmpty(list)) {
                return list;
            }
            return null;
        } catch (Exception e) {
            log.error("listSfProduct error:{}", e.getMessage(), e);
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_LIST_PRODUCT_ERROR);
        }
    }

    @Override
    public List<String> getWarrantyEmailBySn(String sn) {
        SfTokenVo sfTokenVo = getSfToken(false);
        RestTemplate restTemplate = getRestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.add("Authorization", "Bearer " + sfTokenVo.getAccess_token());
        String url = sfTokenVo.getInstance_url() + "/services/data/" + sfUserConfig.getApiVersion() + "/query?q=" +
                "SELECT+Warranty__r.AccountCustomer__r.personemail+from+warranty_item__c+WHERE+Serial_Number__c+=+'" + sn + "'";
        ParameterizedTypeReference<SfQueryVo<JSONObject>> responseBodyType = new ParameterizedTypeReference<SfQueryVo<JSONObject>>() {
        };
        try {
            ResponseEntity<SfQueryVo<JSONObject>> response = restTemplate.exchange(url,
                    HttpMethod.GET,
                    new HttpEntity<String>(headers),
                    responseBodyType);
            List<JSONObject> records = Objects.requireNonNull(response.getBody()).getRecords();
            if (CollectionUtils.isEmpty(records)) {
                return new ArrayList<>();
            }
            return records.stream().map(e -> {
                if (e.containsKey("Warranty__r")) {
                    JSONObject warranty__r = e.getJSONObject("Warranty__r");
                    if (warranty__r.containsKey("AccountCustomer__r")) {
                        JSONObject accountCustomer__r = warranty__r.getJSONObject("AccountCustomer__r");
                        if (accountCustomer__r.containsKey("PersonEmail")) {
                            return accountCustomer__r.getString("PersonEmail");
                        }
                    }
                }
                return null;
            }).filter(StringUtils::hasLength).distinct().collect(Collectors.toList());
        } catch (Exception e) {
            log.error("SaleForceServiceImpl#getWarrantyEmailBySn -> failed to obtain maintenance email:{}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }


    @Override
    public void submitSurvey(SfSurveySubmitDto sfSurveySubmitDto) {
        SfTokenVo sfTokenVo = getSfToken(false);
        RestTemplate restTemplate = getRestTemplate();
        // 兼容 application/octet-stream
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();

        converter.setSupportedMediaTypes(Arrays.asList(MediaType.parseMediaType("text/plain;charset=utf-8"), MediaType.APPLICATION_OCTET_STREAM));
        restTemplate.getMessageConverters().add(converter);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer " + sfTokenVo.getAccess_token());
        String url = sfTokenVo.getInstance_url() + "/services/apexrest/CCM_SurveyProcessor/surveyService/*";
        ResponseEntity<byte[]> response = restTemplate.postForEntity(url, new HttpEntity<>(sfSurveySubmitDto, headers), byte[].class);
        if (Objects.isNull(response.getBody())) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_SUBMIT_SURVEY_ERROR, "return body is empty");
        }
        String res = new String(response.getBody());
        log.info("submitSurvey result is{}", res);
        try {
            List resList = JsonUtils.parseObject(res, List.class);
            if (CollectionUtils.isEmpty(resList)) {
                throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_SUBMIT_SURVEY_ERROR, "list is empty");

            }
        } catch (Exception e) {
            log.error("submitSurvey error:{}", e.getMessage(), e);
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_SUBMIT_SURVEY_ERROR, "can't transfer list");
        }

        if (!response.getStatusCode().is2xxSuccessful()) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_SUBMIT_SURVEY_ERROR, "http not 200");

        }
    }

    /**
     * 设置超时时间来控制请求的超时行为
     *
     * @return SimpleClientHttpRequestFactory
     */
    public RestTemplate getRestTemplate() {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(UserCenterConstant.DEVICE_SYNC_SF_TIMEOUT);
        requestFactory.setReadTimeout(UserCenterConstant.DEVICE_SYNC_SF_TIMEOUT);
        return new RestTemplate(requestFactory);
    }

    @Override
    public List<SfWarrantyRecord> listSfWarrantyLastUpdated(Long startTime) {
        SfTokenVo sfTokenVo = getSfToken();
        RestTemplate restTemplate = getRestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.add("Authorization", "Bearer " + sfTokenVo.getAccess_token());
        String timeQueryStr = DateFormatUtils.format(startTime, SF_DATETIME_FORMAT, TimeZone.getTimeZone("GMT+00:00"));
        String url = sfTokenVo.getInstance_url() + "/services/data/" + sfUserConfig.getApiVersion() + "/query?q=" +
                "SELECT+Id,Receipt_Status__c,Receipt_Link__c,Consumer__c,Product_Use_Type2__c,Purchase_Place__c," +
                "Purchase_Date__c,Serial_Number__c,LastModifiedDate" +
                "+FROM+Warranty_Item__c+WHERE+Consumer__c+!=+NULL+AND+Brand__c+=+'EGO'+AND+LastModifiedDate+>=+" + timeQueryStr;
        ParameterizedTypeReference<SfQueryVo<SfWarrantyRecord>> responseBodyType = new ParameterizedTypeReference<SfQueryVo<SfWarrantyRecord>>() {
        };
        ResponseEntity<SfQueryVo<SfWarrantyRecord>> response = restTemplate.exchange(url,
                HttpMethod.GET,
                new HttpEntity<String>(headers),
                responseBodyType);
        List<SfWarrantyRecord> result = new ArrayList<>();
        if (null == response.getBody()) {
            return result;
        }
        if (null != response.getBody().getRecords()) {
            result = response.getBody().getRecords();
        }
        while (!response.getBody().getDone()) {
            String nextRecordsUrl = sfTokenVo.getInstance_url() + response.getBody().getNextRecordsUrl();
            response = restTemplate.exchange(nextRecordsUrl,
                    HttpMethod.GET,
                    new HttpEntity<String>(headers),
                    responseBodyType);
            result.addAll(Objects.requireNonNull(response.getBody()).getRecords());
        }

        return result.stream()
                .filter(warranty -> com.chervon.common.core.utils.StringUtils.isNotEmpty(warranty.getSn())
                        && com.chervon.common.core.utils.StringUtils.isNotEmpty(warranty.getSfUserId()))
                .sorted(Comparator.comparing(SfWarrantyRecord::getLastModifiedDate))
                .collect(Collectors.toList());
    }

    @Override
    public void updateUserSyncTime(Long time) {
        RedisUtils.setCacheObject(UserCenterConstant.SF_SYNC_TIME_USER, time);
    }

    @Override
    public void updateWarrantySyncTime(Long time) {
        RedisUtils.setCacheObject(UserCenterConstant.SF_SYNC_TIME_WARRANTY, time);
    }

    @Override
    public Long getWarrantySyncTime() {
        return RedisUtils.getCacheObject(UserCenterConstant.SF_SYNC_TIME_WARRANTY);
    }

    @Override
    public SfQueryVo<SfWarrantyRecord> batchSfWarranty(String queryUrl) {
        SfTokenVo sfTokenVo = getSfToken();
        RestTemplate restTemplate = getRestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.add("Authorization", "Bearer " + sfTokenVo.getAccess_token());
        if (Strings.isNullOrEmpty(queryUrl)) {
            queryUrl = "/services/data/" + sfUserConfig.getApiVersion() + "/query?q=" +
                    "SELECT+Id,Receipt_Status__c,Receipt_Link__c,Consumer__c,Product_Use_Type2__c,Purchase_Place__c," +
                    "Purchase_Date__c,Serial_Number__c,LastModifiedDate" +
                    "+FROM+Warranty_Item__c+WHERE+Consumer__c+!=+NULL+AND+Brand__c+=+'EGO'";
        }
        String url = sfTokenVo.getInstance_url() + queryUrl;
        try {
            ResponseEntity<SfQueryVo<SfWarrantyRecord>> response = restTemplate.exchange(url,
                    HttpMethod.GET,
                    new HttpEntity<String>(headers),
                    new ParameterizedTypeReference<SfQueryVo<SfWarrantyRecord>>() {
                    });
            if (null == response.getBody()) {
                return null;
            }
            return response.getBody();
        } catch (Exception e) {
            log.error("listSfWarranty, queryUrl:{}, error: ", queryUrl, e);
            return null;
        }
    }

    @Override
    public List<SfWarrantyRecord> getWarrantyBySn(String sn) {
        return null;
    }

    @Override
    public List<SfWarrantyRecord> getWarrantyByUser(String sfUserId) {
        return null;
    }

    @Override
    public String getSfWarrantyId(@NotEmpty String sn) {
        SfTokenVo sfTokenVo = getSfToken();
        RestTemplate restTemplate = getRestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.add("Authorization", "Bearer " + sfTokenVo.getAccess_token());
        String url = sfTokenVo.getInstance_url() + "/services/data/" + sfUserConfig.getApiVersion() + "/query?q=" +
                "SELECT+Id,Consumer__c+FROM+Warranty_Item__c+WHERE+Brand__c+=+'EGO'+AND+Serial_Number__c='" + sn + "'";
        try {
            ResponseEntity<SfQueryVo<SfWarrantyRecord>> response = restTemplate.exchange(url,
                    HttpMethod.GET,
                    new HttpEntity<String>(headers),
                    new ParameterizedTypeReference<SfQueryVo<SfWarrantyRecord>>() {
                    });
            SfQueryVo<SfWarrantyRecord> result = response.getBody();
            if (null == result || result.getTotalSize() <= 0) {
                return null;
            }
            return response.getBody().getRecords().get(0).getId();
        } catch (Exception e) {
            log.error("getSfWarrantyId, url:{}, error: ", url, e);
            return null;
        }
    }
}
