package com.chervon.common.dubbo.filter;

import cn.hutool.core.util.IdUtil;
import com.chervon.common.core.utils.SnowFlake;
import com.chervon.common.core.utils.StringUtils;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;
import org.slf4j.MDC;

/**
 * 日志染色
 * <AUTHOR>
 */
@Activate(group = {CommonConstants.PROVIDER},order = 1)
public class ProviderRpcTraceFilter implements Filter {

    /**
     *
     * @param invoker
     * @param invocation
     * @return
     * @throws org.apache.dubbo.rpc.RpcException
     */
    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        String traceId = RpcContext.getServiceContext().getAttachment("traceId");
        if (StringUtils.isBlank(traceId)) {
            traceId = this.getUUID() ;
        }
        //设置日志traceId变量
        MDC.put("traceId", traceId);
        RpcContext.getServiceContext().setAttachment("traceId", traceId);
        try{
            return invoker.invoke(invocation);
        }finally {
            MDC.remove("traceId");
        }
    }

    /**
     * 获取UUID
     * @return String UUID
     */
    public String getUUID(){
        String uuid = String.valueOf(SnowFlake.nextId());
        //替换-字符
        return uuid;
    }

}


