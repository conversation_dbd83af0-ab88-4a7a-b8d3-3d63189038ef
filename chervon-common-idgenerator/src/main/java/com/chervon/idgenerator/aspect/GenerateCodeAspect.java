package com.chervon.idgenerator.aspect;

import com.chervon.idgenerator.annotation.GenerateCode;
import com.chervon.idgenerator.entity.CodeMetadata;
import com.chervon.idgenerator.generator.impl.DefaultCodeGenerator;
import com.chervon.idgenerator.service.CodeMetadataService;
import com.chervon.idgenerator.entity.CodeGeneratorContext;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.HashSet;
import java.util.Set;

/**
 * 
 * @date 2022/09/11
 */
@Component
@Aspect
public class GenerateCodeAspect {
    @Autowired
    private CodeMetadataService codeMetadataService;
    @Autowired
    private DefaultCodeGenerator codeGenerator;

    private Set<String> keySet = new HashSet<>();

    @Around("@annotation(com.chervon.idgenerator.annotation.GenerateCode)")
    public Object contextManage(ProceedingJoinPoint joinPoint) {
        GenerateCode generateCode = ((MethodSignature) joinPoint.getSignature()).getMethod().getAnnotation(GenerateCode.class);
        String key = generateCode.key();
        if (!keySet.contains(key)) {
            synchronized (GenerateCodeAspect.class) {
                if (!keySet.contains(key)) {
                    boolean isReset = generateCode.isReset();
                    long len = generateCode.len();
                    CodeMetadata codeMetadata = new CodeMetadata()
                            .setKey(key)
                            .setIsReset(isReset)
                            .setSegmentLen(len);
                    codeMetadataService.verifyHasKeyMetadata(codeMetadata);
                    keySet.add(key);
                }
            }
        }

        Object[] parameters = joinPoint.getArgs();
        String[] names = ((MethodSignature) joinPoint.getSignature()).getParameterNames();
        try (CodeGeneratorContext ctx = CodeGeneratorContext.getInstance()) {
            ctx.put("key", key);
            if (parameters.length > 0) {
                for (int i = 0; i < parameters.length; i++) {
                    ctx.put(names[i], parameters[i]);
                }
            }
            return codeGenerator.generator(generateCode.template());
        }
    }
}
