package com.chervon.idgenerator.generator.impl;

import com.chervon.idgenerator.exception.GenerateCodeException;
import com.chervon.idgenerator.generator.IdGenerator;
import com.chervon.idgenerator.entity.CodeGeneratorContext;
import com.chervon.idgenerator.entity.CodeSegment;
import com.chervon.idgenerator.service.CodeSegmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 分段ID生成器
 *
 * 
 * @date 2022/09/11
 */
@Component
public class SegmentIdGenerator implements IdGenerator {
    private static final Map<String, CodeSegment> CODE_SEGMENT_MAP = new ConcurrentHashMap<>();
    private static final Map<String, String> KEY_MAP = new ConcurrentHashMap<>();

    @Autowired
    private CodeSegmentService codeSegmentService;

    /**
     * 生成编号
     *
     * @param template 编号模板
     * @return
     */
    @Override
    public Long generator(String template) {
        String key = getCodeMetadataKey(template);
        if (!StringUtils.hasText(key)) {
            throw new GenerateCodeException("Generator code key not provided.");
        }
        return next(key);
    }

    /**
     * 获取编码元数据的key
     *
     * @param template
     * @return
     */
    private String getCodeMetadataKey(String template) {
        String key = KEY_MAP.get(template);
        if (null != key) {
            return key;
        }
        String[] keyParts = template.split(":");
        if (keyParts.length > 1) {
            key = keyParts[1];
            if (key.startsWith("{") && key.endsWith("}")) {
                Pattern pattern = Pattern.compile("\\{(\\??(\\w+))}");
                Matcher matcher = pattern.matcher(key);
                if (matcher.find()) {
                    String group1 = matcher.group(1);
                    key = matcher.group(2);
                    if (group1.startsWith("?")) {
                        key = CodeGeneratorContext.getInstance().get(key);
                    } else {
                        KEY_MAP.put(template, key);
                    }
                }
            }
            return key;
        }
        throw new GenerateCodeException("Generator code template style error.");
    }

    /**
     * 下一个
     *
     * @return
     */
    public Long next(String key) {
        CodeSegment segment = getSegmentCache(key);

        return segment.next(key);
    }

    /**
     * 获取编码块
     *
     * @param key
     * @return
     */
    private CodeSegment getSegmentCache(String key) {
        CodeSegment segment = CODE_SEGMENT_MAP.get(key);
        if (segment == null) {
            synchronized (SegmentIdGenerator.class) {
                segment = CODE_SEGMENT_MAP.get(key);
                if (segment == null) {
                    segment = codeSegmentService.applySegment(key);
                    CODE_SEGMENT_MAP.put(key, segment);
                }
            }
        }

        if (segment.getSize() <= 0 || segment.isExpired()) {
            segment = CODE_SEGMENT_MAP.get(key);
            synchronized (segment) {
                if (segment.getSize() <= 0 || segment.isExpired()) {
                    codeSegmentService.applySegment(key, segment);
                }
            }
        }
        return segment;
    }
}
