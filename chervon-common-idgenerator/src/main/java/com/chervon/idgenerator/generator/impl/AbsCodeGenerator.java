package com.chervon.idgenerator.generator.impl;

import com.chervon.idgenerator.generator.CodeGenerator;
import com.chervon.idgenerator.entity.CodeBlock;
import com.chervon.idgenerator.parse.TemplateParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 编码生成器抽象类
 *
 *
 * @date 2022/09/11
 */
@Slf4j
public abstract class AbsCodeGenerator<T> implements CodeGenerator<T> {

    /**
     * 模版解析器
     */
    @Autowired
    protected TemplateParser templateParser;

    /**
     * 生成编号
     *
     * @param template 编号模板
     * @return
     */
    @Override
    public T generator(String template) {
        List<CodeBlock> codeBlockList = templateParser.parse(template);
        return generator(codeBlockList);
    }

    /**
     * 生成编号
     *
     * @param codeBlockList 编号块
     * @return
     */
    protected abstract T generator(List<CodeBlock> codeBlockList);
}
