package com.chervon.idgenerator.provider.impl;

import com.chervon.idgenerator.entity.CodeBlock;
import com.chervon.idgenerator.entity.CodeBlockStyle;
import com.chervon.idgenerator.entity.CodeGeneratorContext;
import com.chervon.idgenerator.exception.GenerateCodeException;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.regex.Matcher;

/**
 * 文本提供者
 *
 *
 * @date 2022/09/11
 */
@Component
public class TextProvider extends AbsCodeBlockValueProvider<String> {
    /**
     * 样式正则
     */
    private static final String STYLE_REGEX = "^\\{(?<value1>[^\\?\\{}\\s]+)}$|^\\{\\?(?<value2>[^\\?\\{}\\s]+)}$";

    /**
     * 获取提供者名称
     *
     * @return
     */
    @Override
    public String getName() {
        return "text";
    }

    /**
     * 计算值
     *
     * @param codeBlock
     * @return
     */
    @Override
    protected String computeValue(CodeBlock codeBlock) {
        return (String) codeBlock.getCodeBlockStyle().getValue();
    }

    /**
     * 解析模板样式信息
     * <p>
     * PS: support format
     * text
     * text:{value}
     * text:{?value}
     *
     * @param style 样式
     * @return
     */
    @Override
    public CodeBlockStyle parse(String style) {
        //默认情况
        if (!StringUtils.hasText(style)) {
            return new CodeBlockStyle().setValue(EMPTY_STR);
        }
        Matcher matcher = regexMatcher(style, STYLE_REGEX);
        if (matcher.find()) {
            String value = matcher.group(REGEX_VALUE_1);
            if (value == null) {
                String key = matcher.group(REGEX_VALUE_2);
                value = CodeGeneratorContext.getInstance().get(key);
            }
            return new CodeBlockStyle().setValue(value);
        }
        throw new GenerateCodeException("[text]:Parsing codeBlockStyle failed, codeBlockStyle format does not match.");
    }
}
