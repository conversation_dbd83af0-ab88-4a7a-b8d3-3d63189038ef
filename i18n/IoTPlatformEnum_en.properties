operationDeviceTypeSearch=[{"label":"gatewaySubDevice", "description":"gateway sub-device"},{"label":"oldIotDevice", "description":"old IoT devices"},{"label":"directConnectedDevice", "description":"directly connected devices"},{"label":"gatewayDevice", "description":"gateway device"},{"label":"notIotDevice", "description":"non-IoT devices"}]
logTransferType=[{"label":"DOWN", "description":"down"},{"label":"UP", "description":"up"},{"label":"UNKNOWN", "description":"unknown"}]
logStatus=[{"label":"SUCCESS","description":"success"},{"label":"FAIL","description":"fail"}]
businessType=[{"label":"1","description":"EGO Connect"},{"label":"2","description":"Fleet Service"}]
messageType=[{"label":"0","description":"System Message"},{"label":"1","description":"Marketing Message"},{"label":"2","description":"Device Message"}]
pushMethod=[{"label":"POPUP","description":"pop-up"},{"label":"MESSAGE","description":"message"},{"label":"BANNER","description":"banner"},{"label":"MAIL","description":"email"},{"label":"TOMBSTONE","description":"tombstone"},{"label":"PHONE_VOICE","description":"voice"}]
multiCodeStatus=[{"label":"0","description":"disuse"},{"label":"1","description":"normal"}]
thingModelEventType=[{"label":"warn","description":"warn"},{"label":"error","description":"error"},{"label":"info","description":"info"}]
deviceOnlineStatus=[{"label":"ONLINE","description":"online"},{"label":"OFFLINE","description":"offline"}]
deviceStatus=[{"label":"DISABLE","description":"Fault"},{"label":"NORMAL","description":"Normal"}]
deviceActiveStatus=[{"label":"ACTIVE","description":"active"},{"label":"INACTIVE","description":"inactive"}]
c_ota_fail=[{"label":"0","description":"update successed"},{"label":"1","description":"download failed"},{"label":"2","description":"lack of space"},{"label":"3","description":"failure of unknown causes"},{"label":"4","description":"during upgrade"},{"label":"5","description":"pending upgrade"},{"label":"6","description":"preparing for reboot (for Bluetooth modules)"},{"label":"7","description":"IAP upgrade incomplete"},{"label":"8","description":"product number node mismatch"},{"label":"9","description":"packet too large"},{"label":"10","description":"exceeds the size of the receiver's flash"},{"label":"11","description":"receiver failed to write flash"},{"label":"12","description":"upgrade command timeout not replied"},{"label":"13","description":"watermark command timeout not replied"},{"label":"14","description":"confirmation of escalation message timeout without reply"},{"label":"15","description":"packet repetition timeout"},{"label":"16","description":"node end command timeout not replied"},{"label":"17","description":"download file crc verification failed"}]
accessMode=[{"label":"r","description":"ReadOnly"},{"label":"rw","description":"WriteAndRead"},{"label":"w","description":"WriteOnly"}]