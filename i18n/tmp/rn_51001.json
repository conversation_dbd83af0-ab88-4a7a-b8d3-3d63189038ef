{"no": {"rn_51001_smartexitdockingstation_note_textview_text": "Merk: <PERSON><PERSON> rygger langs ledningen til inngangsavstanden og drar deretter for å klippe!", "rn_51001_modify_validate_textview_text": "Sluttidspunktet kan ikke være senere enn starttidspunktet", "rn_51001_panelhome_mailsemptyerror_textview_text": "E-posten er tom, bekreftelse mislyktes", "rn_51001_geofencesensitivity_medium_textview_text": "Medium", "rn_51001_modify_conflict_textview_text": "Konflikt med gjeldende periode; vennligst tilbakestill", "rn_51001_panelhome_status40_textview_text": "Lading fullført - Venter på neste klippeplan", "rn_51001_resetrobotpwd_toregister_button_text": "Å registrere", "rn_51001_statistics_totalworkinghours_textview_text": "Totale driftstimer", "rn_51001_panelhome_updatemapfailnote_textview_text": "<PERSON><PERSON><PERSON><PERSON>, den aktuella robotstatusen tillåter inte återställning av kartan, vänligen återställ gräsklipparen först.", "rn_51001_resetrobotpwd_done_button_text": "<PERSON><PERSON><PERSON>", "rn_51001_batterydetail_title_textview_text": "Batteriinformasjon", "rn_51001_panelhome_status45_textview_text": "Lading fullført - Lokket åpent", "rn_51001_detaillist_cutheight_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_geofencesensitivity_high_textview_text": "Hø<PERSON>", "rn_51001_backhomecorridor_widthleft_textview_text": "Korridorbredde", "rn_51001_resetrobotpwd_unregisteredtip4_textview_text": "Eller du kan kontakte forhand<PERSON>en din for å få hjelp.", "rn_51001_modify_addtime_button_text": "Legg til tid", "rn_51001_statistics_energysavingdata_textview_text": "Energisparedata", "rn_51001_statistics_totaldistance_textview_text": "Total tilbakelagt distanse", "rn_51001_modify_sunday_textview_text": "<PERSON><PERSON><PERSON>g", "rn_51001_panelhome_status21_textview_text": "Returnerer - <PERSON><PERSON><PERSON> batteri", "rn_51001_panelhome_interfaceerror_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>ert, feil rapportert", "rn_51001_modal_slideronOff_textview_text": "På / av", "rn_51001_cutmode_spiral_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_override_48_textview_text": "48 timer", "rn_51001_panelhome_parktiphour_textview_text": "Innstillingstiden må være større enn gjeldende tid enn én time", "rn_51001_panelhome_updatemapmsg_textview_text": "<PERSON> återställer en karta. Den nya kartan kommer att ersätta den nuvarande. Vill du fortsätta?", "rn_51001_headlight_white_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_panelhome_status30_textview_text": "<PERSON>der", "rn_51001_panelhome_status22_textview_text": "Retur - Regner", "rn_51001_resetrobotpwd_successtip1_textview_text": "Robotpassordet ditt har blitt tilbakestilt.", "rn_51001_resetrobotpwd_pwdnotcompleted_textview_text": "Vennligst skriv inn bekreftelseskoden", "rn_51001_resetrobotpwd_failtip2_textview_text": "<PERSON><PERSON><PERSON> for at du har en aktiv tilkobling", "rn_51001_drivepastwire_note_textview_text": "Notera: <PERSON><PERSON> är det avstånd som robotens front kan röra sig förbi begränsningskabeln.", "rn_51001_cutheight_inch_textview_text": "<PERSON><PERSON> (/tommer)", "rn_51001_modify_sun_button_text": "SOL", "rn_51001_modify_wednesday_textview_text": "Onsdag", "rn_51001_panelhome_otaalertmsg_textview_text": "<PERSON><PERSON> du ikke oppdaterer, vil ikke enheten fungere som den skal. Vennligst oppdater nå.", "rn_51001_modify_tue_button_text": "TIR", "rn_51001_resetrobotpwd_successtip2start_textview_text": "Vennligst bruk", "rn_51001_detaillist_antitheft_textview_text": "Innstillinger for tyverisikring", "rn_51001_statistics_totalchargenote_textview_text": "Den totale ladetiden er tiden som klipperen bruker på ladestasjonen. Vær oppmerksom på at ladetiden ikke er inkludert i de totale driftstimer.", "rn_51001_statistics_energynote_textview_text": "Sammenlignet med andre produkter produserer EGO-klipperen mindre totalt karbondioksid under drift.", "rn_51001_panelhome_status50_textview_text": "Bygningskart - Hovedområde", "rn_51001_override_24_textview_text": "24 timer", "rn_51001_statistics_batterydata_textview_text": "Batterida<PERSON>", "rn_51001_resetrobotpwd_resettipsend_textview_text": ". Vennligst skriv inn koden nedenfor for å bekrefte e-postadressen din.", "rn_51001_resetrobotpwd_unregistertip_button_text": "Tyvärr! \n<PERSON><PERSON><PERSON> s<PERSON>ts skull måste din robot först registreras och\n sedan kan du återställa lösenordet här.\neller så kan du kontakta din återförsäljare för hjälp.", "rn_51001_statistics_chargingcyclenote_textview_text": "Ladesyklusen viser antall ganger batteriet har vært fulladet og utladet.", "rn_51001_headlight_off_textview_text": "Alltid AV", "rn_51001_headlight_green_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_sucesstitle_textview_text": "<PERSON><PERSON> robot<PERSON>ord har återställts framgångsrikt. Använd \"0000\" för att starta roboten Om problemet kvarstår kan du kontakta en återförsäljare.", "rn_51001_resetrobotpwd_failtip3_textview_text": "tilko<PERSON> til klipperen, enten ved hjelp av Bluetooth", "rn_51001_backhomecorridor_note_textview_text": "Notera: <PERSON><PERSON> <PERSON>r bredden mellan roboten och begränsningskabeln när roboten återvänder. <PERSON><PERSON><PERSON> bredden så stor som möjligt för att minska linjerna i gräsmattan.", "rn_51001_panelhome_status44_textview_text": "Lading fullført - Venter på start", "rn_51001_resetrobotpwd_failtip_textview_text": "<PERSON><PERSON><PERSON><PERSON>, återställning av lösenord misslyckades. Se till att du har en aktiv anslutning till din gräsklippare, antingen via Bluetooth eller nätverket.", "rn_51001_alarmsetting_duration_textview_text": "Varighet av alarm (/min)", "rn_51001_override_note_textview_text": "Roboten vil klippe plenen kontinuerlig basert på innstillingen din.", "rn_51001_statistics_batteryhealth_textview_text": "Batterihelse", "rn_51001_dashboard_exit_textview_text": "Exit", "rn_51001_timerange_end_textview_text": "<PERSON><PERSON> ferdig", "rn_51001_otherslist_headlight_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_modify_all_textview_text": "Velg alle", "rn_51001_resetrobotpwd_finddealer_button_text": "Finn forhandler", "rn_51001_panelhome_status41_textview_text": "Lading fullført - Klippeplan er ikke angitt", "rn_51001_resetrobotpwd_fail_textview_text": "Mislyktes", "rn_51001_cutmode_workarea_textview_text": "Arbeidsplass", "rn_51001_override_72_textview_text": "72 timer", "rn_51001_resetrobotpwd_failtip4_textview_text": "eller mobilnettet.", "rn_51001_panelhome_updatemapfailnote4_textview_text": "<PERSON><PERSON><PERSON><PERSON>, den aktuella robotstatusen tillåter inte återställning av kartan, vänligen återställ gräsklipparen först.", "rn_51001_cutmode_econote_textview_text": "Merk: <PERSON><PERSON> roboten oppdager at plenen er klippet, vil den kjøre fort over den til den når uklippet gress!", "rn_51001_resetrobotpwd_success_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_panelhome_mainarea_textview_text": "Hovedområde", "rn_51001_panelhome_status11_textview_text": "Pause - Stopp", "rn_51001_modify_saturday_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_backhomecorridor_width_textview_text": "Korridorbredde (/mm)", "rn_51001_panelhome_pickerlefttitle_textview_text": "dato", "rn_51001_override_done_button_text": "<PERSON><PERSON><PERSON>", "rn_51001_cutmode_workareasecond_textview_text": "<PERSON>", "rn_51001_modify_delete_button_text": "<PERSON><PERSON>", "rn_51001_alarmsetting_switch_textview_text": "Alarminnstilling", "rn_51001_modify_fri_button_text": "FRI", "rn_51001_statistics_totaltravelnote_textview_text": "Den totale kjøreavstanden er avstanden som klipperen har tilbakelagt i alle moduser.", "rn_51001_resetrobotpwd_problemsolved_button_text": "<PERSON> løst", "rn_51001_smartexitdockingstation_title_textview_text": "Gå ut av dokkingstasjonen", "rn_51001_panelhome_status25_textview_text": "Retur - Utenfor <PERSON>", "rn_51001_detaillist_others_textview_text": "<PERSON>", "rn_51001_panelhome_mapsettings_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_panelhome_updatetitle_textview_text": "<PERSON><PERSON> op<PERSON> tilg<PERSON>g", "rn_51001_panelhome_status14_textview_text": "Pause - Justering av klippehøyde", "rn_51001_detaillist_functionsettings_textview_text": "Funksjon<PERSON><PERSON><PERSON><PERSON>", "rn_51001_panelhome_status27_textview_text": "Kart over retur-gjeno<PERSON>by<PERSON>", "rn_51001_backdistance_titleleft_textview_text": "Av<PERSON> bak", "rn_51001_panelhome_parktipmax_textview_text": "Innstillingstiden skal ikke overstige 99 timer", "rn_51001_panelhome_updatemapfailnote7_textview_text": "<PERSON><PERSON><PERSON><PERSON>, den aktuella robotstatusen tillåter inte återställning av kartan, vänligen återställ gräsklipparen först.", "rn_51001_dashboard_modalmessage_textview_text": "Trykk begge knappene samtidig for å starte", "rn_51001_guardagainsttheft_alarmsetting_textview_text": "Alarminnstilling", "rn_51001_panelhome_devicedidreceivecmd_textview_text": "Enheten mottok kommandoen for tilbakestilling av passord", "rn_51001_detaillist_bordermanagement_textview_text": "Grenseforvaltning", "rn_51001_panelhome_pickerrighttitle_textview_text": "minutt", "rn_51001_panelhome_updatemapfailnote6_textview_text": "<PERSON><PERSON><PERSON><PERSON>, den aktuella robotstatusen tillåter inte återställning av kartan, vänligen återställ gräsklipparen först.", "rn_51001_cutmode_sys_textview_text": "Systematisk skjæring", "rn_51001_panelhome_status26_textview_text": "Retur-ringes av ladestasjon", "rn_51001_schedulemanagement_title_textview_text": "Tidsplanstyring", "rn_51001_dashboard_alert2subtitle_textview_text": "Klipperadius (/ m)", "rn_51001_panelhome_mowingpath_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_detaillist_availablesettings_textview_text": "Tilgjengelige innstillinger", "rn_51001_panelhome_status13_textview_text": "Pause - <PERSON><PERSON>", "rn_51001_bordermanagement_title_textview_text": "Grenseforvaltning", "rn_51001_guardagainsttheft_resetrobotpwd_textview_text": "Tilbakestill robotpassord", "rn_51001_guardagainsttheft_geofencesetting_textview_text": "Geofence-innstillinger", "rn_51001_panelhome_status12_textview_text": "Pause - Signalkonflikt", "rn_51001_statistics_chargingcycle_textview_text": "Ladesyklus", "rn_51001_resetrobotpwd_successtipend_textview_text": "for å starte roboten.$$Hvis problemet ditt fortsatt er der, kan du finne en forhandler.", "rn_51001_panelhome_updatemapfailnote5_textview_text": "<PERSON><PERSON><PERSON><PERSON>, den aktuella robotstatusen tillåter inte återställning av kartan, vänligen återställ gräsklipparen först.", "rn_51001_cutmode_edge_textview_text": "Kantskjæring", "rn_51001_modify_period_textview_text": "Tidsperiode", "rn_51001_cutheight_title_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_weeklyoverview_title_textview_text": "Ukentlig oversikt", "rn_51001_panelhome_status61_textview_text": "Klipping - Sekundærområde", "rn_51001_resetrobotpwd_unregisteredtip2_textview_text": "For sikkerhets skyld må roboten registreres først og", "rn_51001_cutheight_mm_textview_text": "<PERSON><PERSON> (/mm)", "rn_51001_statistics_totalco2savings_textview_text": "Total CO2-besparelse", "rn_51001_panelhome_status24_textview_text": "Retur - Grensesignal i konflikt", "rn_51001_schedule_override_textview_text": "Overstyr tidsplan", "rn_51001_cutmode_spiralsensitivity_textview_text": "Sensitivitetsnivå", "rn_51001_cutheight_titleleft_textview_text": "<PERSON><PERSON>", "rn_51001_statistics_totalenergyconsumption_textview_text": "Totalt energiforbruk", "rn_51001_panelhome_satellitemap_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_otherslist_rainsensornote_textview_text": "Notera: <PERSON><PERSON><PERSON> regn detekteras kör roboten tillbaka till laddstationen och startar om tills regnsensorn är torr och fördröjningstiden har löpt ut.", "rn_51001_modify_monday_textview_text": "Mandag", "rn_51001_detaillist_schedule_textview_text": "Tidsplanstyring", "rn_51001_panelhome_done_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_pwdcheckedsuccess_textview_text": "Bekreftelseskodebekreftelsen vellykket, send tilbakestillingskommando", "rn_51001_cutmode_workareamain_textview_text": "Hovedområde", "rn_51001_panelhome_status42_textview_text": "Lading fullført - <PERSON><PERSON> for<PERSON>", "rn_51001_modify_noschedule_textview_text": "Ingen tidsplan", "rn_51001_geofencesetting_title_textview_text": "Geofence-innstilling", "rn_51001_modify_setperiod_textview_text": "Angi tidsperiode", "rn_51001_panelhome_status10_textview_text": "Pause", "rn_51001_headlight_blue_textview_text": "Blå", "rn_51001_cutmode_spiralnote_textview_text": "Roboten vil klippe gresset i et spiralmønster hvis gresset er lengre i et bestemt område.", "rn_51001_resetrobotpwd_title_textview_text": "Tilbakestill robotpassord", "rn_51001_resetrobotpwd_successtipstart_textview_text": "Robotpassordet ditt har blitt tilbakestilt.$$Vennligst bruk", "rn_51001_panelhome_geofencesetting_textview_text": "Geofence-innstilling", "rn_51001_panelhome_status43_textview_text": "Lading fullført - <PERSON><PERSON>", "rn_51001_panelhome_parkuntil_textview_text": "<PERSON>", "rn_51001_resetrobotpwd_resetagain_button_text": "Tilbakestill igjen", "rn_51001_override_0_textview_text": "0t", "rn_51001_panelhome_parktipmin_textview_text": "Innstillingstiden må være større enn gjeldende tid.", "rn_51001_dashboard_alerttitle_textview_text": "Tips", "rn_51001_modify_thu_button_text": "TOR", "rn_51001_statistics_consumptionunit_textview_text": "A/H", "rn_51001_otherslist_rainsensor_textview_text": "Regnsensorinnstilling", "rn_51001_cutmode_eco_textview_text": "ØKO-skjæring", "rn_51001_resetrobotpwd_unregisteredtip_textview_text": "Av säkerhetsskäl måste din robot först registreras och sedan kan du återställa lösenordet här. Du kan också kontakta din återförsäljare för hjälp.", "rn_51001_statistics_totalchargetime_textview_text": "Total ladetid", "rn_51001_drivepastwire_cm_textview_text": "<PERSON><PERSON><PERSON><PERSON> forbi led<PERSON>en (/cm)", "rn_51001_panelhome_status51_textview_text": "Bygningskart - Sekundærområde", "rn_51001_cutmode_title_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_panelhome_status23_textview_text": "Retur - Park", "rn_51001_modify_copyto_button_text": "<PERSON><PERSON><PERSON> <PERSON>", "rn_51001_detaillist_cutmode_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_modify_tuesday_textview_text": "Tirsdag", "rn_51001_dashboard_alert2title_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_guardagainsttheft_resetpwdfail_textview_text": "robot tilbakestilling passord mislyktes, vennligst send igjen.", "rn_51001_map_renametitle_textview_text": "Gi nytt navn til kartet", "rn_51001_schedule_weeklyoverview_textview_text": "Ukentlig oversikt", "rn_51001_panelhome_cancel_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_modify_thursday_textview_text": "Torsdag", "rn_51001_alarmsetting_durationleft_textview_text": "Varighet av alarm", "rn_51001_guardagainsttheft_title_textview_text": "Tyverisikring", "rn_51001_dashboard_selectedareacuttingunit_textview_text": "m", "rn_51001_cutheight_done_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_panelhome_park_textview_text": "<PERSON><PERSON>", "rn_51001_resetrobotpwd_successtip3_textview_text": "<PERSON><PERSON> problemet fortsatt er der, kan du finne en forhandler.", "rn_51001_panelhome_status20_textview_text": "Retur <PERSON> <PERSON><PERSON><PERSON>ørt", "rn_51001_headlight_color_textview_text": "<PERSON>ys farge", "rn_51001_resetrobotpwd_successtip2end_textview_text": "for å starte roboten din.", "rn_51001_cutmode_selectedarea_textview_text": "<PERSON><PERSON>", "rn_51001_geofencesensitivity_setting_textview_text": "Sensitivitetsinnstilling", "rn_51001_detaillist_remotecontrol_textview_text": "Fjer<PERSON>ntroll", "rn_51001_timerange_start_textview_text": "Start", "rn_51001_resetrobotpwd_pwdnotcorrect_textview_text": "Bekreftelseskoden er feil", "rn_51001_guardagainsttheft_titlenew_textview_text": "Innstilling for tyverisikring", "rn_51001_cutmode_cuttingradius_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_panelhome_schedule_button_text": "Rute", "rn_51001_dashboard_exceededrangewaning_textview_text": "Du har overskredet den kontrollerbare rekkevidden til fjernkontrollen.", "rn_51001_backdistance_cm_textview_text": "Avstand bak (/cm)", "rn_51001_headlight_red_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_failtip1_textview_text": "<PERSON><PERSON><PERSON>, tilbakestilling av passord mislyktes.", "rn_51001_dashboard_modaltitle_textview_text": "Start klippemotoren", "rn_51001_headlight_schedule_textview_text": "Lett timeplan", "rn_51001_resetrobotpwd_unregisteredtip3_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON> deretter passordet her.", "rn_51001_statistics_totalstatistics_textview_text": "Totalstatistikk", "rn_51001_headlight_on_textview_text": "Alltid på", "rn_51001_modify_addtime_textview_text": "Tidsperiode", "rn_51001_cutmode_selectedarearadius_textview_text": "Klipperadius (/m)", "rn_51001_statistics_title_textview_text": "Statistikk", "rn_51001_drivepastwire_titleleft_textview_text": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "rn_51001_panelhome_status46_textview_text": "Lading fullført - <PERSON><PERSON> ikke eliminert", "rn_51001_dashboard_alertmessage_textview_text": "Vil du slutte med denne oppgaven?", "rn_51001_alarmsetting_alarmenable_textview_text": "Alarm når den løftes", "rn_51001_modify_wed_button_text": "ONS", "rn_51001_modify_mon_button_text": "MAN", "rn_51001_backhomecorridor_title_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_drivepastwire_title_textview_text": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "rn_51001_modify_friday_textview_text": "Fred<PERSON>", "rn_51001_cutmode_workareanote_textview_text": "Merk: Roboten vil automatisk klippe plenen i henhold til tidsplanen og personlige innstillinger.", "rn_51001_panelhome_updatemap_textview_text": "Tilbakestill kart", "rn_51001_rainsensor_delay_textview_text": "Forsinkelsestid (/min)", "rn_51001_cutmode_edgenote_textview_text": "Merk: <PERSON><PERSON> begynner å klippe langs plenens begrensningssløyfe først i hver nye tidsplan.", "rn_51001_cutmode_selectedareanote_textview_text": "<PERSON><PERSON>r valgt områdek<PERSON><PERSON> er på, vil roboten klippe området rundt det gjeldende punktet innenfor en viss radius.", "rn_51001_headlight_period_textview_text": "Angi tidsperiode", "rn_51001_dashboard_stopalertmsg_textview_text": "Enheten er i drift. Vil du slutte?", "rn_51001_resetrobotpwd_resettipstart_textview_text": "Vi har nettopp sendt en 6-digital kode til din registrerte robot-e-postadresse", "rn_51001_alarmsetting_title_textview_text": "Alarminnstilling", "rn_51001_panelhome_secondarea_textview_text": "<PERSON>", "rn_51001_panelhome_pickercentertitle_textview_text": "time", "rn_51001_cutmode_sysnote_textview_text": "Roboten begynner å kutte i rette systematiske linjer.", "rn_51001_panelhome_status60_textview_text": "Klipping - Hovedområde", "rn_51001_modify_sat_button_text": "LØR", "rn_51001_headlight_flashes_textview_text": "<PERSON><PERSON><PERSON> når det er feilvarsel", "rn_51001_modify_done_button_text": "<PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_unregisteredtip1_textview_text": "Beklager!", "rn_51001_geofencesensitivity_low_textview_text": "Lav"}, "de": {"rn_51001_smartexitdockingstation_note_textview_text": "Hinweis: Der Roboter fährt entlang des Drahts bis zur eingegebenen Entfernung zurück und beginnt anschließend zu mähen!", "rn_51001_modify_validate_textview_text": "Zeit für Ende muss später sein als die Zeit für Start", "rn_51001_panelhome_mailsemptyerror_textview_text": "E-Mail ist leer, Verifizierung fehlgeschlagen", "rn_51001_geofencesensitivity_medium_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_modify_conflict_textview_text": "Konflikt mit dem aktuellen Zeitraum; bitte löschen", "rn_51001_panelhome_status40_textview_text": "Ladevorgang abgeschlossen – Warten auf den nächsten Mähtermin", "rn_51001_resetrobotpwd_toregister_button_text": "Registrierung", "rn_51001_statistics_totalworkinghours_textview_text": "Gesamtbetriebsstunden", "rn_51001_panelhome_updatemapfailnote_textview_text": "Sorry, the current robot status does not allow to reset map, please recover the mower first.", "rn_51001_resetrobotpwd_done_button_text": "<PERSON><PERSON><PERSON>", "rn_51001_batterydetail_title_textview_text": "Angaben zum Akku", "rn_51001_panelhome_status45_textview_text": "Ladevorgang abgeschlossen – Deckel g<PERSON>net", "rn_51001_detaillist_cutheight_textview_text": "Schnitthöhe", "rn_51001_geofencesensitivity_high_textview_text": "Hoch", "rn_51001_backhomecorridor_widthleft_textview_text": "Breite des Korridors", "rn_51001_resetrobotpwd_unregisteredtip4_textview_text": "Oder Sie fordern Hilfe beim Handelsgeschäft an.", "rn_51001_modify_addtime_button_text": "Zeit hinzufügen", "rn_51001_statistics_energysavingdata_textview_text": "Energiespardaten", "rn_51001_statistics_totaldistance_textview_text": "Zurückgelegte Gesamtstrecke", "rn_51001_modify_sunday_textview_text": "Sonntag", "rn_51001_panelhome_status21_textview_text": "Rückkehr – Akku schwach", "rn_51001_panelhome_interfaceerror_textview_text": "Schnittstelle nicht zurückgemeldet, Fehler gemeldet", "rn_51001_modal_slideronOff_textview_text": "Ein/Aus", "rn_51001_cutmode_spiral_textview_text": "Wendelartiges Mähen", "rn_51001_override_48_textview_text": "48 h", "rn_51001_panelhome_parktiphour_textview_text": "Die Sollzeit muss eine Stunde später als die aktuelle Uhrzeit sein", "rn_51001_panelhome_updatemapmsg_textview_text": "You are resetting a map. The new map will replace the current one. Do you want to continue?", "rn_51001_headlight_white_textview_text": "<PERSON><PERSON>", "rn_51001_panelhome_status30_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_panelhome_status22_textview_text": "Rückkehr - Regen", "rn_51001_resetrobotpwd_successtip1_textview_text": "Ihr Roboterpasswort wurde erfolgreich zurückgesetzt.", "rn_51001_resetrobotpwd_pwdnotcompleted_textview_text": "Bitte geben Sie den Bestätigungscode ein", "rn_51001_resetrobotpwd_failtip2_textview_text": "Bitte sorgen Sie für eine aktive Verbindung zum Mähroboter - entweder per Bluetooth oder per Netzwerk", "rn_51001_drivepastwire_note_textview_text": "Note: This is the distance that the front of the robot can move past the boundary wire.", "rn_51001_cutheight_inch_textview_text": "Schnitthöhe einstellen (/Zoll)", "rn_51001_modify_sun_button_text": "SO", "rn_51001_modify_wednesday_textview_text": "Mittwoch", "rn_51001_panelhome_otaalertmsg_textview_text": "Ohne die Aktualisierung funktioniert das Gerät nicht ordnungsgemäß. Bitte aktualisieren Sie jetzt.", "rn_51001_modify_tue_button_text": "DI", "rn_51001_resetrobotpwd_successtip2start_textview_text": "Verwenden Sie", "rn_51001_detaillist_antitheft_textview_text": "Diebstahlschutzeinstellungen", "rn_51001_statistics_totalchargenote_textview_text": "Die Gesamtladezeit ist die Zeit, die der Mäher an der Ladestation verbringt. <PERSON><PERSON> Si<PERSON>, dass die Ladezeit in den Gesamtbetriebsstunden nicht enthalten ist.", "rn_51001_statistics_energynote_textview_text": "Im Vergleich zu anderen Produkten erzeugt der EGO-Mäher im Betrieb insgesamt weniger Kohlendioxid.", "rn_51001_panelhome_status50_textview_text": "Aufbau der Karte – Primärbereich", "rn_51001_override_24_textview_text": "24 h", "rn_51001_statistics_batterydata_textview_text": "Akkudaten", "rn_51001_resetrobotpwd_resettipsend_textview_text": ". Bitte geben Sie den untenstehenden Code ein, um Ihre E-Mail-Adresse zu bestätigen.", "rn_51001_resetrobotpwd_unregistertip_button_text": "Sorry!\nFor safety, your robot must be registered first and\n then reset the password here.\nOr you can contact your dealer for help.", "rn_51001_statistics_chargingcyclenote_textview_text": "Der Ladezyklus zeigt an, wie oft der Akku vollständig geladen und entladen wurde.", "rn_51001_headlight_off_textview_text": "Immer AUS", "rn_51001_headlight_green_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_sucesstitle_textview_text": "Your robot password has been reset successfully. Please use “0000” to start your robot. If your problem is still there, you can find a dealer.", "rn_51001_resetrobotpwd_failtip3_textview_text": "Verbindung zum Mähroboter, entweder per Bluetooth", "rn_51001_backhomecorridor_note_textview_text": "Note: This is the width between the robot and the boundary wire when robot returns. Make the width as wide as possible to reduce lines in the lawn.", "rn_51001_panelhome_status44_textview_text": "Ladevorgang abgeschlossen – Warten auf Start", "rn_51001_resetrobotpwd_failtip_textview_text": "Sorry, reset password has failed.$$Please make sure that you have an active$$ connection to your mower, either using Bluetooth$$ or the mobile network.", "rn_51001_alarmsetting_duration_textview_text": "<PERSON><PERSON><PERSON><PERSON> (/min)", "rn_51001_override_note_textview_text": "Je nach Einstellung mäht der Roboter im Dauerbetrieb.", "rn_51001_statistics_batteryhealth_textview_text": "Akkustatus", "rn_51001_dashboard_exit_textview_text": "<PERSON>den", "rn_51001_timerange_end_textview_text": "<PERSON><PERSON>", "rn_51001_otherslist_headlight_textview_text": "Scheinwerfer", "rn_51001_modify_all_textview_text": "Alle auswählen", "rn_51001_resetrobotpwd_finddealer_button_text": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "rn_51001_panelhome_status41_textview_text": "Ladevorgang abgeschlossen – Mähtermin nicht e<PERSON>ste<PERSON>t", "rn_51001_resetrobotpwd_fail_textview_text": "Fehlgeschlagen", "rn_51001_cutmode_workarea_textview_text": "Arbeitsbereich", "rn_51001_override_72_textview_text": "72 h", "rn_51001_resetrobotpwd_failtip4_textview_text": "oder das Mobilfunknetz gesendet.", "rn_51001_panelhome_updatemapfailnote4_textview_text": "Sorry, the current robot status does not allow to reset map, please recover the mower first.", "rn_51001_cutmode_econote_textview_text": "Hinweis: <PERSON><PERSON> der Roboter feststellt, dass der Rasen gemäht wurde, überfährt er ihn schneller, bis er den ungemähten Rasen erreicht!", "rn_51001_resetrobotpwd_success_textview_text": "Erfolg", "rn_51001_panelhome_mainarea_textview_text": "Hauptbereich", "rn_51001_panelhome_status11_textview_text": "Pause – Stopp", "rn_51001_modify_saturday_textview_text": "Samstag", "rn_51001_backhomecorridor_width_textview_text": "Breite des Korridors (/mm)", "rn_51001_panelhome_pickerlefttitle_textview_text": "Datum", "rn_51001_override_done_button_text": "<PERSON><PERSON><PERSON>", "rn_51001_cutmode_workareasecond_textview_text": "Zweiter Bereich", "rn_51001_modify_delete_button_text": "Löschen", "rn_51001_alarmsetting_switch_textview_text": "Alarmeinstellung", "rn_51001_modify_fri_button_text": "FR", "rn_51001_statistics_totaltravelnote_textview_text": "Die Gesamtfahrstrecke ist die Strecke, die der Mäher in allen Betriebsarten zurückgelegt hat.", "rn_51001_resetrobotpwd_problemsolved_button_text": "<PERSON> gelöst", "rn_51001_smartexitdockingstation_title_textview_text": "Dockingstation verlassen", "rn_51001_panelhome_status25_textview_text": "Rückkehr - außerhalb des Zeitplans", "rn_51001_detaillist_others_textview_text": "Sonstiges", "rn_51001_panelhome_mapsettings_textview_text": "Karteneinstellungen", "rn_51001_panelhome_updatetitle_textview_text": "Neues Update verfügbar", "rn_51001_panelhome_status14_textview_text": "Pause – Schnitthöheneinstellung", "rn_51001_detaillist_functionsettings_textview_text": "Funktionseinstellungen", "rn_51001_panelhome_status27_textview_text": "Rückkehr - Ka<PERSON> neu aufbauen", "rn_51001_backdistance_titleleft_textview_text": "Entfernung zurück", "rn_51001_panelhome_parktipmax_textview_text": "Die Sollzeit darf 99 h nicht überschreiten.", "rn_51001_panelhome_updatemapfailnote7_textview_text": "Sorry, the current robot status does not allow to reset map, please recover the mower first.", "rn_51001_dashboard_modalmessage_textview_text": "Zum Starten beide Tasten gleichzeitig drücken", "rn_51001_guardagainsttheft_alarmsetting_textview_text": "Alarmeinstellung", "rn_51001_panelhome_devicedidreceivecmd_textview_text": "Das Gerät hat den Befehl zum Zurücksetzen des Passworts erfolgreich empfangen", "rn_51001_detaillist_bordermanagement_textview_text": "Grenzverwaltung", "rn_51001_panelhome_pickerrighttitle_textview_text": "Minute", "rn_51001_panelhome_updatemapfailnote6_textview_text": "Sorry, the current robot status does not allow to reset map, please recover the mower first.", "rn_51001_cutmode_sys_textview_text": "Systematisches Mähen", "rn_51001_panelhome_status26_textview_text": "Rückkehr - Rückruf durch Ladestation", "rn_51001_schedulemanagement_title_textview_text": "Zeitplan-Management", "rn_51001_dashboard_alert2subtitle_textview_text": "Mä<PERSON>dius (/ m)", "rn_51001_panelhome_mowingpath_textview_text": "Mähweg", "rn_51001_detaillist_availablesettings_textview_text": "Verfügbare Einstellungen", "rn_51001_panelhome_status13_textview_text": "<PERSON><PERSON> <PERSON>", "rn_51001_bordermanagement_title_textview_text": "Grenzverwaltung", "rn_51001_guardagainsttheft_resetrobotpwd_textview_text": "Roboterpasswort zurücksetzen", "rn_51001_guardagainsttheft_geofencesetting_textview_text": "Geofence-Einstellungen", "rn_51001_panelhome_status12_textview_text": "Pause - Signalkonflikt", "rn_51001_statistics_chargingcycle_textview_text": "Ladezyklus", "rn_51001_resetrobotpwd_successtipend_textview_text": "Starten des Roboters.$$Wenn Sie sich an den Fachhandel, wenn Ihr Problem weiterhin besteht.", "rn_51001_panelhome_updatemapfailnote5_textview_text": "Sorry, the current robot status does not allow to reset map, please recover the mower first.", "rn_51001_cutmode_edge_textview_text": "<PERSON><PERSON><PERSON> m<PERSON>hen", "rn_51001_modify_period_textview_text": "Zeitspanne", "rn_51001_cutheight_title_textview_text": "Schnitthöhe", "rn_51001_weeklyoverview_title_textview_text": "Wochenübersicht", "rn_51001_panelhome_status61_textview_text": "Mähen - Sekundärbereich", "rn_51001_resetrobotpwd_unregisteredtip2_textview_text": "Aus Sicherheitsgründen muss Ihr Roboter zuerst registriert werden und", "rn_51001_cutheight_mm_textview_text": "Schnitthöhe einstellen (/mm)", "rn_51001_statistics_totalco2savings_textview_text": "CO2-Einsparungen insgesamt", "rn_51001_panelhome_status24_textview_text": "Rückkehr – Grenzsignalkonflikt", "rn_51001_schedule_override_textview_text": "Zeitplan übergehen", "rn_51001_cutmode_spiralsensitivity_textview_text": "Empfindlichkeitsstufe", "rn_51001_cutheight_titleleft_textview_text": "Schnitthöhe einstellen", "rn_51001_statistics_totalenergyconsumption_textview_text": "Gesamtverbrauch an Energie", "rn_51001_panelhome_satellitemap_textview_text": "Satellitenkarte", "rn_51001_otherslist_rainsensornote_textview_text": "Note: When rain is detected, the robot returns to the charging station and restarts until the rain sensor is dry and delay time is over.", "rn_51001_modify_monday_textview_text": "Montag", "rn_51001_detaillist_schedule_textview_text": "Zeitplan-Management", "rn_51001_panelhome_done_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_pwdcheckedsuccess_textview_text": "Überprüfung des Bestätigungscodes erfolgreich, Reset-Befehl senden", "rn_51001_cutmode_workareamain_textview_text": "Hauptbereich", "rn_51001_panelhome_status42_textview_text": "Ladevorgang abgeschlossen – Regenverzögerung", "rn_51001_modify_noschedule_textview_text": "<PERSON><PERSON>", "rn_51001_geofencesetting_title_textview_text": "Geofence-Einstellung", "rn_51001_modify_setperiod_textview_text": "Zeitspanne festlegen", "rn_51001_panelhome_status10_textview_text": "Pause", "rn_51001_headlight_blue_textview_text": "Blau", "rn_51001_cutmode_spiralnote_textview_text": "<PERSON><PERSON> <PERSON> in einem bestimmten Bereich länger ist, mäht es der Roboter spiralförmig.", "rn_51001_resetrobotpwd_title_textview_text": "Roboterpasswort zurücksetzen", "rn_51001_resetrobotpwd_successtipstart_textview_text": "Das Roboterpasswort wurde erfolgreich zurückgesetzt.$$Starten Sie den Roboter mit", "rn_51001_panelhome_geofencesetting_textview_text": "Geofence-Einstellung", "rn_51001_panelhome_status43_textview_text": "Ladevorgang abgeschlossen – Parken", "rn_51001_panelhome_parkuntil_textview_text": "Parken bis", "rn_51001_resetrobotpwd_resetagain_button_text": "<PERSON><PERSON><PERSON>", "rn_51001_override_0_textview_text": "0 h", "rn_51001_panelhome_parktipmin_textview_text": "Die Sollzeit muss größer als die aktuelle Zeit sein.", "rn_51001_dashboard_alerttitle_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_modify_thu_button_text": "DO", "rn_51001_statistics_consumptionunit_textview_text": "A/H", "rn_51001_otherslist_rainsensor_textview_text": "Regensensoreinstellung", "rn_51001_cutmode_eco_textview_text": "ECO Mähfunktion", "rn_51001_resetrobotpwd_unregisteredtip_textview_text": "Sorry!For safety, your robot must be registered first and then reset the password here.Or you can contact your dealer for help.", "rn_51001_statistics_totalchargetime_textview_text": "Gesamtladezeit", "rn_51001_drivepastwire_cm_textview_text": "<PERSON> vorbeifahren (/cm)", "rn_51001_panelhome_status51_textview_text": "Aufbau der Karte - Sekundärbereich", "rn_51001_cutmode_title_textview_text": "Mähfunktion", "rn_51001_panelhome_status23_textview_text": "Rückkehr - Parken", "rn_51001_modify_copyto_button_text": "<PERSON><PERSON><PERSON> auf", "rn_51001_detaillist_cutmode_textview_text": "Mähfunktion", "rn_51001_modify_tuesday_textview_text": "Dienstag", "rn_51001_dashboard_alert2title_textview_text": "Gewählter Mähbereich", "rn_51001_guardagainsttheft_resetpwdfail_textview_text": "Zurücksetzen des Roboter-Passworts fehlgeschlagen, bitte erneut senden.", "rn_51001_map_renametitle_textview_text": "<PERSON><PERSON> um<PERSON>n", "rn_51001_schedule_weeklyoverview_textview_text": "Wöchentliche Übersicht", "rn_51001_panelhome_cancel_textview_text": "Abbrechen", "rn_51001_modify_thursday_textview_text": "Don<PERSON><PERSON>", "rn_51001_alarmsetting_durationleft_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_guardagainsttheft_title_textview_text": "Diebstahlschutz", "rn_51001_dashboard_selectedareacuttingunit_textview_text": "m", "rn_51001_cutheight_done_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_panelhome_park_textview_text": "Park", "rn_51001_resetrobotpwd_successtip3_textview_text": "<PERSON>n Ihr Problem weiterhin besteht, können Si<PERSON> sich an den Fachhandel wenden.", "rn_51001_panelhome_status20_textview_text": "Rückkehr – <PERSON>ähen abgeschlossen", "rn_51001_headlight_color_textview_text": "<PERSON><PERSON>", "rn_51001_resetrobotpwd_successtip2end_textview_text": "zum Starten des Roboters.", "rn_51001_cutmode_selectedarea_textview_text": "<PERSON><PERSON><PERSON> eines bestimmten Bereichs", "rn_51001_geofencesensitivity_setting_textview_text": "Empfindlichkeitseinstellung", "rn_51001_detaillist_remotecontrol_textview_text": "Fernsteuerung", "rn_51001_timerange_start_textview_text": "Start", "rn_51001_resetrobotpwd_pwdnotcorrect_textview_text": "Der Bestätigungscode ist falsch", "rn_51001_guardagainsttheft_titlenew_textview_text": "Diebstahlschutzeinstellungen", "rn_51001_cutmode_cuttingradius_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_panelhome_schedule_button_text": "Zeitplan", "rn_51001_dashboard_exceededrangewaning_textview_text": "Sie haben die effektive Reichweite der Fernsteuerung überschritten.", "rn_51001_backdistance_cm_textview_text": "Entfernung zurück (/cm)", "rn_51001_headlight_red_textview_text": "Rot", "rn_51001_resetrobotpwd_failtip1_textview_text": "Leider ist das Zurücksetzen des Passworts fehlgeschlagen.", "rn_51001_dashboard_modaltitle_textview_text": "Mähmotor starten", "rn_51001_headlight_schedule_textview_text": "Licht-Zeitplan", "rn_51001_resetrobotpwd_unregisteredtip3_textview_text": "Setzen Sie das Passwort dann hier zurück.", "rn_51001_statistics_totalstatistics_textview_text": "Statistik insgesamt", "rn_51001_headlight_on_textview_text": "Immer AN", "rn_51001_modify_addtime_textview_text": "Zeitspanne", "rn_51001_cutmode_selectedarearadius_textview_text": "Mä<PERSON>dius (/m)", "rn_51001_statistics_title_textview_text": "Statistik", "rn_51001_drivepastwire_titleleft_textview_text": "<PERSON> v<PERSON>", "rn_51001_panelhome_status46_textview_text": "Ladevorgang abgeschlossen – Fehler nicht behoben", "rn_51001_dashboard_alertmessage_textview_text": "Wollen Sie diese Aufgabe beenden?", "rn_51001_alarmsetting_alarmenable_textview_text": "Alarm be<PERSON>", "rn_51001_modify_wed_button_text": "MI", "rn_51001_modify_mon_button_text": "MO", "rn_51001_backhomecorridor_title_textview_text": "Rückkehr-Korridor", "rn_51001_drivepastwire_title_textview_text": "<PERSON> v<PERSON>", "rn_51001_modify_friday_textview_text": "Freitag", "rn_51001_cutmode_workareanote_textview_text": "Hinweis: Der Roboter mäht den Rasen automatisch nach Zeitplan und nach persönlichen Einstellungen.", "rn_51001_panelhome_updatemap_textview_text": "<PERSON><PERSON> zurücksetzen", "rn_51001_rainsensor_delay_textview_text": "Wartezeit (/min)", "rn_51001_cutmode_edgenote_textview_text": "Hinweis: <PERSON><PERSON> jedem neuen Zeitplan beginnt der Roboter zuerst mit dem Mähen entlang des Begrenzungskabels des Rasens.", "rn_51001_cutmode_selectedareanote_textview_text": "Ist das Mähen eines bestimmten Bereichs eingeschaltet, mäht der Roboter in einem bestimmten Radius um die aktuelle Position herum.", "rn_51001_headlight_period_textview_text": "Zeitspanne festlegen", "rn_51001_dashboard_stopalertmsg_textview_text": "Gerät ist in Betrieb. Gerät abstellen?", "rn_51001_resetrobotpwd_resettipstart_textview_text": "Wir haben gerade einen 6-stelligen Code an Ihre registrierte Roboter-E-Mail-Adresse", "rn_51001_alarmsetting_title_textview_text": "Alarmeinstellung", "rn_51001_panelhome_secondarea_textview_text": "Zweiter Bereich", "rn_51001_panelhome_pickercentertitle_textview_text": "Stunde", "rn_51001_cutmode_sysnote_textview_text": "Der Roboter beginnt mit dem Mähen in geraden, systematischen Linien.", "rn_51001_panelhome_status60_textview_text": "Mähen - Primärbereich", "rn_51001_modify_sat_button_text": "SA", "rn_51001_headlight_flashes_textview_text": "Blinkt bei Fehlermeldung", "rn_51001_modify_done_button_text": "<PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_unregisteredtip1_textview_text": "Entschuldigung!", "rn_51001_geofencesensitivity_low_textview_text": "<PERSON><PERSON><PERSON>"}, "se": {"rn_51001_smartexitdockingstation_note_textview_text": "Notera: Robot<PERSON> backar längs kabeln till ingångsavståndet och kör sedan iväg för att klippa!", "rn_51001_modify_validate_textview_text": "Sluttiden kan inte vara senare än starttiden", "rn_51001_panelhome_mailsemptyerror_textview_text": "E-postad<PERSON>en är tom, verifiering misslyckades", "rn_51001_geofencesensitivity_medium_textview_text": "Medium", "rn_51001_modify_conflict_textview_text": "Konflikt med den aktuella perioden; vänligen återställ", "rn_51001_panelhome_status40_textview_text": "Laddningen slutförd - Väntar på nästa klippschema", "rn_51001_resetrobotpwd_toregister_button_text": "Registrera", "rn_51001_statistics_totalworkinghours_textview_text": "Totalt antal <PERSON>timmar", "rn_51001_panelhome_updatemapfailnote_textview_text": "Valitettavasti robotin n<PERSON>yinen tila ei salli kartan nollaamista, palauta ensin ruohonle<PERSON>.", "rn_51001_resetrobotpwd_done_button_text": "<PERSON><PERSON><PERSON>", "rn_51001_batterydetail_title_textview_text": "Batteriinformation:", "rn_51001_panelhome_status45_textview_text": "Laddningen är klar - lock öppet", "rn_51001_detaillist_cutheight_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_geofencesensitivity_high_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_backhomecorridor_widthleft_textview_text": "Korridorens bredd", "rn_51001_resetrobotpwd_unregisteredtip4_textview_text": "Du kan också kontakta din återförsäljare för hjälp.", "rn_51001_modify_addtime_button_text": "<PERSON><PERSON><PERSON> till tid", "rn_51001_statistics_energysavingdata_textview_text": "Uppgifter om energibesparingar", "rn_51001_statistics_totaldistance_textview_text": "Total körsträcka", "rn_51001_modify_sunday_textview_text": "S<PERSON><PERSON>g", "rn_51001_panelhome_status21_textview_text": "Återgår - <PERSON><PERSON><PERSON>", "rn_51001_panelhome_interfaceerror_textview_text": "Gränssnittet returnerades inte, fel rapporterades", "rn_51001_modal_slideronOff_textview_text": "På/av", "rn_51001_cutmode_spiral_textview_text": "Spiralklippning", "rn_51001_override_48_textview_text": "48h", "rn_51001_panelhome_parktiphour_textview_text": "Inställningstiden måste vara en timme senare än nuvarande tid.", "rn_51001_panelhome_updatemapmsg_textview_text": "<PERSON><PERSON> karttaa. Uusi kartta kor<PERSON>a n<PERSON>yisen kartan. Haluatko jatkaa?", "rn_51001_headlight_white_textview_text": "Vit", "rn_51001_panelhome_status30_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_panelhome_status22_textview_text": "Å<PERSON>går - regn", "rn_51001_resetrobotpwd_successtip1_textview_text": "<PERSON><PERSON> har återställts framgångsrikt.", "rn_51001_resetrobotpwd_pwdnotcompleted_textview_text": "Vänligen ange verifieringskoden", "rn_51001_resetrobotpwd_failtip2_textview_text": "Kontrollera att du har en aktiv", "rn_51001_drivepastwire_note_textview_text": "Huomaa: <PERSON><PERSON><PERSON><PERSON> on etäisyys, jonka robotin etuosa saa mennä rajalangan ohi.", "rn_51001_cutheight_inch_textview_text": "<PERSON><PERSON> (/tum)", "rn_51001_modify_sun_button_text": "SÖN", "rn_51001_modify_wednesday_textview_text": "Onsdag", "rn_51001_panelhome_otaalertmsg_textview_text": "Om du inte uppdaterar kommer enheten inte att fungera korrekt. Uppdatera nu.", "rn_51001_modify_tue_button_text": "TIS", "rn_51001_resetrobotpwd_successtip2start_textview_text": "Kontrollera", "rn_51001_detaillist_antitheft_textview_text": "Stöldskyddsinställningar", "rn_51001_statistics_totalchargenote_textview_text": "Den totala laddningstiden är den tid som gräsklipparen tillbringar vid laddningsstationen. Observera att laddningstiden inte ingår i den totala drifttiden.", "rn_51001_statistics_energynote_textview_text": "Jämfört med andra produkter producerar EGO-gräsklipparen mindre total koldioxid under drift.", "rn_51001_panelhome_status50_textview_text": "Byggnadskarta - Huvudområde", "rn_51001_override_24_textview_text": "24h", "rn_51001_statistics_batterydata_textview_text": "Batterida<PERSON>", "rn_51001_resetrobotpwd_resettipsend_textview_text": ". <PERSON>e koden nedan för att verifiera din e-postadress.", "rn_51001_resetrobotpwd_unregistertip_button_text": "Anteeksi!\nTurvallisuuden vuoksi robotti on ensin rekisteröitävä ja\n salasana on sitten nollattava täällä.\nVoit myös pyytää apua jälleenmyyjältäsi.", "rn_51001_statistics_chargingcyclenote_textview_text": "Laddningscykeln visar hur många gånger batteriet har laddats fullt och laddats ur.", "rn_51001_headlight_off_textview_text": "Alltid av", "rn_51001_headlight_green_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_sucesstitle_textview_text": "Robottisi salasanan nollaus onnistui. Käynnistä robotti kä<PERSON>m<PERSON>llä ”0000\" Jo<PERSON> ongelmasi jatkuu edelleen, etsi jälle<PERSON>my<PERSON>j<PERSON>.", "rn_51001_resetrobotpwd_failtip3_textview_text": "anslutning till din gr<PERSON>sklippare, antingen med Bluetooth", "rn_51001_backhomecorridor_note_textview_text": "Huomaa: <PERSON><PERSON><PERSON><PERSON> on robotin ja rajalangan välinen etäisyys, kun robotti palaa takaisin. Tee etäisyys mahdollisimman leveäksi vähentääksesi raitoja nurmikossa.", "rn_51001_panelhome_status44_textview_text": "Laddningen är klar - väntar på start", "rn_51001_resetrobotpwd_failtip_textview_text": "Valitettavasti salasanan nollaus epäonnistui.$$Varmista, että sinulla on aktiivinen$$ yhteys ruohonleikkuriin joko <PERSON>in$$ tai mobiiliverkon kautta.", "rn_51001_alarmsetting_duration_textview_text": "Varaktighet för larmet (/min)", "rn_51001_override_note_textview_text": "Roboten klipper gräsmattan kontinuerligt baserat på din inställning.", "rn_51001_statistics_batteryhealth_textview_text": "Batterihälsa", "rn_51001_dashboard_exit_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_timerange_end_textview_text": "Slutförande", "rn_51001_otherslist_headlight_textview_text": "Strålkastare", "rn_51001_modify_all_textview_text": "<PERSON><PERSON><PERSON><PERSON> alla", "rn_51001_resetrobotpwd_finddealer_button_text": "<PERSON><PERSON>terförsäljare", "rn_51001_panelhome_status41_textview_text": "Laddning slutförd - Klippschema inte fastställt", "rn_51001_resetrobotpwd_fail_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_cutmode_workarea_textview_text": "Arbetsområ<PERSON>", "rn_51001_override_72_textview_text": "72h", "rn_51001_resetrobotpwd_failtip4_textview_text": "eller mobilnätet.", "rn_51001_panelhome_updatemapfailnote4_textview_text": "Valitettavasti robotin n<PERSON>yinen tila ei salli kartan nollaamista, palauta ensin ruohonle<PERSON>.", "rn_51001_cutmode_econote_textview_text": "Notera: Om roboten upptäcker att gräsmattan har klippts kör den snabbt över den tills den når oklippt gräs!", "rn_51001_resetrobotpwd_success_textview_text": "Framgång", "rn_51001_panelhome_mainarea_textview_text": "Huvudområde", "rn_51001_panelhome_status11_textview_text": "Paus - Stopp", "rn_51001_modify_saturday_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_backhomecorridor_width_textview_text": "Korridorens bredd (/mm)", "rn_51001_panelhome_pickerlefttitle_textview_text": "datum", "rn_51001_override_done_button_text": "<PERSON><PERSON><PERSON>", "rn_51001_cutmode_workareasecond_textview_text": "<PERSON><PERSON>", "rn_51001_modify_delete_button_text": "<PERSON> bort", "rn_51001_alarmsetting_switch_textview_text": "Inställning av larm", "rn_51001_modify_fri_button_text": "FRE", "rn_51001_statistics_totaltravelnote_textview_text": "Den totala körsträckan är den sträcka som gräsklipparen kör i alla lägen.", "rn_51001_resetrobotpwd_problemsolved_button_text": "Problemet är löst", "rn_51001_smartexitdockingstation_title_textview_text": "Utgångsdockningsstation", "rn_51001_panelhome_status25_textview_text": "Återgår - Utanf<PERSON><PERSON> schemat", "rn_51001_detaillist_others_textview_text": "<PERSON><PERSON>", "rn_51001_panelhome_mapsettings_textview_text": "Kartinställningar", "rn_51001_panelhome_updatetitle_textview_text": "Ny uppdatering finns tillgänglig", "rn_51001_panelhome_status14_textview_text": "Pause - Klipphöjdsinställning", "rn_51001_detaillist_functionsettings_textview_text": "Funktionsinställningar", "rn_51001_panelhome_status27_textview_text": "Återgår - återuppbyggande karta", "rn_51001_backdistance_titleleft_textview_text": "Backavstånd", "rn_51001_panelhome_parktipmax_textview_text": "Inställningstiden får inte överstiga 99h.", "rn_51001_panelhome_updatemapfailnote7_textview_text": "Valitettavasti robotin n<PERSON>yinen tila ei salli kartan nollaamista, palauta ensin ruohonle<PERSON>.", "rn_51001_dashboard_modalmessage_textview_text": "<PERSON><PERSON> på båda knapparna samtidigt för att starta", "rn_51001_guardagainsttheft_alarmsetting_textview_text": "Inställning av larm", "rn_51001_panelhome_devicedidreceivecmd_textview_text": "Enheten tog lyckosamt emot kommandot för återställning av lösenord", "rn_51001_detaillist_bordermanagement_textview_text": "Gränsförvaltning", "rn_51001_panelhome_pickerrighttitle_textview_text": "minut", "rn_51001_panelhome_updatemapfailnote6_textview_text": "Valitettavasti robotin n<PERSON>yinen tila ei salli kartan nollaamista, palauta ensin ruohonle<PERSON>.", "rn_51001_cutmode_sys_textview_text": "Systematisk klippning", "rn_51001_panelhome_status26_textview_text": "Återgår - Kallad av laddstation", "rn_51001_schedulemanagement_title_textview_text": "Hantering av schema", "rn_51001_dashboard_alert2subtitle_textview_text": "<PERSON><PERSON><PERSON><PERSON> (/ m)", "rn_51001_panelhome_mowingpath_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_detaillist_availablesettings_textview_text": "Tillgängliga inställningar", "rn_51001_panelhome_status13_textview_text": "<PERSON><PERSON> <PERSON> <PERSON><PERSON>", "rn_51001_bordermanagement_title_textview_text": "Gränsförvaltning", "rn_51001_guardagainsttheft_resetrobotpwd_textview_text": "Återställ robotens lösenord", "rn_51001_guardagainsttheft_geofencesetting_textview_text": "Inställningar för geofence", "rn_51001_panelhome_status12_textview_text": "Paus - Signalkonflikt", "rn_51001_statistics_chargingcycle_textview_text": "Laddningscykel", "rn_51001_resetrobotpwd_successtipend_textview_text": "för att starta roboten.$$Om problemet kvarstår kan du kontakta en återförsäljare.", "rn_51001_panelhome_updatemapfailnote5_textview_text": "Valitettavasti robotin n<PERSON>yinen tila ei salli kartan nollaamista, palauta ensin ruohonle<PERSON>.", "rn_51001_cutmode_edge_textview_text": "Sidoklippning", "rn_51001_modify_period_textview_text": "Tidsperiod", "rn_51001_cutheight_title_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_weeklyoverview_title_textview_text": "Veckovis översikt", "rn_51001_panelhome_status61_textview_text": "Klipper - sekundärt område", "rn_51001_resetrobotpwd_unregisteredtip2_textview_text": "<PERSON><PERSON><PERSON> måste din robot först registreras och", "rn_51001_cutheight_mm_textview_text": "<PERSON><PERSON> k<PERSON> (/mm)", "rn_51001_statistics_totalco2savings_textview_text": "Totala koldioxidbesparingar", "rn_51001_panelhome_status24_textview_text": "Återgår - Konflikt med gränssignal", "rn_51001_schedule_override_textview_text": "<PERSON><PERSON><PERSON> för <PERSON>", "rn_51001_cutmode_spiralsensitivity_textview_text": "Känslighetsnivå", "rn_51001_cutheight_titleleft_textview_text": "<PERSON><PERSON>", "rn_51001_statistics_totalenergyconsumption_textview_text": "Total energiförbrukning", "rn_51001_panelhome_satellitemap_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_otherslist_rainsensornote_textview_text": "Huomaa: <PERSON><PERSON> sade <PERSON>, <PERSON>ti palaa <PERSON> ja k<PERSON><PERSON><PERSON><PERSON>y u<PERSON> vasta, kun<PERSON> sadetun<PERSON><PERSON> on kuiva ja viive<PERSON>ka on ohi.", "rn_51001_modify_monday_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_detaillist_schedule_textview_text": "Hantering av schema", "rn_51001_panelhome_done_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_pwdcheckedsuccess_textview_text": "Verifiering av verifieringskod framgångsrik, ski<PERSON>a <PERSON>tällningskommando", "rn_51001_cutmode_workareamain_textview_text": "Huvudområde", "rn_51001_panelhome_status42_textview_text": "Laddningen är klar -regnfördröjning", "rn_51001_modify_noschedule_textview_text": "<PERSON><PERSON> tidtabell", "rn_51001_geofencesetting_title_textview_text": "Inställning av geofence", "rn_51001_modify_setperiod_textview_text": "Ställ in tidsperioden", "rn_51001_panelhome_status10_textview_text": "<PERSON><PERSON>", "rn_51001_headlight_blue_textview_text": "Blå", "rn_51001_cutmode_spiralnote_textview_text": "Roboten klipper gräset i ett spiralmönster om gräset är längre i ett visst område.", "rn_51001_resetrobotpwd_title_textview_text": "Återställ robotens lösenord", "rn_51001_resetrobotpwd_successtipstart_textview_text": "Ditt robots lösenord har återställts framgångsrikt. Använd “XXXX” för att starta roboten.", "rn_51001_panelhome_geofencesetting_textview_text": "Inställning av geofence", "rn_51001_panelhome_status43_textview_text": "Laddningen är klar - parkering", "rn_51001_panelhome_parkuntil_textview_text": "Parkera tills", "rn_51001_resetrobotpwd_resetagain_button_text": "Återställ igen", "rn_51001_override_0_textview_text": "0h", "rn_51001_panelhome_parktipmin_textview_text": "Inställningstiden måste vara större än den aktuella tiden.", "rn_51001_dashboard_alerttitle_textview_text": "Tips", "rn_51001_modify_thu_button_text": "TORS", "rn_51001_statistics_consumptionunit_textview_text": "A/H", "rn_51001_otherslist_rainsensor_textview_text": "Inställning av regnsensor", "rn_51001_cutmode_eco_textview_text": "ECO-klippning", "rn_51001_resetrobotpwd_unregisteredtip_textview_text": "Anteeksi! Turvallisuuden vuoksi robotti on ensin rekisteröitävä ja salasana on sitten nollattava täällä. Voit myös pyytää apua jälleenmyyjältäsi.", "rn_51001_statistics_totalchargetime_textview_text": "Total laddningstid", "rn_51001_drivepastwire_cm_textview_text": "Körning förbi tråden (/cm)", "rn_51001_panelhome_status51_textview_text": "Byggnadskarta - sekundärt område", "rn_51001_cutmode_title_textview_text": "K<PERSON><PERSON>läge", "rn_51001_panelhome_status23_textview_text": "Återgår - parkering", "rn_51001_modify_copyto_button_text": "Ko<PERSON>ra till", "rn_51001_detaillist_cutmode_textview_text": "K<PERSON><PERSON>läge", "rn_51001_modify_tuesday_textview_text": "Tisdag", "rn_51001_dashboard_alert2title_textview_text": "Valda klippområden", "rn_51001_guardagainsttheft_resetpwdfail_textview_text": "lösenord för <PERSON>ällning av robot misslyckades ， skicka igen.", "rn_51001_map_renametitle_textview_text": "Byt namn på kartan", "rn_51001_schedule_weeklyoverview_textview_text": "Veckoöversikt", "rn_51001_panelhome_cancel_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_modify_thursday_textview_text": "Torsdag", "rn_51001_alarmsetting_durationleft_textview_text": "Varaktighet för larmet", "rn_51001_guardagainsttheft_title_textview_text": "Stöldskydd", "rn_51001_dashboard_selectedareacuttingunit_textview_text": "m", "rn_51001_cutheight_done_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_panelhome_park_textview_text": "Park", "rn_51001_resetrobotpwd_successtip3_textview_text": "<PERSON>m problemet k<PERSON>år kan du kontakta en återförsäljare.", "rn_51001_panelhome_status20_textview_text": "Återgår - Klippning slutförd", "rn_51001_headlight_color_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_successtip2end_textview_text": "för att starta din robot.", "rn_51001_cutmode_selectedarea_textview_text": "<PERSON><PERSON>", "rn_51001_geofencesensitivity_setting_textview_text": "Inställning av känslighet", "rn_51001_detaillist_remotecontrol_textview_text": "Fjärrkontroll", "rn_51001_timerange_start_textview_text": "<PERSON><PERSON>", "rn_51001_resetrobotpwd_pwdnotcorrect_textview_text": "Verifieringskoden är felaktig", "rn_51001_guardagainsttheft_titlenew_textview_text": "Stöldskyddsinställningar", "rn_51001_cutmode_cuttingradius_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_panelhome_schedule_button_text": "<PERSON><PERSON><PERSON>", "rn_51001_dashboard_exceededrangewaning_textview_text": "Du har överskridit fjärrkontrollens räckvidd.", "rn_51001_backdistance_cm_textview_text": "Backavstånd (cm)", "rn_51001_headlight_red_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_failtip1_textview_text": "<PERSON><PERSON><PERSON><PERSON>, återställning av lösenord har misslyckats.", "rn_51001_dashboard_modaltitle_textview_text": "<PERSON><PERSON>", "rn_51001_headlight_schedule_textview_text": "Ljusschema", "rn_51001_resetrobotpwd_unregisteredtip3_textview_text": "Återställ sedan lösenordet här.", "rn_51001_statistics_totalstatistics_textview_text": "Total statistik", "rn_51001_headlight_on_textview_text": "Alltid på", "rn_51001_modify_addtime_textview_text": "Tidsperiod", "rn_51001_cutmode_selectedarearadius_textview_text": "<PERSON><PERSON><PERSON><PERSON> (/m)", "rn_51001_statistics_title_textview_text": "Statistik", "rn_51001_drivepastwire_titleleft_textview_text": "<PERSON><PERSON><PERSON> förbi en tråd", "rn_51001_panelhome_status46_textview_text": "Laddning klart - Felet är avhjälpt", "rn_51001_dashboard_alertmessage_textview_text": "Vill du avsluta den här uppgiften?", "rn_51001_alarmsetting_alarmenable_textview_text": "Larm vid lyftning", "rn_51001_modify_wed_button_text": "ONS", "rn_51001_modify_mon_button_text": "MÅN", "rn_51001_backhomecorridor_title_textview_text": "Till<PERSON><PERSON> hem korridor", "rn_51001_drivepastwire_title_textview_text": "<PERSON><PERSON><PERSON> förbi en tråd", "rn_51001_modify_friday_textview_text": "Fred<PERSON>", "rn_51001_cutmode_workareanote_textview_text": "Notera: Roboten klipper automatiskt gräsmattan enligt schemat och personliga inställningar.", "rn_51001_panelhome_updatemap_textview_text": "Återställ karta", "rn_51001_rainsensor_delay_textview_text": "<PERSON><PERSON><PERSON>röjningstid (/min)", "rn_51001_cutmode_edgenote_textview_text": "Notera: <PERSON><PERSON> börjar först klippa längs gräsmattans begränsningsslinga i varje nytt schema.", "rn_51001_cutmode_selectedareanote_textview_text": "När klippning av valt område är aktiverat klipper roboten området runt den aktuella punkten inom en viss radie.", "rn_51001_headlight_period_textview_text": "Ställ in tidsperioden", "rn_51001_dashboard_stopalertmsg_textview_text": "Enheten arbetar. Vill du sluta?", "rn_51001_resetrobotpwd_resettipstart_textview_text": "Vi har just skickat en 6-digital kod till din registrerade robot-e-postadress", "rn_51001_alarmsetting_title_textview_text": "Inställning av larm", "rn_51001_panelhome_secondarea_textview_text": "<PERSON><PERSON>", "rn_51001_panelhome_pickercentertitle_textview_text": "timme", "rn_51001_cutmode_sysnote_textview_text": "Roboten börjar skära i raka systematiska linjer.", "rn_51001_panelhome_status60_textview_text": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>domr<PERSON><PERSON>", "rn_51001_modify_sat_button_text": "LÖR", "rn_51001_headlight_flashes_textview_text": "Blinkar vid felvarning", "rn_51001_modify_done_button_text": "<PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_unregisteredtip1_textview_text": "Förlåt!", "rn_51001_geofencesensitivity_low_textview_text": "<PERSON><PERSON><PERSON>"}, "fi": {"rn_51001_smartexitdockingstation_note_textview_text": "Huomautus: <PERSON><PERSON>aa lankaa pitkin syöttöetäisyydelle ja lähtee sitten leik<PERSON>!", "rn_51001_modify_validate_textview_text": "Päättymisajan pitää olla myöhäisempi kuin alkamisaika", "rn_51001_panelhome_mailsemptyerror_textview_text": "Sähköposti on tyhjä, vahvistus epäonnistui", "rn_51001_geofencesensitivity_medium_textview_text": "Keskitaso", "rn_51001_modify_conflict_textview_text": "<PERSON><PERSON><PERSON><PERSON> kauden kanssa; nolla<PERSON>", "rn_51001_panelhome_status40_textview_text": "La<PERSON>us valmis – Odotetaan seuraavaa le<PERSON>a", "rn_51001_resetrobotpwd_toregister_button_text": "Rekisteröi", "rn_51001_statistics_totalworkinghours_textview_text": "Käyttötunnit yhteensä", "rn_51001_panelhome_updatemapfailnote_textview_text": "<PERSON><PERSON><PERSON>, robottens aktuelle status gør at kortet ikke kan nulstilles. Du skal først gendanne plæneklipperen.", "rn_51001_resetrobotpwd_done_button_text": "Val<PERSON>", "rn_51001_batterydetail_title_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_panelhome_status45_textview_text": "La<PERSON><PERSON> valmis – <PERSON><PERSON>i auki", "rn_51001_detaillist_cutheight_textview_text": "Leik<PERSON>ukor<PERSON><PERSON>", "rn_51001_geofencesensitivity_high_textview_text": "Korkea", "rn_51001_backhomecorridor_widthleft_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> leveys", "rn_51001_resetrobotpwd_unregisteredtip4_textview_text": "Voit my<PERSON> p<PERSON>ä apua jälleenmyyjältäsi.", "rn_51001_modify_addtime_button_text": "Lisää aika", "rn_51001_statistics_energysavingdata_textview_text": "Energiansäästöä koskevat tiedot", "rn_51001_statistics_totaldistance_textview_text": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "rn_51001_modify_sunday_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_panelhome_status21_textview_text": "Paluu – Akku vähissä", "rn_51001_panelhome_interfaceerror_textview_text": "Liitäntää ei palautettu, virhe raportoitu", "rn_51001_modal_slideronOff_textview_text": "Päällä/pois", "rn_51001_cutmode_spiral_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_override_48_textview_text": "48 t", "rn_51001_panelhome_parktiphour_textview_text": "<PERSON><PERSON><PERSON><PERSON> on oltava yksi tunti nykyistä aikaa myöhempi", "rn_51001_panelhome_updatemapmsg_textview_text": "Du nulstiller et kort. Det nye kort erstatter det aktuelle kort. Vil du fortsætte?", "rn_51001_headlight_white_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_panelhome_status30_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_panelhome_status22_textview_text": "<PERSON><PERSON><PERSON> <PERSON>", "rn_51001_resetrobotpwd_successtip1_textview_text": "<PERSON><PERSON>i salasanan nollaus onnistui.", "rn_51001_resetrobotpwd_pwdnotcompleted_textview_text": "Syötä v<PERSON>oodi", "rn_51001_resetrobotpwd_failtip2_textview_text": "Varmista, et<PERSON><PERSON> sinulla on aktiivinen yhteys ruohonleikkuriin joko <PERSON>in tai verkon kautta", "rn_51001_drivepastwire_note_textview_text": "Bemærk: <PERSON><PERSON> er a<PERSON>, som robotens forende kan køre forbi kantkablet.", "rn_51001_cutheight_inch_textview_text": "Sääd<PERSON> le<PERSON> (/tuuma)", "rn_51001_modify_sun_button_text": "SU", "rn_51001_modify_wednesday_textview_text": "Keskiviikko", "rn_51001_panelhome_otaalertmsg_textview_text": "<PERSON><PERSON> et päivitä, laite ei toimi oikein. Päivitä nyt.", "rn_51001_modify_tue_button_text": "TI", "rn_51001_resetrobotpwd_successtip2start_textview_text": "Käytä", "rn_51001_detaillist_antitheft_textview_text": "Varkaudenestoasetukset", "rn_51001_statistics_totalchargenote_textview_text": "Kokonaislatausai<PERSON> on aika, jonka ruohonleikku<PERSON> viettää latausasemalla. <PERSON><PERSON>a, että latausaika ei sisälly kokonaiskäyttötunteihin.", "rn_51001_statistics_energynote_textview_text": "EGO ruohonleikkuri tuottaa vähemmän hiilidioksidia käydessään verrattuna muihin tuotteisiin.", "rn_51001_panelhome_status50_textview_text": "<PERSON><PERSON><PERSON> kartta – Pääalue", "rn_51001_override_24_textview_text": "24 t", "rn_51001_statistics_batterydata_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_resettipsend_textview_text": ". Vahvista sähköpostiosoitteesi syöttämällä alla oleva koodi.", "rn_51001_resetrobotpwd_unregistertip_button_text": "Beklager!\nAf sikkerhedsmæssige årsager skal din robot registreres først\nhvorefter adgangskoden kan nulstilles her.\nDu kan også kontakte din forhandler for hjælp.", "rn_51001_statistics_chargingcyclenote_textview_text": "Lataussykli n<PERSON>ää, kuinka monta kertaa akku on ladattu täyteen ja tyhjent<PERSON>yt.", "rn_51001_headlight_off_textview_text": "<PERSON><PERSON> pois päältä", "rn_51001_headlight_green_textview_text": "Vihreä", "rn_51001_resetrobotpwd_sucesstitle_textview_text": "Robottens adgangskode er blevet nulstillet. Brug \"0000\" til at starte din robot. Hvis dit problem fortsætter, skal du finde en forhandler.", "rn_51001_resetrobotpwd_failtip3_textview_text": "yhteys ruo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> joko <PERSON> avulla", "rn_51001_backhomecorridor_note_textview_text": "Bemærk: <PERSON>te er bredden mellem robotten og kantkablet, når robotten kører tilbage. <PERSON><PERSON><PERSON> bredden så bred som muligt for at reducere linjer på plænen.", "rn_51001_panelhome_status44_textview_text": "La<PERSON>us val<PERSON> – Odottaa käynnistystä", "rn_51001_resetrobotpwd_failtip_textview_text": "<PERSON><PERSON><PERSON>, adgangskoden kunne ikke nulstilles.$$Sørg for, at du har en aktiv$$ forbindelse til plæneklipperen, enten via Bluetooth$$ eller et mobilnetværk.", "rn_51001_alarmsetting_duration_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kesto (/min)", "rn_51001_override_note_textview_text": "Robotti leik<PERSON>a nurmikkoa jatkuvasti asetuk<PERSON> mukaan.", "rn_51001_statistics_batteryhealth_textview_text": "<PERSON><PERSON><PERSON> kunto", "rn_51001_dashboard_exit_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_timerange_end_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_otherslist_headlight_textview_text": "Etuvalo", "rn_51001_modify_all_textview_text": "Valits<PERSON>", "rn_51001_resetrobotpwd_finddealer_button_text": "<PERSON><PERSON><PERSON> j<PERSON>", "rn_51001_panelhome_status41_textview_text": "Lataus valmis – Leikkuuaikataulua ei ole asetettu", "rn_51001_resetrobotpwd_fail_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_cutmode_workarea_textview_text": "Työalue", "rn_51001_override_72_textview_text": "72 t", "rn_51001_resetrobotpwd_failtip4_textview_text": "tai mobiiliverkkoon.", "rn_51001_panelhome_updatemapfailnote4_textview_text": "<PERSON><PERSON><PERSON>, robottens aktuelle status gør at kortet ikke kan nulstilles. Du skal først gendanne plæneklipperen.", "rn_51001_cutmode_econote_textview_text": "Huomautus: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> on leikattu, se ajaa sen yli nopeasti, kunnes saavuttaa leik<PERSON>amattoman nurmikon!", "rn_51001_resetrobotpwd_success_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_panelhome_mainarea_textview_text": "Pääalue", "rn_51001_panelhome_status11_textview_text": "Tauko – P<PERSON>äytys", "rn_51001_modify_saturday_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_backhomecorridor_width_textview_text": "Käytä<PERSON><PERSON><PERSON> leveys (/mm)", "rn_51001_panelhome_pickerlefttitle_textview_text": "päivämäärä", "rn_51001_override_done_button_text": "Val<PERSON>", "rn_51001_cutmode_workareasecond_textview_text": "<PERSON><PERSON>", "rn_51001_modify_delete_button_text": "Poista", "rn_51001_alarmsetting_switch_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as<PERSON>", "rn_51001_modify_fri_button_text": "PE", "rn_51001_statistics_totaltravelnote_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON> on ruohonleik<PERSON><PERSON> kulkema matka kaikissa tiloissa.", "rn_51001_resetrobotpwd_problemsolved_button_text": "<PERSON><PERSON><PERSON> ratkai<PERSON>u", "rn_51001_smartexitdockingstation_title_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_panelhome_status25_textview_text": "Paluu – Aikataulun ulkopuolella", "rn_51001_detaillist_others_textview_text": "<PERSON><PERSON>", "rn_51001_panelhome_mapsettings_textview_text": "Kartan as<PERSON>uk<PERSON>", "rn_51001_panelhome_updatetitle_textview_text": "Uusi päivitys saatavilla", "rn_51001_panelhome_status14_textview_text": "Tauko – Leikkuukorkeuden säätö", "rn_51001_detaillist_functionsettings_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_panelhome_status27_textview_text": "Paluu – Kartan uudelleenluonti", "rn_51001_backdistance_titleleft_textview_text": "Takaetäisyys", "rn_51001_panelhome_parktipmax_textview_text": "Asetusaika saa olla enintään 99 tuntia", "rn_51001_panelhome_updatemapfailnote7_textview_text": "<PERSON><PERSON><PERSON>, robottens aktuelle status gør at kortet ikke kan nulstilles. Du skal først gendanne plæneklipperen.", "rn_51001_dashboard_modalmessage_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON> molempia painik<PERSON>ita y<PERSON>des<PERSON>ä", "rn_51001_guardagainsttheft_alarmsetting_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as<PERSON>", "rn_51001_panelhome_devicedidreceivecmd_textview_text": "<PERSON><PERSON> vast<PERSON>i salasanan nollauskomennon onnistuneesti", "rn_51001_detaillist_bordermanagement_textview_text": "<PERSON><PERSON>", "rn_51001_panelhome_pickerrighttitle_textview_text": "minu<PERSON>i", "rn_51001_panelhome_updatemapfailnote6_textview_text": "<PERSON><PERSON><PERSON>, robottens aktuelle status gør at kortet ikke kan nulstilles. Du skal først gendanne plæneklipperen.", "rn_51001_cutmode_sys_textview_text": "Järjestelmällinen leikkuu", "rn_51001_panelhome_status26_textview_text": "Pa<PERSON><PERSON> – Latausaseman kutsu", "rn_51001_schedulemanagement_title_textview_text": "<PERSON><PERSON><PERSON><PERSON> hallinta", "rn_51001_dashboard_alert2subtitle_textview_text": "Leik<PERSON>ussä<PERSON> (/ m)", "rn_51001_panelhome_mowingpath_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_detaillist_availablesettings_textview_text": "Käytettävissä olevat asetukset", "rn_51001_panelhome_status13_textview_text": "Tauko – Vika", "rn_51001_bordermanagement_title_textview_text": "<PERSON><PERSON>", "rn_51001_guardagainsttheft_resetrobotpwd_textview_text": "<PERSON><PERSON><PERSON> robotin sa<PERSON>ana", "rn_51001_guardagainsttheft_geofencesetting_textview_text": "Geofence-asetukset", "rn_51001_panelhome_status12_textview_text": "Tauko – Signaaliristiriita", "rn_51001_statistics_chargingcycle_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_successtipend_textview_text": "<PERSON>in k<PERSON>.$$Jos ongelmasi jatkuu edelleen, etsi jälle<PERSON>.", "rn_51001_panelhome_updatemapfailnote5_textview_text": "<PERSON><PERSON><PERSON>, robottens aktuelle status gør at kortet ikke kan nulstilles. Du skal først gendanne plæneklipperen.", "rn_51001_cutmode_edge_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_modify_period_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_cutheight_title_textview_text": "Leik<PERSON>ukor<PERSON><PERSON>", "rn_51001_weeklyoverview_title_textview_text": "Viikoittainen yleiskatsaus", "rn_51001_panelhome_status61_textview_text": "Leika<PERSON><PERSON> ruohoa – <PERSON><PERSON><PERSON><PERSON> alue", "rn_51001_resetrobotpwd_unregisteredtip2_textview_text": "Turva<PERSON>uude<PERSON> vuoksi robotti on ensin rekisteröitävä ja", "rn_51001_cutheight_mm_textview_text": "Säädä leikkuukor<PERSON>utta (/mm)", "rn_51001_statistics_totalco2savings_textview_text": "CO2-säästöt yhteensä", "rn_51001_panelhome_status24_textview_text": "<PERSON><PERSON><PERSON> – Rajasignaaliristiriita", "rn_51001_schedule_override_textview_text": "<PERSON><PERSON>", "rn_51001_cutmode_spiralsensitivity_textview_text": "Herkkyystaso", "rn_51001_cutheight_titleleft_textview_text": "Säädä le<PERSON>or<PERSON>utta", "rn_51001_statistics_totalenergyconsumption_textview_text": "Energian kokonaiskulutus", "rn_51001_panelhome_satellitemap_textview_text": "<PERSON>tel<PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_otherslist_rainsensornote_textview_text": "Bemærk: <PERSON><PERSON> robotten registrerer regn, kører robotten tilbage til ladestationen. Den starter igen, når regnsensoren er tør og udsættelsestiden er gået.", "rn_51001_modify_monday_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_detaillist_schedule_textview_text": "<PERSON><PERSON><PERSON><PERSON> hallinta", "rn_51001_panelhome_done_textview_text": "Val<PERSON>", "rn_51001_resetrobotpwd_pwdcheckedsuccess_textview_text": "Vahvistuskoodi vahvistus on<PERSON>, lähetä nollauskomento", "rn_51001_cutmode_workareamain_textview_text": "Pääalue", "rn_51001_panelhome_status42_textview_text": "<PERSON><PERSON><PERSON> valmis – Viive sateen takia", "rn_51001_modify_noschedule_textview_text": "Ei aikataulua", "rn_51001_geofencesetting_title_textview_text": "Geofence-asetus", "rn_51001_modify_setperiod_textview_text": "<PERSON><PERSON>", "rn_51001_panelhome_status10_textview_text": "<PERSON><PERSON>", "rn_51001_headlight_blue_textview_text": "<PERSON><PERSON>", "rn_51001_cutmode_spiralnote_textview_text": "<PERSON><PERSON> le<PERSON> ruohon s<PERSON>, jos ruoho on pidempi tietyllä al<PERSON>.", "rn_51001_resetrobotpwd_title_textview_text": "<PERSON><PERSON><PERSON> robotin sa<PERSON>ana", "rn_51001_resetrobotpwd_successtipstart_textview_text": "Robottisi salasanan nollaus onnistui.$$Käytä ”XXXX” käynnistääks<PERSON> robotin", "rn_51001_panelhome_geofencesetting_textview_text": "Geofence-asetus", "rn_51001_panelhome_status43_textview_text": "Lataus valmis – Pysäköinti", "rn_51001_panelhome_parkuntil_textview_text": "Pysäk<PERSON><PERSON> kunnes", "rn_51001_resetrobotpwd_resetagain_button_text": "<PERSON><PERSON><PERSON>", "rn_51001_override_0_textview_text": "0 t", "rn_51001_panelhome_parktipmin_textview_text": "<PERSON><PERSON><PERSON><PERSON> on oltava suurempi kuin nykyinen aika.", "rn_51001_dashboard_alerttitle_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_modify_thu_button_text": "TO", "rn_51001_statistics_consumptionunit_textview_text": "A/H", "rn_51001_otherslist_rainsensor_textview_text": "<PERSON><PERSON><PERSON><PERSON> as<PERSON>", "rn_51001_cutmode_eco_textview_text": "ECO-leikkuu", "rn_51001_resetrobotpwd_unregisteredtip_textview_text": "Beklager! Af sikkerhedsmæssige årsager skal din robot registreres først hvorefter adgangskoden kan nulstilles her. Du kan også kontakte din forhandler for hjælp.", "rn_51001_statistics_totalchargetime_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_drivepastwire_cm_textview_text": "<PERSON><PERSON> langan ohi (/cm)", "rn_51001_panelhome_status51_textview_text": "<PERSON><PERSON><PERSON> ka<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON> alue", "rn_51001_cutmode_title_textview_text": "Leikkaustila", "rn_51001_panelhome_status23_textview_text": "Paluu – Pysäköinti", "rn_51001_modify_copyto_button_text": "<PERSON><PERSON><PERSON> k<PERSON>n", "rn_51001_detaillist_cutmode_textview_text": "Leikkaustila", "rn_51001_modify_tuesday_textview_text": "Tiistai", "rn_51001_dashboard_alert2title_textview_text": "<PERSON><PERSON><PERSON> al<PERSON> le<PERSON>", "rn_51001_guardagainsttheft_resetpwdfail_textview_text": "<PERSON>in salasanan nollaus ep<PERSON>on<PERSON>, lähet<PERSON> uudelleen.", "rn_51001_map_renametitle_textview_text": "Nimeä kartta uudelleen", "rn_51001_schedule_weeklyoverview_textview_text": "Viikoittainen yleiskatsaus", "rn_51001_panelhome_cancel_textview_text": "Peruuta", "rn_51001_modify_thursday_textview_text": "Tor<PERSON><PERSON>", "rn_51001_alarmsetting_durationleft_textview_text": "Hä<PERSON><PERSON>ksen kesto", "rn_51001_guardagainsttheft_title_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_dashboard_selectedareacuttingunit_textview_text": "m", "rn_51001_cutheight_done_textview_text": "Val<PERSON>", "rn_51001_panelhome_park_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_successtip3_textview_text": "<PERSON><PERSON> j<PERSON> edelleen, etsi j<PERSON><PERSON>.", "rn_51001_panelhome_status20_textview_text": "Paluu – Ruohonleikkuu valmis", "rn_51001_headlight_color_textview_text": "Valon väri", "rn_51001_resetrobotpwd_successtip2end_textview_text": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "rn_51001_cutmode_selectedarea_textview_text": "<PERSON><PERSON><PERSON> al<PERSON> le<PERSON>", "rn_51001_geofencesensitivity_setting_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON> asetus", "rn_51001_detaillist_remotecontrol_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_timerange_start_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_pwdnotcorrect_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on vir<PERSON><PERSON><PERSON>", "rn_51001_guardagainsttheft_titlenew_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_cutmode_cuttingradius_textview_text": "Leikkuusäde", "rn_51001_panelhome_schedule_button_text": "Aikataulu", "rn_51001_dashboard_exceededrangewaning_textview_text": "<PERSON><PERSON>t etäohjauksella hallittavan alueen.", "rn_51001_backdistance_cm_textview_text": "Takaetäisyys (/cm)", "rn_51001_headlight_red_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_failtip1_textview_text": "Valitettavasti salasanan nollaaminen epäonnistui.", "rn_51001_dashboard_modaltitle_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON> le<PERSON>", "rn_51001_headlight_schedule_textview_text": "Valon aikataulu", "rn_51001_resetrobotpwd_unregisteredtip3_textview_text": "nollaa sitten salasana tä<PERSON>ll<PERSON>.", "rn_51001_statistics_totalstatistics_textview_text": "Tilastot yhteensä", "rn_51001_headlight_on_textview_text": "<PERSON><PERSON>", "rn_51001_modify_addtime_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_cutmode_selectedarearadius_textview_text": "Leik<PERSON>ussä<PERSON> (/m)", "rn_51001_statistics_title_textview_text": "Tilastot", "rn_51001_drivepastwire_titleleft_textview_text": "<PERSON><PERSON> langan ohi", "rn_51001_panelhome_status46_textview_text": "<PERSON><PERSON><PERSON> valmis – <PERSON><PERSON> poistamatta", "rn_51001_dashboard_alertmessage_textview_text": "<PERSON>uatko lopettaa tämän tehtävän?", "rn_51001_alarmsetting_alarmenable_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_modify_wed_button_text": "KE", "rn_51001_modify_mon_button_text": "MA", "rn_51001_backhomecorridor_title_textview_text": "<PERSON><PERSON><PERSON> kotikäytävään", "rn_51001_drivepastwire_title_textview_text": "<PERSON><PERSON> langan ohi", "rn_51001_modify_friday_textview_text": "Perjan<PERSON>", "rn_51001_cutmode_workareanote_textview_text": "Huomautus: <PERSON><PERSON> le<PERSON>a nurmikon automaattisesti aikataulun ja henkilökohtaisten as<PERSON><PERSON> mukaan.", "rn_51001_panelhome_updatemap_textview_text": "Nollaa kartta", "rn_51001_rainsensor_delay_textview_text": "Viiveaika (/min)", "rn_51001_cutmode_edgenote_textview_text": "Huomautus: <PERSON><PERSON> alkaa leikata nurmikkoa rajalankaa pitkin aina ensimmäiseksi joka<PERSON>sa uudessa aikata<PERSON>.", "rn_51001_cutmode_selectedareanote_textview_text": "Kun valitun alueen leikka<PERSON>n on päällä, robotti leik<PERSON>a nykyisen pisteen ympärillä olevan alueen tietyllä säteellä.", "rn_51001_headlight_period_textview_text": "<PERSON><PERSON>", "rn_51001_dashboard_stopalertmsg_textview_text": "Laite on to<PERSON><PERSON><PERSON><PERSON>. Haluatko lo<PERSON>?", "rn_51001_resetrobotpwd_resettipstart_textview_text": "Olemme juuri l<PERSON>ttäneet 6-nume<PERSON>en koodin robottisi rekisteröityyn sähköpostiosoitteeseen", "rn_51001_alarmsetting_title_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as<PERSON>", "rn_51001_panelhome_secondarea_textview_text": "<PERSON><PERSON>", "rn_51001_panelhome_pickercentertitle_textview_text": "tunti", "rn_51001_cutmode_sysnote_textview_text": "Robotti alkaa leikata suoria, järjestelmällisiä kaistaleita.", "rn_51001_panelhome_status60_textview_text": "Leikataan ruohoa – Pääalue", "rn_51001_modify_sat_button_text": "LA", "rn_51001_headlight_flashes_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kun vika<PERSON>", "rn_51001_modify_done_button_text": "Val<PERSON>", "rn_51001_resetrobotpwd_unregisteredtip1_textview_text": "<PERSON>teeks<PERSON>!", "rn_51001_geofencesensitivity_low_textview_text": "<PERSON><PERSON><PERSON>"}, "en-US": {"rn_51001_smartexitdockingstation_note_textview_text": "Note: The robot backs along the wire to the input distance and then departs to mow!", "rn_51001_modify_validate_textview_text": "End time must be later than start time", "rn_51001_panelhome_mailsemptyerror_textview_text": "Email is empty, verification failed", "rn_51001_geofencesensitivity_medium_textview_text": "Medium", "rn_51001_modify_conflict_textview_text": "Conflict with current period; please reset", "rn_51001_panelhome_status40_textview_text": "Charging Completed - Waiting for next mowing schedule", "rn_51001_resetrobotpwd_toregister_button_text": "To register", "rn_51001_statistics_totalworkinghours_textview_text": "Total operating hours", "rn_51001_panelhome_updatemapfailnote_textview_text": "Sorry, the current robot status does not allow to reset map, please recove the mower first.", "rn_51001_resetrobotpwd_done_button_text": "Done", "rn_51001_batterydetail_title_textview_text": "Battery information", "rn_51001_panelhome_status45_textview_text": "Charging Completed - Lid Open", "rn_51001_detaillist_cutheight_textview_text": "Cut height", "rn_51001_geofencesensitivity_high_textview_text": "High", "rn_51001_backhomecorridor_widthleft_textview_text": "Corridor width", "rn_51001_resetrobotpwd_unregisteredtip4_textview_text": "Or you can contact your dealer for help.", "rn_51001_modify_addtime_button_text": "Add time", "rn_51001_statistics_energysavingdata_textview_text": "Energy savings data", "rn_51001_statistics_totaldistance_textview_text": "Total distance travelled", "rn_51001_modify_sunday_textview_text": "Sunday", "rn_51001_panelhome_status21_textview_text": "Returning - Low battery", "rn_51001_panelhome_interfaceerror_textview_text": "Interface not returned, error reported", "rn_51001_modal_slideronOff_textview_text": "On/Off", "rn_51001_cutmode_spiral_textview_text": "Spiral cutting", "rn_51001_override_48_textview_text": "48 hrs", "rn_51001_panelhome_parktiphour_textview_text": "The setting time must be one hour later than the current time.", "rn_51001_panelhome_updatemapmsg_textview_text": "You are reseting a map. The new map will replace the current one. Do you want to continue?", "rn_51001_headlight_white_textview_text": "White", "rn_51001_panelhome_status30_textview_text": "Charging", "rn_51001_panelhome_status22_textview_text": "Returning - <PERSON><PERSON>", "rn_51001_resetrobotpwd_successtip1_textview_text": "Your robot password has been reset successfully.", "rn_51001_resetrobotpwd_pwdnotcompleted_textview_text": "Please input the verify code", "rn_51001_resetrobotpwd_failtip2_textview_text": "Please make sure that you have an active connection to your mower, either using Bluetooth or the network.", "rn_51001_drivepastwire_note_textview_text": "Note: This is the distance that the front of the robot can move past the boundary wire.", "rn_51001_cutheight_inch_textview_text": "Adjust cut height (/inch)", "rn_51001_modify_sun_button_text": "SUN", "rn_51001_modify_wednesday_textview_text": "Wednesday", "rn_51001_panelhome_otaalertmsg_textview_text": "If you do not update, the device will not work properly. Please update now.", "rn_51001_modify_tue_button_text": "TUE", "rn_51001_resetrobotpwd_successtip2start_textview_text": "Please use", "rn_51001_detaillist_antitheft_textview_text": "Anti-theft settings", "rn_51001_statistics_totalchargenote_textview_text": "The total charging time is the time spent by the mower at the charging station. Please note that the charging time is not included in the total operating hours.", "rn_51001_statistics_energynote_textview_text": "Compared with other products, the EGO mower produces less total carbon dioxide when operating.", "rn_51001_panelhome_status50_textview_text": "Building Map - Main Area", "rn_51001_override_24_textview_text": "24 hrs", "rn_51001_statistics_batterydata_textview_text": "Battery data", "rn_51001_resetrobotpwd_resettipsend_textview_text": ". Please enter the code below to verify your email address.", "rn_51001_resetrobotpwd_unregistertip_button_text": "Sorry!\nFor safety, your robot must be register first and\n then reset the password here.\nOr you can contract your dealer for help.", "rn_51001_statistics_chargingcyclenote_textview_text": "The charging cycle shows the number of times the battery has been fully charged and discharged.", "rn_51001_headlight_off_textview_text": "Always OFF", "rn_51001_headlight_green_textview_text": "Green", "rn_51001_resetrobotpwd_sucesstitle_textview_text": "Your robot password has been reset successfully. Please use “0000” to start your robot If your problem is still there, you can find a dealer.", "rn_51001_resetrobotpwd_failtip3_textview_text": "connection to your mower, either using Bluetooth", "rn_51001_backhomecorridor_note_textview_text": "Note: This is the width between the robot and the boundary wire when robot returns. Make the width as wide as possible to reduce lines in the lawn.", "rn_51001_panelhome_status44_textview_text": "Charging Completed - Waiting for start", "rn_51001_resetrobotpwd_failtip_textview_text": "Sorry, reset password has failed.\nPlease make sure that you have an active\n connection to your mower, either using Bluetooth\n or the mobile network.", "rn_51001_alarmsetting_duration_textview_text": "Duration of alarm (/min)", "rn_51001_override_note_textview_text": "The robot will mow the lawn continuously based on your settings.", "rn_51001_statistics_batteryhealth_textview_text": "Battery health", "rn_51001_dashboard_exit_textview_text": "Exit", "rn_51001_timerange_end_textview_text": "Finish", "rn_51001_otherslist_headlight_textview_text": "Headlight", "rn_51001_modify_all_textview_text": "Select all", "rn_51001_resetrobotpwd_finddealer_button_text": "Find dealer", "rn_51001_panelhome_status41_textview_text": "Charging Completed - Mowing schedule not set", "rn_51001_resetrobotpwd_fail_textview_text": "Failed", "rn_51001_cutmode_workarea_textview_text": "Work area", "rn_51001_override_72_textview_text": "72 hrs", "rn_51001_resetrobotpwd_failtip4_textview_text": "or the mobile network.", "rn_51001_panelhome_updatemapfailnote4_textview_text": "Sorry, the current robot status does not allow to reset map, please recove the mower first.", "rn_51001_cutmode_econote_textview_text": "Note: If the robot detects that the lawn has been cut, it will drive over it quickly until it reaches uncut grass!", "rn_51001_resetrobotpwd_success_textview_text": "Success", "rn_51001_panelhome_mainarea_textview_text": "Main area", "rn_51001_panelhome_status11_textview_text": "Pause - Stop", "rn_51001_modify_saturday_textview_text": "Saturday", "rn_51001_backhomecorridor_width_textview_text": "Corridor width (/mm)", "rn_51001_panelhome_pickerlefttitle_textview_text": "date", "rn_51001_override_done_button_text": "Done", "rn_51001_cutmode_workareasecond_textview_text": "Second area", "rn_51001_modify_delete_button_text": "Delete", "rn_51001_alarmsetting_switch_textview_text": "Alarm Setting", "rn_51001_modify_fri_button_text": "FRI", "rn_51001_statistics_totaltravelnote_textview_text": "The total travel distance is the distance travelled by the mower in all modes.", "rn_51001_resetrobotpwd_problemsolved_button_text": "Problem solved", "rn_51001_smartexitdockingstation_title_textview_text": "Exit docking station", "rn_51001_panelhome_status25_textview_text": "Returning - Out of schedule", "rn_51001_detaillist_others_textview_text": "Others", "rn_51001_panelhome_mapsettings_textview_text": "Map settings", "rn_51001_panelhome_updatetitle_textview_text": "New update available", "rn_51001_panelhome_status14_textview_text": "Pause - Cut Height Adjusting", "rn_51001_detaillist_functionsettings_textview_text": "Function settings", "rn_51001_panelhome_status27_textview_text": "Returning - Rebuild Map", "rn_51001_backdistance_titleleft_textview_text": "Back distance", "rn_51001_panelhome_parktipmax_textview_text": "The setting time cannot exceed 99 hrs", "rn_51001_panelhome_updatemapfailnote7_textview_text": "    Sorry, the current robot status does not allow to reset map, please recove the mower first.", "rn_51001_dashboard_modalmessage_textview_text": "Press both buttons together to start", "rn_51001_guardagainsttheft_alarmsetting_textview_text": "Alarm setting", "rn_51001_panelhome_devicedidreceivecmd_textview_text": "The device successfully received the reset password command", "rn_51001_detaillist_bordermanagement_textview_text": "Border management", "rn_51001_panelhome_pickerrighttitle_textview_text": "minute", "rn_51001_panelhome_updatemapfailnote6_textview_text": "    Sorry, the current robot status does not allow to reset map, please recove the mower first.", "rn_51001_cutmode_sys_textview_text": "Systematic Cutting", "rn_51001_panelhome_status26_textview_text": "Returning - Called by Charging Station", "rn_51001_schedulemanagement_title_textview_text": "Schedule management", "rn_51001_dashboard_alert2subtitle_textview_text": "Cutting radius (/ m)", "rn_51001_panelhome_mowingpath_textview_text": "Mowing path", "rn_51001_detaillist_availablesettings_textview_text": "Available settings", "rn_51001_panelhome_status13_textview_text": "<PERSON><PERSON> <PERSON> <PERSON><PERSON>", "rn_51001_bordermanagement_title_textview_text": "Border management", "rn_51001_guardagainsttheft_resetrobotpwd_textview_text": "Reset Robot Password", "rn_51001_guardagainsttheft_geofencesetting_textview_text": "Geo-fence settings", "rn_51001_panelhome_status12_textview_text": "Pause - Signal Conflict", "rn_51001_statistics_chargingcycle_textview_text": "Charging cycle", "rn_51001_resetrobotpwd_successtipend_textview_text": "to start your robot.$$If your problem is still there, you can find a dealer.", "rn_51001_panelhome_updatemapfailnote5_textview_text": "    Sorry, the current robot status does not allow to reset map, please recove the mower first.", "rn_51001_cutmode_edge_textview_text": "Edge cutting", "rn_51001_modify_period_textview_text": "Time period", "rn_51001_cutheight_title_textview_text": "Cut height", "rn_51001_weeklyoverview_title_textview_text": "Weekly summary", "rn_51001_panelhome_status61_textview_text": "Mowing - Secondary Area", "rn_51001_resetrobotpwd_unregisteredtip2_textview_text": "For safety, your robot must be registered first and", "rn_51001_cutheight_mm_textview_text": "Adjust cut height (/mm)", "rn_51001_statistics_totalco2savings_textview_text": "Total CO2 savings", "rn_51001_panelhome_status24_textview_text": "Returning - Border Signal Conflicting", "rn_51001_schedule_override_textview_text": "Override schedule", "rn_51001_cutmode_spiralsensitivity_textview_text": "Sensitivity level", "rn_51001_cutheight_titleleft_textview_text": "Adjust cut height", "rn_51001_statistics_totalenergyconsumption_textview_text": "Total energy consumption", "rn_51001_panelhome_satellitemap_textview_text": "Satellite map", "rn_51001_otherslist_rainsensornote_textview_text": "Note: When rain is detected, the robot retuns to the charging station and restarts until the rain sensor is dry and delay time is over.", "rn_51001_modify_monday_textview_text": "Monday", "rn_51001_detaillist_schedule_textview_text": "Schedule management", "rn_51001_panelhome_done_textview_text": "Done", "rn_51001_resetrobotpwd_pwdcheckedsuccess_textview_text": "Verification code verification successful, send reset command", "rn_51001_cutmode_workareamain_textview_text": "Main area", "rn_51001_panelhome_status42_textview_text": "Charging Completed - Rain Delaying", "rn_51001_modify_noschedule_textview_text": "No schedule", "rn_51001_geofencesetting_title_textview_text": "Geo-fence setting", "rn_51001_modify_setperiod_textview_text": "Set time period", "rn_51001_panelhome_status10_textview_text": "Pause", "rn_51001_headlight_blue_textview_text": "Blue", "rn_51001_cutmode_spiralnote_textview_text": "The robot will cut the grass in a spiral pattern if the grass is longer in a certain area.", "rn_51001_resetrobotpwd_title_textview_text": "Reset Robot Password", "rn_51001_resetrobotpwd_successtipstart_textview_text": "Your robot password has been reset successfully. Please use \"XXXX\" to start your robot.", "rn_51001_panelhome_geofencesetting_textview_text": "Geo-fence Setting", "rn_51001_panelhome_status43_textview_text": "Charging Completed - Parking", "rn_51001_panelhome_parkuntil_textview_text": "Park until", "rn_51001_resetrobotpwd_resetagain_button_text": "Reset again", "rn_51001_override_0_textview_text": "0 hrs", "rn_51001_panelhome_parktipmin_textview_text": "The setting time must be greater than the current time.", "rn_51001_dashboard_alerttitle_textview_text": "Tip", "rn_51001_modify_thu_button_text": "THU", "rn_51001_statistics_consumptionunit_textview_text": "A/H", "rn_51001_otherslist_rainsensor_textview_text": "Rain sensor setting", "rn_51001_cutmode_eco_textview_text": "ECO cutting", "rn_51001_resetrobotpwd_unregisteredtip_textview_text": "Sorry!For safety, your robot must be register first and then reset the password here.Or you can contract your dealer for help.", "rn_51001_statistics_totalchargetime_textview_text": "Total charge time", "rn_51001_drivepastwire_cm_textview_text": "Drive past wire (/cm)", "rn_51001_panelhome_status51_textview_text": "Building Map - Secondary Area", "rn_51001_cutmode_title_textview_text": "Cutting mode", "rn_51001_panelhome_status23_textview_text": "Returning - Park", "rn_51001_modify_copyto_button_text": "Copy to", "rn_51001_detaillist_cutmode_textview_text": "Cutting mode", "rn_51001_modify_tuesday_textview_text": "Tuesday", "rn_51001_dashboard_alert2title_textview_text": "Selected area cutting", "rn_51001_guardagainsttheft_resetpwdfail_textview_text": "robot reset password failed，please send again.", "rn_51001_map_renametitle_textview_text": "Rename map", "rn_51001_schedule_weeklyoverview_textview_text": "Weekly overview", "rn_51001_panelhome_cancel_textview_text": "Cancel", "rn_51001_modify_thursday_textview_text": "Thursday", "rn_51001_alarmsetting_durationleft_textview_text": "Duration of alarm", "rn_51001_guardagainsttheft_title_textview_text": "Anti-theft protection", "rn_51001_dashboard_selectedareacuttingunit_textview_text": "m", "rn_51001_cutheight_done_textview_text": "Done", "rn_51001_panelhome_park_textview_text": "Park", "rn_51001_resetrobotpwd_successtip3_textview_text": "If your problem is still there, you can find a dealer.", "rn_51001_panelhome_status20_textview_text": "Returning - <PERSON><PERSON> Completed", "rn_51001_headlight_color_textview_text": "Light colour", "rn_51001_resetrobotpwd_successtip2end_textview_text": "to start your robot.", "rn_51001_cutmode_selectedarea_textview_text": "Selected Area cutting", "rn_51001_geofencesensitivity_setting_textview_text": "Sensitivity setting", "rn_51001_detaillist_remotecontrol_textview_text": "Remote control", "rn_51001_timerange_start_textview_text": "Start", "rn_51001_resetrobotpwd_pwdnotcorrect_textview_text": "The verify code is incorrect", "rn_51001_guardagainsttheft_titlenew_textview_text": "Anti theft setting", "rn_51001_cutmode_cuttingradius_textview_text": "Cutting radius", "rn_51001_panelhome_schedule_button_text": "Schedule", "rn_51001_dashboard_exceededrangewaning_textview_text": "You have exceeded the controllable range of the remote.", "rn_51001_backdistance_cm_textview_text": "Back distance (/cm)", "rn_51001_headlight_red_textview_text": "Red", "rn_51001_resetrobotpwd_failtip1_textview_text": "Sorry, reset password has failed.", "rn_51001_dashboard_modaltitle_textview_text": "Start cutting motor", "rn_51001_headlight_schedule_textview_text": "Light schedule", "rn_51001_resetrobotpwd_unregisteredtip3_textview_text": "then reset the password here.", "rn_51001_statistics_totalstatistics_textview_text": "Total statistics", "rn_51001_headlight_on_textview_text": "Always ON", "rn_51001_modify_addtime_textview_text": "Time period", "rn_51001_cutmode_selectedarearadius_textview_text": "Cutting radius (/m)", "rn_51001_statistics_title_textview_text": "Statistics", "rn_51001_drivepastwire_titleleft_textview_text": "Drive past wire", "rn_51001_panelhome_status46_textview_text": "Charging Completed - <PERSON><PERSON> not eliminated", "rn_51001_dashboard_alertmessage_textview_text": "Do you want to quit this task?", "rn_51001_alarmsetting_alarmenable_textview_text": "Alarm when lifted", "rn_51001_modify_wed_button_text": "WED", "rn_51001_modify_mon_button_text": "MON", "rn_51001_backhomecorridor_title_textview_text": "Back home corridor", "rn_51001_drivepastwire_title_textview_text": "Drive past wire", "rn_51001_modify_friday_textview_text": "Friday", "rn_51001_cutmode_workareanote_textview_text": "Note: The robot will automatically mow the lawn according to the schedule and personalised settings.", "rn_51001_panelhome_updatemap_textview_text": "Reset map", "rn_51001_rainsensor_delay_textview_text": "Delay time (/min)", "rn_51001_cutmode_edgenote_textview_text": "Notice: the robot starts cutting along the boundary wire of the lawn first in every new schedule.", "rn_51001_cutmode_selectedareanote_textview_text": "When selected area cutting is on, the robot will cut the area around the current point within a certain radius.", "rn_51001_headlight_period_textview_text": "Set time period", "rn_51001_dashboard_stopalertmsg_textview_text": "<PERSON><PERSON> is operating. Do you want to stop?", "rn_51001_resetrobotpwd_resettipstart_textview_text": "We have just sent a 6- digit code to your registered robot email address", "rn_51001_alarmsetting_title_textview_text": "Alarm setting", "rn_51001_panelhome_secondarea_textview_text": "Second area", "rn_51001_panelhome_pickercentertitle_textview_text": "hour", "rn_51001_cutmode_sysnote_textview_text": "The robot starts cutting in straight systematic lines.", "rn_51001_panelhome_status60_textview_text": "Mowing - Main Area", "rn_51001_modify_sat_button_text": "SAT", "rn_51001_headlight_flashes_textview_text": "Flashes for a fault alert", "rn_51001_modify_done_button_text": "Done", "rn_51001_resetrobotpwd_unregisteredtip1_textview_text": "Sorry!", "rn_51001_geofencesensitivity_low_textview_text": "Low"}, "dk": {"rn_51001_smartexitdockingstation_note_textview_text": "Bemærk: <PERSON><PERSON> bakker den indtastede afstand langs kablet, h<PERSON><PERSON><PERSON> den begynder at klippe græsset derfra!", "rn_51001_modify_validate_textview_text": "Sluttidspunktet må ikke være senere end starttidspunktet", "rn_51001_panelhome_mailsemptyerror_textview_text": "E-mail-feltet er tomt. Bekræftelsen mislykkedes", "rn_51001_geofencesensitivity_medium_textview_text": "Medium", "rn_51001_modify_conflict_textview_text": "Konflikt med aktuelle periode. Nulstil perioden", "rn_51001_panelhome_status40_textview_text": "Opladning færdig - Venter på næste klippeplan", "rn_51001_resetrobotpwd_toregister_button_text": "Sådan registrerer du dit produkt", "rn_51001_statistics_totalworkinghours_textview_text": "<PERSON>let antal driftstimer", "rn_51001_panelhome_updatemapfailnote_textview_text": "Sorry, de huidige robotstatus maakt het niet mogelijk om de kaart te resetten. Herstel e<PERSON>t de ma<PERSON>er.", "rn_51001_resetrobotpwd_done_button_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_batterydetail_title_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_panelhome_status45_textview_text": "Opladning færdig - Låget er åbent", "rn_51001_detaillist_cutheight_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_geofencesensitivity_high_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_backhomecorridor_widthleft_textview_text": "Bredde på gang", "rn_51001_resetrobotpwd_unregisteredtip4_textview_text": "<PERSON>r du kan kontakte din forhandler for hjælp.", "rn_51001_modify_addtime_button_text": "<PERSON><PERSON><PERSON><PERSON><PERSON> tid", "rn_51001_statistics_energysavingdata_textview_text": "Oplysninger om energibesparelser", "rn_51001_statistics_totaldistance_textview_text": "Samlet tilbagelagt afstand", "rn_51001_modify_sunday_textview_text": "<PERSON><PERSON><PERSON>g", "rn_51001_panelhome_status21_textview_text": "<PERSON><PERSON><PERSON> hjem - <PERSON><PERSON>t batteristr<PERSON>m", "rn_51001_panelhome_interfaceerror_textview_text": "Grænsefladen blev ikke <PERSON>eret, fejl rapporteret", "rn_51001_modal_slideronOff_textview_text": "Tænd/sluk", "rn_51001_cutmode_spiral_textview_text": "Spiralklipning", "rn_51001_override_48_textview_text": "48 timer", "rn_51001_panelhome_parktiphour_textview_text": "Indstillingstiden skal være over en time efter det aktuelle klokkeslæt.", "rn_51001_panelhome_updatemapmsg_textview_text": "U bent een kaart aan het resetten. De nieuwe kaart vervangt de huidige kaart. Wilt u doorgaan?", "rn_51001_headlight_white_textview_text": "<PERSON><PERSON>", "rn_51001_panelhome_status30_textview_text": "Lader op", "rn_51001_panelhome_status22_textview_text": "<PERSON><PERSON><PERSON> hje<PERSON> - Regner", "rn_51001_resetrobotpwd_successtip1_textview_text": "Robottens adgangskode er blevet nulstillet.", "rn_51001_resetrobotpwd_pwdnotcompleted_textview_text": "Indtast bekræftelseskoden", "rn_51001_resetrobotpwd_failtip2_textview_text": "<PERSON><PERSON><PERSON> for, at du har en aktiv forbindelse til din plæneklipper enten via Bluetooth eller via et netværk.", "rn_51001_drivepastwire_note_textview_text": "Opmerking: Di<PERSON> is de afstand die de voorkant van de robot voorbij de begrenzingsdraad kan afleggen.", "rn_51001_cutheight_inch_textview_text": "<PERSON><PERSON> (/tomme)", "rn_51001_modify_sun_button_text": "SØN", "rn_51001_modify_wednesday_textview_text": "Onsdag", "rn_51001_panelhome_otaalertmsg_textview_text": "<PERSON><PERSON> du ikke opdaterer, fungerer enheden ikke korrekt. Opdater nu.", "rn_51001_modify_tue_button_text": "TIR", "rn_51001_resetrobotpwd_successtip2start_textview_text": "Brug venligst", "rn_51001_detaillist_antitheft_textview_text": "<PERSON><PERSON><PERSON><PERSON> for tyverisikring", "rn_51001_statistics_totalchargenote_textview_text": "Den samlede opladningstid er den tid, plæneklipperen bruger i ladestationen. Bemærk, at opladningstiden ikke medregnes i det samlede antal driftstimer.", "rn_51001_statistics_energynote_textview_text": "Denne EGO-plæneklipper producerer mindre kuldioxid under drift i forhold til almindelige plæneklippere.", "rn_51001_panelhome_status50_textview_text": "Opbygger kort - Hovedområde", "rn_51001_override_24_textview_text": "24 timer", "rn_51001_statistics_batterydata_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_resettipsend_textview_text": ". Indtast koden nedenfor for at bekræfte din e-mailadresse.", "rn_51001_resetrobotpwd_unregistertip_button_text": "Sorry! \nOm veiligheidsredenen moet uw robot eerst geregistreerd zijn en\nreset het wachtwoord dan hier. \nOf u kunt contact opnemen met uw dealer voor hulp.", "rn_51001_statistics_chargingcyclenote_textview_text": "Op<PERSON><PERSON><PERSON> viser, hvor mange gange batteriet er blevet helt opladet og afladet.", "rn_51001_headlight_off_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_headlight_green_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_sucesstitle_textview_text": "Het wachtwoord van uw robot is succesvol gereset. Gebruik \"0000\" om uw robot te starten. Als uw probleem zich nog steeds voordoet, kunt u een dealer vinden.", "rn_51001_resetrobotpwd_failtip3_textview_text": "forbin<PERSON><PERSON> til din plæneklipper, enten via Bluetooth", "rn_51001_backhomecorridor_note_textview_text": "Opmerking: Di<PERSON> is de breedte tussen de robot en de begrenzingsdraad wanneer de robot terugkeert. Maak de breedte zo breed mogelijk om lijnen in het gazon te beperken.", "rn_51001_panelhome_status44_textview_text": "Opladning færdig - Venter på start", "rn_51001_resetrobotpwd_failtip_textview_text": "Sorry, het resetten van het wachtwoord is mislukt. $$Zorg ervoor dat u een actieve$$ verbinding hebt met uw maaier, via Bluetooth$$ of via het mobiele netwerk.", "rn_51001_alarmsetting_duration_textview_text": "<PERSON><PERSON><PERSON> varighed (/min)", "rn_51001_override_note_textview_text": "<PERSON><PERSON> klipper plænen uafbrudt i henhold til din indstilling.", "rn_51001_statistics_batteryhealth_textview_text": "Batteri-helbred", "rn_51001_dashboard_exit_textview_text": "A<PERSON>lut", "rn_51001_timerange_end_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_otherslist_headlight_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_modify_all_textview_text": "<PERSON><PERSON><PERSON><PERSON> alle", "rn_51001_resetrobotpwd_finddealer_button_text": "Find forhandler", "rn_51001_panelhome_status41_textview_text": "Opladning færdig - Klippeplan ikke indstillet", "rn_51001_resetrobotpwd_fail_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_cutmode_workarea_textview_text": "Arbejdsområde", "rn_51001_override_72_textview_text": "72 timer", "rn_51001_resetrobotpwd_failtip4_textview_text": "eller mobilnetværket.", "rn_51001_panelhome_updatemapfailnote4_textview_text": "Sorry, de huidige robotstatus maakt het niet mogelijk om de kaart te resetten. Herstel e<PERSON>t de ma<PERSON>er.", "rn_51001_cutmode_econote_textview_text": "Bemærk: <PERSON><PERSON> robotten registrerer, at plænen er blevet klippet, kø<PERSON> den hurtigt over gr<PERSON><PERSON><PERSON>, indtil den når et gr<PERSON><PERSON>mrå<PERSON>, de ikke er klippet!", "rn_51001_resetrobotpwd_success_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_panelhome_mainarea_textview_text": "Hovedområde", "rn_51001_panelhome_status11_textview_text": "Pause - Stop", "rn_51001_modify_saturday_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_backhomecorridor_width_textview_text": "Bredde på gang (/mm)", "rn_51001_panelhome_pickerlefttitle_textview_text": "dato", "rn_51001_override_done_button_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_cutmode_workareasecond_textview_text": "<PERSON><PERSON>", "rn_51001_modify_delete_button_text": "Slet", "rn_51001_alarmsetting_switch_textview_text": "Alarmindstilling", "rn_51001_modify_fri_button_text": "FRE", "rn_51001_statistics_totaltravelnote_textview_text": "Den samlede køreafstand er den afstand, som plæneklipperen har tilbagelagt på alle funktionerne.", "rn_51001_resetrobotpwd_problemsolved_button_text": "<PERSON> løst", "rn_51001_smartexitdockingstation_title_textview_text": "K<PERSON><PERSON> ud af dockingstationen", "rn_51001_panelhome_status25_textview_text": "<PERSON><PERSON><PERSON> hjem - <PERSON><PERSON> for tidsplanen", "rn_51001_detaillist_others_textview_text": "<PERSON>", "rn_51001_panelhome_mapsettings_textview_text": "<PERSON><PERSON><PERSON>in<PERSON><PERSON><PERSON>", "rn_51001_panelhome_updatetitle_textview_text": "En ny opdatering er klar", "rn_51001_panelhome_status14_textview_text": "Pause - Justering af klip<PERSON>højde", "rn_51001_detaillist_functionsettings_textview_text": "Funktionsindstillinger", "rn_51001_panelhome_status27_textview_text": "<PERSON><PERSON>rer hjem - Genopbygning af kort", "rn_51001_backdistance_titleleft_textview_text": "Afstand til hjemkørsel", "rn_51001_panelhome_parktipmax_textview_text": "Indstillingstiden må ikke være over 99 timer", "rn_51001_panelhome_updatemapfailnote7_textview_text": "Sorry, de huidige robotstatus maakt het niet mogelijk om de kaart te resetten. Herstel e<PERSON>t de ma<PERSON>er.", "rn_51001_dashboard_modalmessage_textview_text": "Tryk på begge knapper på samme tid, for at starte", "rn_51001_guardagainsttheft_alarmsetting_textview_text": "Alarmindstilling", "rn_51001_panelhome_devicedidreceivecmd_textview_text": "Enheden modtog kommandoen om nulstilling af adgangskoden", "rn_51001_detaillist_bordermanagement_textview_text": "Grænsestyring", "rn_51001_panelhome_pickerrighttitle_textview_text": "minut", "rn_51001_panelhome_updatemapfailnote6_textview_text": "Sorry, de huidige robotstatus maakt het niet mogelijk om de kaart te resetten. Herstel e<PERSON>t de ma<PERSON>er.", "rn_51001_cutmode_sys_textview_text": "Systematisk klipning", "rn_51001_panelhome_status26_textview_text": "<PERSON><PERSON><PERSON> hjem - Tilbagekaldt af ladestationen", "rn_51001_schedulemanagement_title_textview_text": "Styring af tidsplanen", "rn_51001_dashboard_alert2subtitle_textview_text": "Klipperadius (/m)", "rn_51001_panelhome_mowingpath_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_detaillist_availablesettings_textview_text": "Tilgængelige indstillinger", "rn_51001_panelhome_status13_textview_text": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "rn_51001_bordermanagement_title_textview_text": "Grænsestyring", "rn_51001_guardagainsttheft_resetrobotpwd_textview_text": "Nulstil robottens adgangskode", "rn_51001_guardagainsttheft_geofencesetting_textview_text": "Geofence-indstillinger", "rn_51001_panelhome_status12_textview_text": "Pause - Signalkonflikt", "rn_51001_statistics_chargingcycle_textview_text": "Opladninger", "rn_51001_resetrobotpwd_successtipend_textview_text": "for at starte din robot.$$Hvis problemet fortsætter, skal du kontakte en forhandler.", "rn_51001_panelhome_updatemapfailnote5_textview_text": "Sorry, de huidige robotstatus maakt het niet mogelijk om de kaart te resetten. Herstel e<PERSON>t de ma<PERSON>er.", "rn_51001_cutmode_edge_textview_text": "Kantklipning", "rn_51001_modify_period_textview_text": "Tidsperiode", "rn_51001_cutheight_title_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_weeklyoverview_title_textview_text": "Ugeoversigt", "rn_51001_panelhome_status61_textview_text": "Klipning - <PERSON><PERSON> o<PERSON>r<PERSON>", "rn_51001_resetrobotpwd_unregisteredtip2_textview_text": "<PERSON><PERSON> sikkerhedsmæssige årsager skal din robot først registreres og", "rn_51001_cutheight_mm_textview_text": "<PERSON><PERSON> (/mm)", "rn_51001_statistics_totalco2savings_textview_text": "Samlede CO2-besparelser", "rn_51001_panelhome_status24_textview_text": "K<PERSON>rer hjem - Konflikt med kantkablets signal", "rn_51001_schedule_override_textview_text": "Ignorer tidsplan", "rn_51001_cutmode_spiralsensitivity_textview_text": "Følsomhedsniveau", "rn_51001_cutheight_titleleft_textview_text": "<PERSON><PERSON>", "rn_51001_statistics_totalenergyconsumption_textview_text": "Samlet energiforbrug", "rn_51001_panelhome_satellitemap_textview_text": "Satellitkort", "rn_51001_otherslist_rainsensornote_textview_text": "Opmerking: Als er regen wordt gedetecteerd, keert de robot terug naar het laadstation en start opnieuw wanneer de regensensor droog is en de vertragingstijd voorbij is.", "rn_51001_modify_monday_textview_text": "Mandag", "rn_51001_detaillist_schedule_textview_text": "Styring af tidsplanen", "rn_51001_panelhome_done_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_pwdcheckedsuccess_textview_text": "bekræftelseskoden blev bekræftet. Send nulstillingskommando", "rn_51001_cutmode_workareamain_textview_text": "Hovedområde", "rn_51001_panelhome_status42_textview_text": "Opladning færdig - Udsættelse på grund af regn", "rn_51001_modify_noschedule_textview_text": "Ingen tidsplan", "rn_51001_geofencesetting_title_textview_text": "Geofence-indstilling", "rn_51001_modify_setperiod_textview_text": "Indstil tidsperiode", "rn_51001_panelhome_status10_textview_text": "Pause", "rn_51001_headlight_blue_textview_text": "Blå", "rn_51001_cutmode_spiralnote_textview_text": "<PERSON><PERSON> græsset er længere i et bestemt område, klipper robotten græsset i et spiralmønster.", "rn_51001_resetrobotpwd_title_textview_text": "Nulstil robottens adgangskode", "rn_51001_resetrobotpwd_successtipstart_textview_text": "Robottens adgangskode er blevet nulstillet. Brug venligst »XXXX« for at starte robotten.", "rn_51001_panelhome_geofencesetting_textview_text": "Geofence-indstilling", "rn_51001_panelhome_status43_textview_text": "Opladning færdig - Parkerer", "rn_51001_panelhome_parkuntil_textview_text": "Parker indtil", "rn_51001_resetrobotpwd_resetagain_button_text": "Nulstil igen", "rn_51001_override_0_textview_text": "0 timer", "rn_51001_panelhome_parktipmin_textview_text": "Indstillingstiden skal være efter det aktuelle klokkeslæt.", "rn_51001_dashboard_alerttitle_textview_text": "Tip", "rn_51001_modify_thu_button_text": "TOR", "rn_51001_statistics_consumptionunit_textview_text": "A/H", "rn_51001_otherslist_rainsensor_textview_text": "Indstilling af regnsensoren", "rn_51001_cutmode_eco_textview_text": "Energibesparende klipning", "rn_51001_resetrobotpwd_unregisteredtip_textview_text": "Sorry! Om veiligheidsredenen moet uw robot eerst geregistreerd zijn en reset het wachtwoord dan hier. Of u kunt contact opnemen met uw dealer voor hulp.", "rn_51001_statistics_totalchargetime_textview_text": "Samlet opladningstid", "rn_51001_drivepastwire_cm_textview_text": "<PERSON><PERSON><PERSON> forbi kablet (/cm)", "rn_51001_panelhome_status51_textview_text": "Opbygger kort - <PERSON>et område", "rn_51001_cutmode_title_textview_text": "Klippefunktion", "rn_51001_panelhome_status23_textview_text": "<PERSON><PERSON><PERSON> <PERSON>", "rn_51001_modify_copyto_button_text": "<PERSON><PERSON><PERSON> <PERSON>", "rn_51001_detaillist_cutmode_textview_text": "Klippefunktion", "rn_51001_modify_tuesday_textview_text": "Tirsdag", "rn_51001_dashboard_alert2title_textview_text": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "rn_51001_guardagainsttheft_resetpwdfail_textview_text": "robottens adgangskode kunne ikke nulstilles. Send venligst igen.", "rn_51001_map_renametitle_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON> kort", "rn_51001_schedule_weeklyoverview_textview_text": "Ugeoversigt", "rn_51001_panelhome_cancel_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_modify_thursday_textview_text": "Torsdag", "rn_51001_alarmsetting_durationleft_textview_text": "<PERSON><PERSON><PERSON> varighed", "rn_51001_guardagainsttheft_title_textview_text": "Beskyttelse mod tyverisikring", "rn_51001_dashboard_selectedareacuttingunit_textview_text": "m", "rn_51001_cutheight_done_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_panelhome_park_textview_text": "<PERSON>", "rn_51001_resetrobotpwd_successtip3_textview_text": "<PERSON><PERSON> problemet fortsætter, skal du finde en forhandler.", "rn_51001_panelhome_status20_textview_text": "<PERSON><PERSON><PERSON> hjem - Klipning afsluttet", "rn_51001_headlight_color_textview_text": "Lysfarve", "rn_51001_resetrobotpwd_successtip2end_textview_text": "for at starte din robot.", "rn_51001_cutmode_selectedarea_textview_text": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "rn_51001_geofencesensitivity_setting_textview_text": "Indstilling af følsomheden", "rn_51001_detaillist_remotecontrol_textview_text": "Fjernbetjening", "rn_51001_timerange_start_textview_text": "Start", "rn_51001_resetrobotpwd_pwdnotcorrect_textview_text": "Bekræftelseskoden er forkert", "rn_51001_guardagainsttheft_titlenew_textview_text": "<PERSON><PERSON><PERSON><PERSON> for tyverisikring", "rn_51001_cutmode_cuttingradius_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_panelhome_schedule_button_text": "Tidsplan", "rn_51001_dashboard_exceededrangewaning_textview_text": "Du er over fjernbetjeningens rækkevidde.", "rn_51001_backdistance_cm_textview_text": "Afstand til hjemkørsel (/cm)", "rn_51001_headlight_red_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_failtip1_textview_text": "<PERSON><PERSON><PERSON>, adgangskoden kunne ikke nulstilles.", "rn_51001_dashboard_modaltitle_textview_text": "Start klippemotoren", "rn_51001_headlight_schedule_textview_text": "Lys-tidsplan", "rn_51001_resetrobotpwd_unregisteredtip3_textview_text": "<PERSON><PERSON><PERSON><PERSON> der<PERSON><PERSON> adgangskoden her.", "rn_51001_statistics_totalstatistics_textview_text": "Statistik over alle oplysningerne", "rn_51001_headlight_on_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_modify_addtime_textview_text": "Tidsperiode", "rn_51001_cutmode_selectedarearadius_textview_text": "Klipperadius (/m)", "rn_51001_statistics_title_textview_text": "Statistik", "rn_51001_drivepastwire_titleleft_textview_text": "<PERSON><PERSON><PERSON> forbi kablet", "rn_51001_panelhome_status46_textview_text": "Opladning færdig - Fejl ikke rettet", "rn_51001_dashboard_alertmessage_textview_text": "Vil du afslutte denne opgave?", "rn_51001_alarmsetting_alarmenable_textview_text": "Alarm, når enheden løftes", "rn_51001_modify_wed_button_text": "ONS", "rn_51001_modify_mon_button_text": "MAN", "rn_51001_backhomecorridor_title_textview_text": "Gang til hjemkørsel", "rn_51001_drivepastwire_title_textview_text": "<PERSON><PERSON><PERSON> forbi kablet", "rn_51001_modify_friday_textview_text": "Fred<PERSON>", "rn_51001_cutmode_workareanote_textview_text": "Bemærk: Robotten klipper automatisk plænen i henhold til tidsplanen og dine personlige indstillinger.", "rn_51001_panelhome_updatemap_textview_text": "Nulstil kort", "rn_51001_rainsensor_delay_textview_text": "Udsæ<PERSON>lsestid (/min)", "rn_51001_cutmode_edgenote_textview_text": "Bemærk: I hver nye tidsplan begynder robotten at klippe græsset langs kantkablet.", "rn_51001_cutmode_selectedareanote_textview_text": "<PERSON><PERSON><PERSON> udvalgt klippeområder slå<PERSON> til, klipper robotten det udvalgte området inden for en bestemt radius.", "rn_51001_headlight_period_textview_text": "Indstil tidsperiode", "rn_51001_dashboard_stopalertmsg_textview_text": "Enheden kører. Vil du stoppe den?", "rn_51001_resetrobotpwd_resettipstart_textview_text": "Vi har sendt en 6-cifret kode til e-mailadresse, du brugte til at registrere robotten med", "rn_51001_alarmsetting_title_textview_text": "Alarmindstilling", "rn_51001_panelhome_secondarea_textview_text": "<PERSON><PERSON>", "rn_51001_panelhome_pickercentertitle_textview_text": "time", "rn_51001_cutmode_sysnote_textview_text": "<PERSON><PERSON> begynder at klippe i lige systematiske linjer.", "rn_51001_panelhome_status60_textview_text": "Klipning - Hovedområdet", "rn_51001_modify_sat_button_text": "LØR", "rn_51001_headlight_flashes_textview_text": "<PERSON><PERSON><PERSON>, når en fejladvarsel opstår", "rn_51001_modify_done_button_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_unregisteredtip1_textview_text": "Beklager!", "rn_51001_geofencesensitivity_low_textview_text": "Lav"}, "it": {"rn_51001_smartexitdockingstation_note_textview_text": "Nota: il robot seguirà il cavo per la distanza impostata, quindi si allontanerà per tagliare l'erba.", "rn_51001_modify_validate_textview_text": "L'ora di fine deve essere successiva all'ora di inizio.", "rn_51001_panelhome_mailsemptyerror_textview_text": "E-mail vuota, verifica non riuscita", "rn_51001_geofencesensitivity_medium_textview_text": "Media", "rn_51001_modify_conflict_textview_text": "In conflitto con l'intervallo corrente. Reimpostalo.", "rn_51001_panelhome_status40_textview_text": "Ricarica completata - In attesa del prossimo programma di taglio", "rn_51001_resetrobotpwd_toregister_button_text": "Registra", "rn_51001_statistics_totalworkinghours_textview_text": "Ore di funzionamento totali", "rn_51001_panelhome_updatemapfailnote_textview_text": "<PERSON><PERSON><PERSON><PERSON>, le statut actuel du robot ne permet pas de réinitialiser la carte, veuillez d’abord récupérer la tondeuse.", "rn_51001_resetrobotpwd_done_button_text": "<PERSON><PERSON>", "rn_51001_batterydetail_title_textview_text": "Informazioni sulla batteria", "rn_51001_panelhome_status45_textview_text": "Ricarica completata - Coperchio a<PERSON>o", "rn_51001_detaillist_cutheight_textview_text": "Altezza di taglio", "rn_51001_geofencesensitivity_high_textview_text": "Alta", "rn_51001_backhomecorridor_widthleft_textview_text": "Ampiezza del corridoio", "rn_51001_resetrobotpwd_unregisteredtip4_textview_text": "Contatta il rivenditore per ricevere assistenza.", "rn_51001_modify_addtime_button_text": "Aggiungi orario", "rn_51001_statistics_energysavingdata_textview_text": "Dati sul risparmio energetico", "rn_51001_statistics_totaldistance_textview_text": "Distanza percorsa totale", "rn_51001_modify_sunday_textview_text": "Domenica", "rn_51001_panelhome_status21_textview_text": "Ritorno - Batteria scarica", "rn_51001_panelhome_interfaceerror_textview_text": "Interfaccia non visualizzata, errore segnalato", "rn_51001_modal_slideronOff_textview_text": "Acceso/spento", "rn_51001_cutmode_spiral_textview_text": "Taglio a spirale", "rn_51001_override_48_textview_text": "48 ore", "rn_51001_panelhome_parktiphour_textview_text": "L'ora deve essere successiva all'ora corrente di almeno un'ora.", "rn_51001_panelhome_updatemapmsg_textview_text": "Vous réinitialisez une carte. La nouvelle carte remplacera la carte actuelle. Voulez-vous continuer ?", "rn_51001_headlight_white_textview_text": "<PERSON>", "rn_51001_panelhome_status30_textview_text": "Sotto carica", "rn_51001_panelhome_status22_textview_text": "Ritorno - Pioggia", "rn_51001_resetrobotpwd_successtip1_textview_text": "La password del robot è stata ripristinata correttamente.$$", "rn_51001_resetrobotpwd_pwdnotcompleted_textview_text": "Inserisci il codice di verifica", "rn_51001_resetrobotpwd_failtip2_textview_text": "Assicurati che il tagliaerba sia", "rn_51001_drivepastwire_note_textview_text": "Remarque : Il s’agit de la distance à laquelle l’avant du robot peut se déplacer au-delà du câble périphérique.", "rn_51001_cutheight_inch_textview_text": "Regola altezza di taglio (/pollici)", "rn_51001_modify_sun_button_text": "DOM", "rn_51001_modify_wednesday_textview_text": "Mercoledì", "rn_51001_panelhome_otaalertmsg_textview_text": "Se l'aggiornamento non viene effettuato, il dispositivo non funzionerà correttamente. Effettua ora l'aggiornamento.", "rn_51001_modify_tue_button_text": "MAR", "rn_51001_resetrobotpwd_successtip2start_textview_text": "<PERSON><PERSON>", "rn_51001_detaillist_antitheft_textview_text": "Impostazioni antifurto", "rn_51001_statistics_totalchargenote_textview_text": "Il tempo di ricarica totale è il tempo in cui il tagliaerba è rimasto collegato alla stazione di ricarica. Nota: il tempo di ricarica totale non è incluso nelle ore di funzionamento totali.", "rn_51001_statistics_energynote_textview_text": "Rispetto ad altri prodotti, il tagliaerba EGO produce meno anidride carbonica quando è in funzione.", "rn_51001_panelhome_status50_textview_text": "Costruzione mappa - Area principale", "rn_51001_override_24_textview_text": "24 ore", "rn_51001_statistics_batterydata_textview_text": "<PERSON><PERSON>ia", "rn_51001_resetrobotpwd_resettipsend_textview_text": ". Inser<PERSON>ci il seguente codice per verificare il tuo indirizzo e-mail.", "rn_51001_resetrobotpwd_unregistertip_button_text": "Désolé !\nPour des raisons de sécurité, votre robot doit d'abord être enregistré, puis le mot de passe doit être réinitialisé ici. Ou vous pouvez demander de l'aide à votre revendeur.", "rn_51001_statistics_chargingcyclenote_textview_text": "Il ciclo di ricarica indica il numero di volte in cui la batteria è stata completamente caricata e scaricata.", "rn_51001_headlight_off_textview_text": "Semp<PERSON> spenta", "rn_51001_headlight_green_textview_text": "Verde", "rn_51001_resetrobotpwd_sucesstitle_textview_text": "Le mot de passe de votre robot a été réinitialisé avec succès. Veuillez utiliser « 0000 » pour démarrer votre robot. Si votre problème persiste, vous pouvez trouver un revendeur.", "rn_51001_resetrobotpwd_failtip3_textview_text": "collegato tramite Bluetooth", "rn_51001_backhomecorridor_note_textview_text": "Note : Il s’agit de la largeur entre le robot et le câble périphérique lorsque le robot revient. Faites en sorte que la largeur soit aussi large que possible pour réduire les lignes dans la pelouse.", "rn_51001_panelhome_status44_textview_text": "Ricarica completata - In attesa di avviarsi", "rn_51001_resetrobotpwd_failtip_textview_text": "Désolé, la réinitialisation du mot de passe a échoué. Veuillez vous assurer que vous disposez d'une connexion active avec votre tondeuse, soit par Bluetooth, soit par le réseau mobile.", "rn_51001_alarmsetting_duration_textview_text": "Durata dell'allarme (/min)", "rn_51001_override_note_textview_text": "Il robot taglierà il prato ininterrottamente in base alle impostazioni.", "rn_51001_statistics_batteryhealth_textview_text": "Stato della batteria", "rn_51001_dashboard_exit_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_timerange_end_textview_text": "Fine", "rn_51001_otherslist_headlight_textview_text": "Faro", "rn_51001_modify_all_textview_text": "Se<PERSON><PERSON>na tutto", "rn_51001_resetrobotpwd_finddealer_button_text": "Trova un distributore", "rn_51001_panelhome_status41_textview_text": "Ricarica completata - Programma di taglio non impostato", "rn_51001_resetrobotpwd_fail_textview_text": "Non riuscito", "rn_51001_cutmode_workarea_textview_text": "Area di lavoro", "rn_51001_override_72_textview_text": "72 ore", "rn_51001_resetrobotpwd_failtip4_textview_text": "o una rete mobile.", "rn_51001_panelhome_updatemapfailnote4_textview_text": "<PERSON><PERSON><PERSON><PERSON>, le statut actuel du robot ne permet pas de réinitialiser la carte, veuillez d’abord récupérer la tondeuse.", "rn_51001_cutmode_econote_textview_text": "Nota: se il robot rileva che un'area è già stata tagliata, la supererà fino a raggiungere l'erba non tagliata.", "rn_51001_resetrobotpwd_success_textview_text": "Effettuato correttamente", "rn_51001_panelhome_mainarea_textview_text": "Area principale", "rn_51001_panelhome_status11_textview_text": "Pausa - <PERSON><PERSON><PERSON>", "rn_51001_modify_saturday_textview_text": "Sabato", "rn_51001_backhomecorridor_width_textview_text": "Ampiezza del corridoio (/mm)", "rn_51001_panelhome_pickerlefttitle_textview_text": "data", "rn_51001_override_done_button_text": "<PERSON><PERSON>", "rn_51001_cutmode_workareasecond_textview_text": "Area secondaria", "rn_51001_modify_delete_button_text": "Elimina", "rn_51001_alarmsetting_switch_textview_text": "Impostazioni allarme", "rn_51001_modify_fri_button_text": "VEN", "rn_51001_statistics_totaltravelnote_textview_text": "La distanza percorsa totale è la distanza percorsa dal tagliaerba in tutte le modalità.", "rn_51001_resetrobotpwd_problemsolved_button_text": "<PERSON><PERSON> risolto", "rn_51001_smartexitdockingstation_title_textview_text": "Esci dalla stazione di ricarica", "rn_51001_panelhome_status25_textview_text": "Ritorno - Fuori programma", "rn_51001_detaillist_others_textview_text": "Altro", "rn_51001_panelhome_mapsettings_textview_text": "Impostazioni mappa", "rn_51001_panelhome_updatetitle_textview_text": "Nuovo aggiornamento disponibile", "rn_51001_panelhome_status14_textview_text": "Pausa - Regolazione dell'altezza di taglio", "rn_51001_detaillist_functionsettings_textview_text": "Impostazioni funzioni", "rn_51001_panelhome_status27_textview_text": "Ritorno - Ricostruzione mappa", "rn_51001_backdistance_titleleft_textview_text": "Distanza di ritorno", "rn_51001_panelhome_parktipmax_textview_text": "Il tempo non può superare le 99 ore.", "rn_51001_panelhome_updatemapfailnote7_textview_text": "<PERSON><PERSON><PERSON><PERSON>, le statut actuel du robot ne permet pas de réinitialiser la carte, veuillez d’abord récupérer la tondeuse.", "rn_51001_dashboard_modalmessage_textview_text": "Premi entrambi i pulsanti simultaneamente per iniziare.", "rn_51001_guardagainsttheft_alarmsetting_textview_text": "Impostazioni allarme", "rn_51001_panelhome_devicedidreceivecmd_textview_text": "Il dispositivo ha ricevuto correttamente il comando di ripristino della password", "rn_51001_detaillist_bordermanagement_textview_text": "Gestione del bordo", "rn_51001_panelhome_pickerrighttitle_textview_text": "minuto", "rn_51001_panelhome_updatemapfailnote6_textview_text": "<PERSON><PERSON><PERSON><PERSON>, le statut actuel du robot ne permet pas de réinitialiser la carte, veuillez d’abord récupérer la tondeuse.", "rn_51001_cutmode_sys_textview_text": "<PERSON><PERSON>", "rn_51001_panelhome_status26_textview_text": "Ritorno - Chiamato dalla stazione di ricarica", "rn_51001_schedulemanagement_title_textview_text": "Gestione della programmazione", "rn_51001_dashboard_alert2subtitle_textview_text": "Raggio di taglio (/m)", "rn_51001_panelhome_mowingpath_textview_text": "Percorso di taglio", "rn_51001_detaillist_availablesettings_textview_text": "Impostazioni disponibili", "rn_51001_panelhome_status13_textview_text": "Pausa - Guasto", "rn_51001_bordermanagement_title_textview_text": "Gestione del bordo", "rn_51001_guardagainsttheft_resetrobotpwd_textview_text": "<PERSON><PERSON><PERSON><PERSON> la <PERSON> del robot", "rn_51001_guardagainsttheft_geofencesetting_textview_text": "Impostazioni Geofence", "rn_51001_panelhome_status12_textview_text": "Pausa - <PERSON>f<PERSON><PERSON> di segnale", "rn_51001_statistics_chargingcycle_textview_text": "Ciclo di ricarica", "rn_51001_resetrobotpwd_successtipend_textview_text": "per avviare il robot.$$Se il problema persiste, contatta un rivenditore.", "rn_51001_panelhome_updatemapfailnote5_textview_text": "<PERSON><PERSON><PERSON><PERSON>, le statut actuel du robot ne permet pas de réinitialiser la carte, veuillez d’abord récupérer la tondeuse.", "rn_51001_cutmode_edge_textview_text": "Taglio di bordatura", "rn_51001_modify_period_textview_text": "Intervallo temporale", "rn_51001_cutheight_title_textview_text": "Altezza di taglio", "rn_51001_weeklyoverview_title_textview_text": "Panoramica settimanale", "rn_51001_panelhome_status61_textview_text": "Taglio - Area secondaria", "rn_51001_resetrobotpwd_unregisteredtip2_textview_text": "Per motivi di sicurezza, il robot deve essere registrato per", "rn_51001_cutheight_mm_textview_text": "Regola altezza di taglio (/mm)", "rn_51001_statistics_totalco2savings_textview_text": "Risparmio di CO2 totale", "rn_51001_panelhome_status24_textview_text": "Ritorno - Conflitto di segnale del perimetro", "rn_51001_schedule_override_textview_text": "Ignora programma", "rn_51001_cutmode_spiralsensitivity_textview_text": "Livello di sensibilità", "rn_51001_cutheight_titleleft_textview_text": "Regola altezza di taglio", "rn_51001_statistics_totalenergyconsumption_textview_text": "Consumo energetico totale", "rn_51001_panelhome_satellitemap_textview_text": "<PERSON><PERSON> satellitare", "rn_51001_otherslist_rainsensornote_textview_text": "Remarque : Lorsqu'une pluie est détectée, le robot retourne à la station de charge et redémarre jusqu'à ce que le capteur de pluie soit sec et que le délai soit écoulé.", "rn_51001_modify_monday_textview_text": "Lunedì", "rn_51001_detaillist_schedule_textview_text": "Gestione della programmazione", "rn_51001_panelhome_done_textview_text": "<PERSON><PERSON>", "rn_51001_resetrobotpwd_pwdcheckedsuccess_textview_text": "Codice di verifica verificato correttamente. Invia il comando di ripristino.", "rn_51001_cutmode_workareamain_textview_text": "Area principale", "rn_51001_panelhome_status42_textview_text": "Ricarica completata - <PERSON><PERSON>", "rn_51001_modify_noschedule_textview_text": "Nessuna programmazione", "rn_51001_geofencesetting_title_textview_text": "Impostazioni Geofence", "rn_51001_modify_setperiod_textview_text": "Imposta intervallo temporale", "rn_51001_panelhome_status10_textview_text": "Pausa", "rn_51001_headlight_blue_textview_text": "Blu", "rn_51001_cutmode_spiralnote_textview_text": "Il robot taglierà l'erba effettuando un percorso a spirale se l'erba è più alta in una certa area.", "rn_51001_resetrobotpwd_title_textview_text": "<PERSON><PERSON><PERSON><PERSON> la <PERSON> del robot", "rn_51001_resetrobotpwd_successtipstart_textview_text": "La password del robot è stata ripristinata correttamente.$$Usa", "rn_51001_panelhome_geofencesetting_textview_text": "Impostazioni Geofence", "rn_51001_panelhome_status43_textview_text": "Ricarica completata - Parcheggio", "rn_51001_panelhome_parkuntil_textview_text": "Parcheggia fino a", "rn_51001_resetrobotpwd_resetagain_button_text": "Ripristina nuovamente", "rn_51001_override_0_textview_text": "0 ore", "rn_51001_panelhome_parktipmin_textview_text": "L'ora di inizio deve essere successiva all'ora corrente.", "rn_51001_dashboard_alerttitle_textview_text": "Suggerimento", "rn_51001_modify_thu_button_text": "GIO", "rn_51001_statistics_consumptionunit_textview_text": "A/H", "rn_51001_otherslist_rainsensor_textview_text": "Impostazioni sensore pioggia", "rn_51001_cutmode_eco_textview_text": "Taglio ECO", "rn_51001_resetrobotpwd_unregisteredtip_textview_text": "Désolé ! Pour des raisons de sécurité, votre robot doit d'abord être enregistré, puis le mot de passe doit être réinitialisé ici. Ou vous pouvez demander de l'aide à votre revendeur.", "rn_51001_statistics_totalchargetime_textview_text": "Tempo di ricarica totale", "rn_51001_drivepastwire_cm_textview_text": "Distanza oltre il cavo (/cm)", "rn_51001_panelhome_status51_textview_text": "Costruzione mappa - Area secondaria", "rn_51001_cutmode_title_textview_text": "Modalità di taglio", "rn_51001_panelhome_status23_textview_text": "Ritorno - Parcheggio", "rn_51001_modify_copyto_button_text": "Copia su", "rn_51001_detaillist_cutmode_textview_text": "Modalità di taglio", "rn_51001_modify_tuesday_textview_text": "Martedì", "rn_51001_dashboard_alert2title_textview_text": "Taglio dell'area selezionata", "rn_51001_guardagainsttheft_resetpwdfail_textview_text": "Il ripristino della password del robot non è riuscito. Invia nuovamente.", "rn_51001_map_renametitle_textview_text": "Rinomina mappa", "rn_51001_schedule_weeklyoverview_textview_text": "Panoramica settimanale", "rn_51001_panelhome_cancel_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_modify_thursday_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_alarmsetting_durationleft_textview_text": "Durata dell'allarme", "rn_51001_guardagainsttheft_title_textview_text": "Protezione antifurto", "rn_51001_dashboard_selectedareacuttingunit_textview_text": "m", "rn_51001_cutheight_done_textview_text": "<PERSON><PERSON>", "rn_51001_panelhome_park_textview_text": "Parcheggio", "rn_51001_resetrobotpwd_successtip3_textview_text": "Se il problema persiste, contatta un rivenditore.", "rn_51001_panelhome_status20_textview_text": "Ritorno - Taglio terminato", "rn_51001_headlight_color_textview_text": "Colore della luce", "rn_51001_resetrobotpwd_successtip2end_textview_text": "per avviare il robot.", "rn_51001_cutmode_selectedarea_textview_text": "Taglio dell'area selezionata", "rn_51001_geofencesensitivity_setting_textview_text": "Impostazioni sensibilità", "rn_51001_detaillist_remotecontrol_textview_text": "<PERSON><PERSON>", "rn_51001_timerange_start_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_pwdnotcorrect_textview_text": "Il codice di verifica è errato.", "rn_51001_guardagainsttheft_titlenew_textview_text": "Impostazioni antifurto", "rn_51001_cutmode_cuttingradius_textview_text": "Raggio di taglio", "rn_51001_panelhome_schedule_button_text": "Programmazione", "rn_51001_dashboard_exceededrangewaning_textview_text": "Hai superato la portata di trasmissione per il controllo remoto.", "rn_51001_backdistance_cm_textview_text": "Distanza di ritorno (/cm)", "rn_51001_headlight_red_textview_text": "Ross<PERSON>", "rn_51001_resetrobotpwd_failtip1_textview_text": "Il ripristino della password non è riuscito.", "rn_51001_dashboard_modaltitle_textview_text": "Avvia motore di taglio", "rn_51001_headlight_schedule_textview_text": "<PERSON><PERSON> luce", "rn_51001_resetrobotpwd_unregisteredtip3_textview_text": "<PERSON><PERSON>di rip<PERSON>ina la <PERSON> qui.", "rn_51001_statistics_totalstatistics_textview_text": "Statistiche totali", "rn_51001_headlight_on_textview_text": "Sempre accesa", "rn_51001_modify_addtime_textview_text": "Intervallo temporale", "rn_51001_cutmode_selectedarearadius_textview_text": "Raggio di taglio (/m)", "rn_51001_statistics_title_textview_text": "Statistiche", "rn_51001_drivepastwire_titleleft_textview_text": "Distanza oltre il cavo", "rn_51001_panelhome_status46_textview_text": "Ricarica completata - Guasto non eliminato", "rn_51001_dashboard_alertmessage_textview_text": "Arrestare questa operazione?", "rn_51001_alarmsetting_alarmenable_textview_text": "Allarme quando sollevato", "rn_51001_modify_wed_button_text": "MER", "rn_51001_modify_mon_button_text": "LUN", "rn_51001_backhomecorridor_title_textview_text": "Corridorio di ritorno", "rn_51001_drivepastwire_title_textview_text": "Distanza oltre il cavo", "rn_51001_modify_friday_textview_text": "<PERSON>ener<PERSON><PERSON>", "rn_51001_cutmode_workareanote_textview_text": "Nota: il robot taglierà automaticamente il prato in base alla programmazione e alle impostazioni personalizzate.", "rn_51001_panelhome_updatemap_textview_text": "<PERSON><PERSON><PERSON><PERSON> mappa", "rn_51001_rainsensor_delay_textview_text": "Tempo di ritardo (/min)", "rn_51001_cutmode_edgenote_textview_text": "Nota: il robot inizierà il taglio lungo il cavo perimetrale del prato a ogni nuova programmazione.", "rn_51001_cutmode_selectedareanote_textview_text": "Quando la funzione Taglio dell'area selezionata è attiva, il robot taglierà l'area intorno al punto corrente entro un certo raggio.", "rn_51001_headlight_period_textview_text": "Imposta intervallo temporale", "rn_51001_dashboard_stopalertmsg_textview_text": "Il dispositivo è in funzione. Arrestarlo?", "rn_51001_resetrobotpwd_resettipstart_textview_text": "Abbiamo inviato un codice di 6 cifre al tuo indirizzo e-mail registrato.", "rn_51001_alarmsetting_title_textview_text": "Impostazioni allarme", "rn_51001_panelhome_secondarea_textview_text": "Area secondaria", "rn_51001_panelhome_pickercentertitle_textview_text": "ora", "rn_51001_cutmode_sysnote_textview_text": "Il robot inizierà il taglio sistematicamente in linee rette.", "rn_51001_panelhome_status60_textview_text": "Taglio - Area principale", "rn_51001_modify_sat_button_text": "SAB", "rn_51001_headlight_flashes_textview_text": "Lampeggia in caso di malfunzionamento", "rn_51001_modify_done_button_text": "<PERSON><PERSON>", "rn_51001_resetrobotpwd_unregisteredtip1_textview_text": "Spiacenti!", "rn_51001_geofencesensitivity_low_textview_text": "Bass<PERSON>"}, "fr": {"rn_51001_smartexitdockingstation_note_textview_text": "Remarque : Le robot recule le long du câble jusqu'à la distance saisie, puis se met à tondre !", "rn_51001_modify_validate_textview_text": "L'heure de fin doit être postérieure à l'heure de début", "rn_51001_panelhome_mailsemptyerror_textview_text": "L’e-mail est vide, la vérification a échoué", "rn_51001_geofencesensitivity_medium_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_modify_conflict_textview_text": "Conflit avec la période en cours ; veuillez réinitialiser", "rn_51001_panelhome_status40_textview_text": "Charge terminée - En attente du prochain programme de tonte", "rn_51001_resetrobotpwd_toregister_button_text": "Enregistrer", "rn_51001_statistics_totalworkinghours_textview_text": "Nombre total d'heures de fonctionnement", "rn_51001_panelhome_updatemapfailnote_textview_text": "<PERSON> sentimo<PERSON>, el estado actual del robot no permite restablecer el mapa, primero recupere el cortacésped.", "rn_51001_resetrobotpwd_done_button_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_batterydetail_title_textview_text": "Informations sur la batterie", "rn_51001_panelhome_status45_textview_text": "Charge terminée - Couvercle ouvert", "rn_51001_detaillist_cutheight_textview_text": "Hauteur de coupe", "rn_51001_geofencesensitivity_high_textview_text": "<PERSON><PERSON>", "rn_51001_backhomecorridor_widthleft_textview_text": "Largeur du corridor", "rn_51001_resetrobotpwd_unregisteredtip4_textview_text": "Ou vous pouvez demander de l'aide à votre revendeur.", "rn_51001_modify_addtime_button_text": "Ajouter une durée", "rn_51001_statistics_energysavingdata_textview_text": "Données d’économie d'énergie", "rn_51001_statistics_totaldistance_textview_text": "Distance totale parcourue", "rn_51001_modify_sunday_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_panelhome_status21_textview_text": "Retour - <PERSON><PERSON><PERSON> faible", "rn_51001_panelhome_interfaceerror_textview_text": "L'interface n'a pas été renvoyée, une erreur a été signalée", "rn_51001_modal_slideronOff_textview_text": "Marche/arrêt", "rn_51001_cutmode_spiral_textview_text": "Coupe en spirale", "rn_51001_override_48_textview_text": "48h", "rn_51001_panelhome_parktiphour_textview_text": "L’heure réglée doit être postérieure d'une heure à l'heure en cours.", "rn_51001_panelhome_updatemapmsg_textview_text": "Está restableciendo un mapa. El nuevo mapa reemplazará al actual. ¿Quiere continuar?", "rn_51001_headlight_white_textview_text": "<PERSON>", "rn_51001_panelhome_status30_textview_text": "Charge en cours", "rn_51001_panelhome_status22_textview_text": "Retour - Pluie", "rn_51001_resetrobotpwd_successtip1_textview_text": "Le mot de passe de votre robot a été réinitialisé avec succès.", "rn_51001_resetrobotpwd_pwdnotcompleted_textview_text": "Veuillez saisir le code de vérification", "rn_51001_resetrobotpwd_failtip2_textview_text": "Veu<PERSON>z vous assurer que vous disposez d'une connexion active à votre tondeuse, via <PERSON><PERSON> ou le réseau.", "rn_51001_drivepastwire_note_textview_text": "Nota: Esta es la distancia a la que la parte delantera del robot puede moverse más allá del cable delimitador.", "rn_51001_cutheight_inch_textview_text": "Réglage de la hauteur de coupe (/pouce)", "rn_51001_modify_sun_button_text": "DIM", "rn_51001_modify_wednesday_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_panelhome_otaalertmsg_textview_text": "Si vous ne procédez pas à la mise à jour, l'appareil ne fonctionnera pas correctement. Veuillez le mettre à jour maintenant.", "rn_51001_modify_tue_button_text": "MAR", "rn_51001_resetrobotpwd_successtip2start_textview_text": "Veuillez utiliser", "rn_51001_detaillist_antitheft_textview_text": "Réglages antivol", "rn_51001_statistics_totalchargenote_textview_text": "La durée de charge totale fait référence au temps passé par la tondeuse à la station de charge. Veuillez noter que la durée de charge n'est pas incluse dans le nombre total d'heures de fonctionnement.", "rn_51001_statistics_energynote_textview_text": "Par rapport à d'autres produits, la tondeuse EGO produit au total moins de dioxyde de carbone lors de son fonctionnement.", "rn_51001_panelhome_status50_textview_text": "Cartographie - Zone principale", "rn_51001_override_24_textview_text": "24h", "rn_51001_statistics_batterydata_textview_text": "Données relatives à la batterie", "rn_51001_resetrobotpwd_resettipsend_textview_text": ". Veuillez saisir le code ci-dessous pour vérifier votre adresse e-mail.", "rn_51001_resetrobotpwd_unregistertip_button_text": "¡Lo sentimos!\nPor seguridad, su robot debe registrarse primero y\n después debe restablecer la contraseña aquí.\nO puede contratar a su distribuidor para que le ayude.", "rn_51001_statistics_chargingcyclenote_textview_text": "Le cycle de charge indique le nombre de fois où la batterie a été complètement chargée et déchargée.", "rn_51001_headlight_off_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_headlight_green_textview_text": "<PERSON>ert", "rn_51001_resetrobotpwd_sucesstitle_textview_text": "La contraseña de su robot se ha restablecido correctamente. Utilice «0000» para iniciar su robot. Si el problema persiste, puede buscar un distribuidor.", "rn_51001_resetrobotpwd_failtip3_textview_text": "connexion à votre tondeuse, soit à l’aide de Bluetooth", "rn_51001_backhomecorridor_note_textview_text": "Nota: Esta es la anchura entre el robot y el cable delimitador cuando el robot regresa. Haga que la anchura sea la máxima posible para reducir las líneas en el césped.", "rn_51001_panelhome_status44_textview_text": "Charge terminée - Attente pour le démarrage", "rn_51001_resetrobotpwd_failtip_textview_text": "<PERSON> sentimo<PERSON>, el restablecimiento de la contraseña ha fallado.$$Asegúrese de tener una conexión activa$$ a su cortacésped, ya sea mediante Bluetooth$$ o la red móvil.", "rn_51001_alarmsetting_duration_textview_text": "<PERSON><PERSON><PERSON> de l'alarme (/min)", "rn_51001_override_note_textview_text": "Le robot tondra la pelouse en continu en fonction de vos réglages.", "rn_51001_statistics_batteryhealth_textview_text": "État de la batterie", "rn_51001_dashboard_exit_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_timerange_end_textview_text": "Fin", "rn_51001_otherslist_headlight_textview_text": "Ph<PERSON>", "rn_51001_modify_all_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout", "rn_51001_resetrobotpwd_finddealer_button_text": "Trouver un revendeur", "rn_51001_panelhome_status41_textview_text": "Charge terminée - Programme de tonte non établi", "rn_51001_resetrobotpwd_fail_textview_text": "Échec", "rn_51001_cutmode_workarea_textview_text": "Zone de travail", "rn_51001_override_72_textview_text": "72h", "rn_51001_resetrobotpwd_failtip4_textview_text": "ou le réseau mobile.", "rn_51001_panelhome_updatemapfailnote4_textview_text": "<PERSON> sentimo<PERSON>, el estado actual del robot no permite restablecer el mapa, primero recupere el cortacésped.", "rn_51001_cutmode_econote_textview_text": "Remarque : Si le robot détecte que la pelouse a été tondue, il roulera rapidement dessus jusqu'à atteindre de l'herbe haute !", "rn_51001_resetrobotpwd_success_textview_text": "Su<PERSON>ès", "rn_51001_panelhome_mainarea_textview_text": "Zone principale", "rn_51001_panelhome_status11_textview_text": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "rn_51001_modify_saturday_textview_text": "<PERSON><PERSON>", "rn_51001_backhomecorridor_width_textview_text": "Largeur du couloir (/mm)", "rn_51001_panelhome_pickerlefttitle_textview_text": "date", "rn_51001_override_done_button_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_cutmode_workareasecond_textview_text": "Deuxième zone", "rn_51001_modify_delete_button_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_alarmsetting_switch_textview_text": "Réglage de l'alarme", "rn_51001_modify_fri_button_text": "VEN", "rn_51001_statistics_totaltravelnote_textview_text": "La distance totale de déplacement est la distance parcourue par la tondeuse dans tous les modes.", "rn_51001_resetrobotpwd_problemsolved_button_text": "Problème résolu", "rn_51001_smartexitdockingstation_title_textview_text": "Station de sortie", "rn_51001_panelhome_status25_textview_text": "Retour - Hors horaire", "rn_51001_detaillist_others_textview_text": "Autres", "rn_51001_panelhome_mapsettings_textview_text": "Paramètres de la carte", "rn_51001_panelhome_updatetitle_textview_text": "Nouvelle mise à jour disponible", "rn_51001_panelhome_status14_textview_text": "Pause - Réglage de la hauteur de coupe", "rn_51001_detaillist_functionsettings_textview_text": "Réglages des fonctions", "rn_51001_panelhome_status27_textview_text": "Retour - Reconstruction de la carte", "rn_51001_backdistance_titleleft_textview_text": "Distance arrière", "rn_51001_panelhome_parktipmax_textview_text": "La durée de réglage ne doit pas dépasser 99h", "rn_51001_panelhome_updatemapfailnote7_textview_text": "<PERSON> sentimo<PERSON>, el estado actual del robot no permite restablecer el mapa, primero recupere el cortacésped.", "rn_51001_dashboard_modalmessage_textview_text": "Appuyer simultanément sur les deux boutons pour démarrer", "rn_51001_guardagainsttheft_alarmsetting_textview_text": "Réglage de l'alarme", "rn_51001_panelhome_devicedidreceivecmd_textview_text": "L’appareil a reçu avec succès la commande de réinitialisation du mot de passe", "rn_51001_detaillist_bordermanagement_textview_text": "Gestion des délimitations", "rn_51001_panelhome_pickerrighttitle_textview_text": "minute", "rn_51001_panelhome_updatemapfailnote6_textview_text": "<PERSON> sentimo<PERSON>, el estado actual del robot no permite restablecer el mapa, primero recupere el cortacésped.", "rn_51001_cutmode_sys_textview_text": "Coupe systématique", "rn_51001_panelhome_status26_textview_text": "Retour - Appelé par la station de charge", "rn_51001_schedulemanagement_title_textview_text": "Gestion de la programmation", "rn_51001_dashboard_alert2subtitle_textview_text": "Rayon de coupe (/ m)", "rn_51001_panelhome_mowingpath_textview_text": "Parcours de tonte", "rn_51001_detaillist_availablesettings_textview_text": "Paramètres disponibles", "rn_51001_panelhome_status13_textview_text": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "rn_51001_bordermanagement_title_textview_text": "Gestion des délimitations", "rn_51001_guardagainsttheft_resetrobotpwd_textview_text": "Réinitialisez le mot de passe du robot", "rn_51001_guardagainsttheft_geofencesetting_textview_text": "Paramètres de délimitation du terrain", "rn_51001_panelhome_status12_textview_text": "Pause - Conflit de signal", "rn_51001_statistics_chargingcycle_textview_text": "Cycle de charge", "rn_51001_resetrobotpwd_successtipend_textview_text": "de démarrage de votre robot.$$Si votre problème persiste, vous consultez un revendeur.", "rn_51001_panelhome_updatemapfailnote5_textview_text": "<PERSON> sentimo<PERSON>, el estado actual del robot no permite restablecer el mapa, primero recupere el cortacésped.", "rn_51001_cutmode_edge_textview_text": "Coupe des bordures", "rn_51001_modify_period_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_cutheight_title_textview_text": "Hauteur de coupe", "rn_51001_weeklyoverview_title_textview_text": "Aperçu hebdomadaire", "rn_51001_panelhome_status61_textview_text": "Tonte - Zone secondaire", "rn_51001_resetrobotpwd_unregisteredtip2_textview_text": "Pour des raisons de sécurité, votre robot doit d’abord être enregistré puis", "rn_51001_cutheight_mm_textview_text": "Réglage de la hauteur de coupe (/mm)", "rn_51001_statistics_totalco2savings_textview_text": "Total des économies de CO2", "rn_51001_panelhome_status24_textview_text": "Retour - Conflit de signal frontalier", "rn_51001_schedule_override_textview_text": "Ignorer la programmation", "rn_51001_cutmode_spiralsensitivity_textview_text": "Niveau de sensibilité", "rn_51001_cutheight_titleleft_textview_text": "<PERSON><PERSON><PERSON> la hauteur de coupe", "rn_51001_statistics_totalenergyconsumption_textview_text": "Consommation d'énergie totale", "rn_51001_panelhome_satellitemap_textview_text": "Carte satellite", "rn_51001_otherslist_rainsensornote_textview_text": "Nota: <PERSON><PERSON><PERSON> se detecta lluvia, el robot vuelve a la estación de carga y se reinicia hasta que el sensor de lluvia esté seco y termine el tiempo de retardo.", "rn_51001_modify_monday_textview_text": "<PERSON><PERSON>", "rn_51001_detaillist_schedule_textview_text": "Gestion de la programmation", "rn_51001_panelhome_done_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_pwdcheckedsuccess_textview_text": "Vérification du code de vérification réussie, envoi de la commande de réinitialisation", "rn_51001_cutmode_workareamain_textview_text": "Zone principale", "rn_51001_panelhome_status42_textview_text": "Charge terminée - Retard dû à la pluie", "rn_51001_modify_noschedule_textview_text": "Aucune programmation", "rn_51001_geofencesetting_title_textview_text": "Paramétrage de la délimitation du terrain", "rn_51001_modify_setperiod_textview_text": "Définir la durée", "rn_51001_panelhome_status10_textview_text": "Pause", "rn_51001_headlight_blue_textview_text": "Bleu", "rn_51001_cutmode_spiralnote_textview_text": "Le robot coupera l'herbe en spirale si l'herbe est plus haute dans une zone donnée.", "rn_51001_resetrobotpwd_title_textview_text": "Réinitialisez le mot de passe du robot", "rn_51001_resetrobotpwd_successtipstart_textview_text": "Le mot de passe de votre robot a été réinitialisé avec succès. Veuillez utiliser « XXXX » pour démarrer votre robot.", "rn_51001_panelhome_geofencesetting_textview_text": "Paramétrage de la délimitation du terrain", "rn_51001_panelhome_status43_textview_text": "Charge terminée - Stationnement", "rn_51001_panelhome_parkuntil_textview_text": "Stationnement jusqu'à", "rn_51001_resetrobotpwd_resetagain_button_text": "Réinitialiser à nouveau", "rn_51001_override_0_textview_text": "0h", "rn_51001_panelhome_parktipmin_textview_text": "L'heure de réglage doit être postérieure à l'heure actuelle.", "rn_51001_dashboard_alerttitle_textview_text": "Conseil", "rn_51001_modify_thu_button_text": "JEU", "rn_51001_statistics_consumptionunit_textview_text": "A/H", "rn_51001_otherslist_rainsensor_textview_text": "Réglage du détecteur de pluie", "rn_51001_cutmode_eco_textview_text": "Coupe ECO", "rn_51001_resetrobotpwd_unregisteredtip_textview_text": "¡Lo sentimos! Por seguridad, su robot debe registrarse primero y después debe restablecer la contraseña aquí. O puede contratar a su distribuidor para que le ayude.", "rn_51001_statistics_totalchargetime_textview_text": "Durée de charge totale", "rn_51001_drivepastwire_cm_textview_text": "Distance de dépassement du câble (/cm)", "rn_51001_panelhome_status51_textview_text": "Cartographie - Zone secondaire", "rn_51001_cutmode_title_textview_text": "Mode de coupe", "rn_51001_panelhome_status23_textview_text": "Retour - Stationnement", "rn_51001_modify_copyto_button_text": "Copier vers", "rn_51001_detaillist_cutmode_textview_text": "Mode de coupe", "rn_51001_modify_tuesday_textview_text": "<PERSON><PERSON>", "rn_51001_dashboard_alert2title_textview_text": "Coupe de la zone sélectionnée", "rn_51001_guardagainsttheft_resetpwdfail_textview_text": "Le mot de passe de réinitialisation du robot a échoué, veuillez l’envoyer à nouveau.", "rn_51001_map_renametitle_textview_text": "Renommer la carte", "rn_51001_schedule_weeklyoverview_textview_text": "Aperçu hebdomadaire", "rn_51001_panelhome_cancel_textview_text": "Annuler", "rn_51001_modify_thursday_textview_text": "<PERSON><PERSON>", "rn_51001_alarmsetting_durationleft_textview_text": "Du<PERSON>e de l'alarme", "rn_51001_guardagainsttheft_title_textview_text": "Protection antivol", "rn_51001_dashboard_selectedareacuttingunit_textview_text": "m", "rn_51001_cutheight_done_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_panelhome_park_textview_text": "Stationnement", "rn_51001_resetrobotpwd_successtip3_textview_text": "Si votre problème persiste, consultez un revendeur.", "rn_51001_panelhome_status20_textview_text": "Retour - <PERSON><PERSON> terminée", "rn_51001_headlight_color_textview_text": "<PERSON><PERSON><PERSON> de phare", "rn_51001_resetrobotpwd_successtip2end_textview_text": "pour démarrer votre robot.", "rn_51001_cutmode_selectedarea_textview_text": "Coupe de la zone sélectionnée", "rn_51001_geofencesensitivity_setting_textview_text": "Réglage de la sensibilité", "rn_51001_detaillist_remotecontrol_textview_text": "Télécommande", "rn_51001_timerange_start_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_pwdnotcorrect_textview_text": "Le code de vérification est incorrect", "rn_51001_guardagainsttheft_titlenew_textview_text": "Réglages antivol", "rn_51001_cutmode_cuttingradius_textview_text": "Rayon de coupe", "rn_51001_panelhome_schedule_button_text": "Programmation", "rn_51001_dashboard_exceededrangewaning_textview_text": "Vous avez dépassé la portée de la télécommande.", "rn_51001_backdistance_cm_textview_text": "Distance arrière (/cm)", "rn_51001_headlight_red_textview_text": "Rouge", "rn_51001_resetrobotpwd_failtip1_textview_text": "Désolé, la réinitialisation du mot de passe a échoué.", "rn_51001_dashboard_modaltitle_textview_text": "Démarrage du moteur de coupe", "rn_51001_headlight_schedule_textview_text": "Programmation de phare", "rn_51001_resetrobotpwd_unregisteredtip3_textview_text": "<PERSON><PERSON><PERSON> r<PERSON><PERSON><PERSON><PERSON>z le mot de passe ici.", "rn_51001_statistics_totalstatistics_textview_text": "Statistiques totales", "rn_51001_headlight_on_textview_text": "Toujours allumé", "rn_51001_modify_addtime_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_cutmode_selectedarearadius_textview_text": "Rayon de coupe (/m)", "rn_51001_statistics_title_textview_text": "Statistiques", "rn_51001_drivepastwire_titleleft_textview_text": "Distance de dépassement du câble", "rn_51001_panelhome_status46_textview_text": "Charge terminée - Défaut non éliminé", "rn_51001_dashboard_alertmessage_textview_text": "Vou<PERSON><PERSON>-vous abandonner cette tâche ?", "rn_51001_alarmsetting_alarmenable_textview_text": "Alarme en cas de levage", "rn_51001_modify_wed_button_text": "MER", "rn_51001_modify_mon_button_text": "LUN", "rn_51001_backhomecorridor_title_textview_text": "Couloir de retour", "rn_51001_drivepastwire_title_textview_text": "Distance de dépassement du câble", "rn_51001_modify_friday_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_cutmode_workareanote_textview_text": "Remarque : Le robot tondra automatiquement la pelouse en fonction de sa programmation et des paramètres personnalisés.", "rn_51001_panelhome_updatemap_textview_text": "Réinitialiser la carte", "rn_51001_rainsensor_delay_textview_text": "Temps de retard (/min)", "rn_51001_cutmode_edgenote_textview_text": "Remarque : Le robot commence par couper le long du câble périphérique de la pelouse à chaque nouvelle programmation.", "rn_51001_cutmode_selectedareanote_textview_text": "Lorsque la coupe de la zone sélectionnée est activée, le robot coupe la zone autour du point actuel dans un certain rayon.", "rn_51001_headlight_period_textview_text": "Définir la durée", "rn_51001_dashboard_stopalertmsg_textview_text": "L'appareil est en fonctionnement. Voulez-vous arrêter ?", "rn_51001_resetrobotpwd_resettipstart_textview_text": "Nous venons d'envoyer un code numérique de 6 chiffres à l'adresse électronique de votre robot enregistré", "rn_51001_alarmsetting_title_textview_text": "Réglage de l'alarme", "rn_51001_panelhome_secondarea_textview_text": "Deuxième zone", "rn_51001_panelhome_pickercentertitle_textview_text": "heure", "rn_51001_cutmode_sysnote_textview_text": "Le robot commence à couper en lignes droites et systématiques.", "rn_51001_panelhome_status60_textview_text": "Tonte - Zone principale", "rn_51001_modify_sat_button_text": "SAM", "rn_51001_headlight_flashes_textview_text": "Clignote en cas d'erreur", "rn_51001_modify_done_button_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_unregisteredtip1_textview_text": "Désolé !", "rn_51001_geofencesensitivity_low_textview_text": "Bas"}, "nl": {"rn_51001_smartexitdockingstation_note_textview_text": "Opmerking: De robot keert langs de draad terug naar de ingevoerde afstand en vertrekt dan om te maaien!", "rn_51001_modify_validate_textview_text": "Eindtijd mag niet later zijn dan start<PERSON>jd", "rn_51001_panelhome_mailsemptyerror_textview_text": "E-mailadres is leeg, verificatie is mislukt", "rn_51001_geofencesensitivity_medium_textview_text": "Medium", "rn_51001_modify_conflict_textview_text": "Conflict met huidige periode; reset", "rn_51001_panelhome_status40_textview_text": "Opladen voltooid - In afwachting van het volgend maaischema", "rn_51001_resetrobotpwd_toregister_button_text": "Registreren", "rn_51001_statistics_totalworkinghours_textview_text": "Totaal aantal werkingsuren", "rn_51001_panelhome_updatemapfailnote_textview_text": "Spiacenti! Lo stato corrente del robot non consente il ripristino della mappa. Riavvia il tagliaerba.", "rn_51001_resetrobotpwd_done_button_text": "<PERSON><PERSON><PERSON>", "rn_51001_batterydetail_title_textview_text": "Accu-informatie", "rn_51001_panelhome_status45_textview_text": "Opladen voltooid - Deksel open", "rn_51001_detaillist_cutheight_textview_text": "Maaihoogte", "rn_51001_geofencesensitivity_high_textview_text": "<PERSON><PERSON>", "rn_51001_backhomecorridor_widthleft_textview_text": "<PERSON><PERSON><PERSON> van gang", "rn_51001_resetrobotpwd_unregisteredtip4_textview_text": "Of u kunt contact opnemen met uw dealer voor hulp.", "rn_51001_modify_addtime_button_text": "Ti<PERSON>d toe<PERSON>", "rn_51001_statistics_energysavingdata_textview_text": "<PERSON><PERSON><PERSON><PERSON> over energiebesparing", "rn_51001_statistics_totaldistance_textview_text": "Totaal afgelegde afstand", "rn_51001_modify_sunday_textview_text": "Zondag", "rn_51001_panelhome_status21_textview_text": "Terugkeren - Accu is leeg", "rn_51001_panelhome_interfaceerror_textview_text": "Interface niet <PERSON>, fout gemeld", "rn_51001_modal_slideronOff_textview_text": "A<PERSON>/Uit", "rn_51001_cutmode_spiral_textview_text": "Spiraalvormig maaien", "rn_51001_override_48_textview_text": "48u", "rn_51001_panelhome_parktiphour_textview_text": "De insteltijd moet één uur later dan de huidige tijd zijn", "rn_51001_panelhome_updatemapmsg_textview_text": "Stai per ripristinare una mappa. La nuova mappa sostituirà quella corrente. Continuare?", "rn_51001_headlight_white_textview_text": "Wit", "rn_51001_panelhome_status30_textview_text": "<PERSON><PERSON> met op<PERSON>n", "rn_51001_panelhome_status22_textview_text": "Terugkeren - Regen", "rn_51001_resetrobotpwd_successtip1_textview_text": "Het wachtwo<PERSON> van uw robot is succesvol gereset.", "rn_51001_resetrobotpwd_pwdnotcompleted_textview_text": "Voer de verificatiecode in", "rn_51001_resetrobotpwd_failtip2_textview_text": "<PERSON>org ervoor dat u een actieve verbinding met uw maaier hebt, via <PERSON><PERSON> of het netwerk", "rn_51001_drivepastwire_note_textview_text": "Nota: questa è la distanza che la parte anteriore del robot può percorrere oltre il cavo perimetrale.", "rn_51001_cutheight_inch_textview_text": "Maaihoogte a<PERSON>passen (/inch)", "rn_51001_modify_sun_button_text": "ZO", "rn_51001_modify_wednesday_textview_text": "Woensdag", "rn_51001_panelhome_otaalertmsg_textview_text": "Als u niet bijwerkt, werkt het apparaat niet naar behoren. Werk nu bij.", "rn_51001_modify_tue_button_text": "DI", "rn_51001_resetrobotpwd_successtip2start_textview_text": "Gebruik", "rn_51001_detaillist_antitheft_textview_text": "Antidiefstalinstellingen", "rn_51001_statistics_totalchargenote_textview_text": "De totale laadtijd is de tijd die de maaier doorbrengt in het laadstation. Houd er rekening mee dat de laadtijd niet is inbegrepen in het totaal aantal werkingsuren.", "rn_51001_statistics_energynote_textview_text": "In vergelijking met andere producten zorgt de EGO-maaier voor een lagere totale uitstoot van koolstofdioxide tijdens de werking.", "rn_51001_panelhome_status50_textview_text": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "rn_51001_override_24_textview_text": "24u", "rn_51001_statistics_batterydata_textview_text": "Accugege<PERSON><PERSON>", "rn_51001_resetrobotpwd_resettipsend_textview_text": ". V<PERSON>r de onderstaande code in om uw e-mailadres te verifiëren.", "rn_51001_resetrobotpwd_unregistertip_button_text": "Spiacenti!\nPer motivi di sicurezza, il robot deve essere registrato per\n poter ripristinare la password.\nContatta il rivenditore per ricevere assistenza.", "rn_51001_statistics_chargingcyclenote_textview_text": "De laadcyclus toont het aantal keren dat de accu volledig is opgeladen en ontladen.", "rn_51001_headlight_off_textview_text": "Altijd UIT", "rn_51001_headlight_green_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_sucesstitle_textview_text": "La password del robot è stata ripristinata correttamente.$$ Usa \"0000\" per avviare il robot. Se il problema persiste, contatta un rivenditore.", "rn_51001_resetrobotpwd_failtip3_textview_text": "verbinding met uw ma<PERSON><PERSON>, via Bluetooth", "rn_51001_backhomecorridor_note_textview_text": "Nota: questa è la distanza tra il robot e il cavo perimetrale quando il robot ritorna. Imposta la massima distanza possibile per ridurre le linee sul prato.", "rn_51001_panelhome_status44_textview_text": "Opladen voltooid - In afwachting op starten", "rn_51001_resetrobotpwd_failtip_textview_text": "<PERSON><PERSON><PERSON><PERSON>, il ripristino della password non è riuscito.$$Assicurati che il tagliaerba sia$$ collegato tramite Bluetooth$$ o una rete mobile.", "rn_51001_alarmsetting_duration_textview_text": "<PERSON>ur van alarm (/min)", "rn_51001_override_note_textview_text": "De robot maait het gazon continu op basis van uw instelling.", "rn_51001_statistics_batteryhealth_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON> van accu", "rn_51001_dashboard_exit_textview_text": "Afsluiten", "rn_51001_timerange_end_textview_text": "Eindigen", "rn_51001_otherslist_headlight_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_modify_all_textview_text": "Alles selecteren", "rn_51001_resetrobotpwd_finddealer_button_text": "Dealer vinden", "rn_51001_panelhome_status41_textview_text": "Opladen voltooid - Maaischema niet ingesteld", "rn_51001_resetrobotpwd_fail_textview_text": "Mislukt", "rn_51001_cutmode_workarea_textview_text": "Werkgebied", "rn_51001_override_72_textview_text": "72u", "rn_51001_resetrobotpwd_failtip4_textview_text": "of het mobiele netwerk.", "rn_51001_panelhome_updatemapfailnote4_textview_text": "Spiacenti! Lo stato corrente del robot non consente il ripristino della mappa. Riavvia il tagliaerba.", "rn_51001_cutmode_econote_textview_text": "Opmerking: Als de robot detecteert dat het gazon is gemaaid, zal deze er snel overheen rijden totdat het ongemaaid gras wordt bereikt!", "rn_51001_resetrobotpwd_success_textview_text": "Succes", "rn_51001_panelhome_mainarea_textview_text": "Hoofdgebied", "rn_51001_panelhome_status11_textview_text": "Pauze - Stop", "rn_51001_modify_saturday_textview_text": "Zaterdag", "rn_51001_backhomecorridor_width_textview_text": "<PERSON><PERSON><PERSON> van gang (/mm)", "rn_51001_panelhome_pickerlefttitle_textview_text": "datum", "rn_51001_override_done_button_text": "<PERSON><PERSON><PERSON>", "rn_51001_cutmode_workareasecond_textview_text": "Tweede gebied", "rn_51001_modify_delete_button_text": "Verwijderen", "rn_51001_alarmsetting_switch_textview_text": "Alarminstelling", "rn_51001_modify_fri_button_text": "VR", "rn_51001_statistics_totaltravelnote_textview_text": "De totale afgelegde afstand is de afstand die de maaier in alle modi heeft afgelegd.", "rn_51001_resetrobotpwd_problemsolved_button_text": "Probleem opgelost", "rn_51001_smartexitdockingstation_title_textview_text": "Dockingstation uitrijden", "rn_51001_panelhome_status25_textview_text": "Terugkeren - Buiten het schema", "rn_51001_detaillist_others_textview_text": "Overige", "rn_51001_panelhome_mapsettings_textview_text": "Kaartinstellingen", "rn_51001_panelhome_updatetitle_textview_text": "Nieuwe update beschikbaar", "rn_51001_panelhome_status14_textview_text": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "rn_51001_detaillist_functionsettings_textview_text": "Functie-instellingen", "rn_51001_panelhome_status27_textview_text": "Terugkeren - <PERSON><PERSON>", "rn_51001_backdistance_titleleft_textview_text": "Afstand aan a<PERSON>t", "rn_51001_panelhome_parktipmax_textview_text": "De insteltijd mag niet meer dan 99 u bedragen", "rn_51001_panelhome_updatemapfailnote7_textview_text": "Spiacenti! Lo stato corrente del robot non consente il ripristino della mappa. Riavvia il tagliaerba.", "rn_51001_dashboard_modalmessage_textview_text": "Druk op beide knoppen tegelijk om te starten", "rn_51001_guardagainsttheft_alarmsetting_textview_text": "Alarminstelling", "rn_51001_panelhome_devicedidreceivecmd_textview_text": "Het apparaat he<PERSON><PERSON> met succes de opdracht voor het resetten van het wachtwoord ontvangen", "rn_51001_detaillist_bordermanagement_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_panelhome_pickerrighttitle_textview_text": "minuut", "rn_51001_panelhome_updatemapfailnote6_textview_text": "Spiacenti! Lo stato corrente del robot non consente il ripristino della mappa. Riavvia il tagliaerba.", "rn_51001_cutmode_sys_textview_text": "Systematisch maaien", "rn_51001_panelhome_status26_textview_text": "Terugkeren- Geroepen door laadstation", "rn_51001_schedulemanagement_title_textview_text": "<PERSON>s<PERSON><PERSON>r", "rn_51001_dashboard_alert2subtitle_textview_text": "Maairadius (/m)", "rn_51001_panelhome_mowingpath_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_detaillist_availablesettings_textview_text": "Beschikbare instellingen", "rn_51001_panelhome_status13_textview_text": "Pauze - Fout", "rn_51001_bordermanagement_title_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_guardagainsttheft_resetrobotpwd_textview_text": "Robotwachtwoord resetten", "rn_51001_guardagainsttheft_geofencesetting_textview_text": "Geofence-instellingen", "rn_51001_panelhome_status12_textview_text": "Pauze - Signaalconflict", "rn_51001_statistics_chargingcycle_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_successtipend_textview_text": "om uw robot te starten.$$Als uw probleem zich nog steeds voordoet, kunt u een dealer vinden.", "rn_51001_panelhome_updatemapfailnote5_textview_text": "Spiacenti! Lo stato corrente del robot non consente il ripristino della mappa. Riavvia il tagliaerba.", "rn_51001_cutmode_edge_textview_text": "<PERSON><PERSON>n ma<PERSON>en", "rn_51001_modify_period_textview_text": "Tijdsperiode", "rn_51001_cutheight_title_textview_text": "Maaihoogte", "rn_51001_weeklyoverview_title_textview_text": "Weekoverzicht", "rn_51001_panelhome_status61_textview_text": "Maaien - Secundair gebied", "rn_51001_resetrobotpwd_unregisteredtip2_textview_text": "Om veiligheidsredenen moet uw robot eerst geregistreerd zijn en", "rn_51001_cutheight_mm_textview_text": "Maaihoogte a<PERSON>passen (/mm)", "rn_51001_statistics_totalco2savings_textview_text": "Totale CO2-besparing", "rn_51001_panelhome_status24_textview_text": "Terugkeren - Signaalconflict aan grens", "rn_51001_schedule_override_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_cutmode_spiralsensitivity_textview_text": "Gevoeligheidsniveau", "rn_51001_cutheight_titleleft_textview_text": "Maaihoogte a<PERSON>passen", "rn_51001_statistics_totalenergyconsumption_textview_text": "Totaal energieverbruik", "rn_51001_panelhome_satellitemap_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_otherslist_rainsensornote_textview_text": "Nota: quando viene rilevata pioggia, il robot tornerà alla stazione di ricarica e si riavvierà quando il sensore pioggia è asciutto e il tempo di ritardo è trascorso.", "rn_51001_modify_monday_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_detaillist_schedule_textview_text": "<PERSON>s<PERSON><PERSON>r", "rn_51001_panelhome_done_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_pwdcheckedsuccess_textview_text": "Verificatie van verificatiecode geslaagd, reset-opdracht verzenden", "rn_51001_cutmode_workareamain_textview_text": "Hoofdgebied", "rn_51001_panelhome_status42_textview_text": "Opladen voltooid - Uitstel door regen", "rn_51001_modify_noschedule_textview_text": "Geen schema", "rn_51001_geofencesetting_title_textview_text": "Geofence-instelling", "rn_51001_modify_setperiod_textview_text": "Tijdsperiode instellen", "rn_51001_panelhome_status10_textview_text": "<PERSON><PERSON>", "rn_51001_headlight_blue_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_cutmode_spiralnote_textview_text": "De robot maait het gras in een spiraalvormig patroon als het gras in een bepaald gebied langer is.", "rn_51001_resetrobotpwd_title_textview_text": "Robotwachtwoord resetten", "rn_51001_resetrobotpwd_successtipstart_textview_text": "Het wachtwoord van uw robot is succesvol gereset. $$Gebruik \"XXXX\" om uw robot te starten", "rn_51001_panelhome_geofencesetting_textview_text": "Geofence-instelling", "rn_51001_panelhome_status43_textview_text": "Opladen voltooid - Parkeren", "rn_51001_panelhome_parkuntil_textview_text": "<PERSON><PERSON> tot", "rn_51001_resetrobotpwd_resetagain_button_text": "Opnieuw resetten", "rn_51001_override_0_textview_text": "0u", "rn_51001_panelhome_parktipmin_textview_text": "De insteltijd moet groter zijn dan de huidige tijd.", "rn_51001_dashboard_alerttitle_textview_text": "Tip", "rn_51001_modify_thu_button_text": "DO", "rn_51001_statistics_consumptionunit_textview_text": "A/H", "rn_51001_otherslist_rainsensor_textview_text": "Instelling regensensor", "rn_51001_cutmode_eco_textview_text": "ECO-ma<PERSON>en", "rn_51001_resetrobotpwd_unregisteredtip_textview_text": "Spiacenti! Per motivi di sicurezza, il robot deve essere registrato per poter ripristinare la password. Contatta il rivenditore per ricevere assistenza.", "rn_51001_statistics_totalchargetime_textview_text": "Totale laadti<PERSON>d", "rn_51001_drivepastwire_cm_textview_text": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> draad (/cm)", "rn_51001_panelhome_status51_textview_text": "<PERSON><PERSON> - Secunda<PERSON> geb<PERSON>", "rn_51001_cutmode_title_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_panelhome_status23_textview_text": "Terugkeren - Parkeren", "rn_51001_modify_copyto_button_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_detaillist_cutmode_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_modify_tuesday_textview_text": "Dinsdag", "rn_51001_dashboard_alert2title_textview_text": "Gese<PERSON>eerd gebied maaien", "rn_51001_guardagainsttheft_resetpwdfail_textview_text": "wachtwoord van robot resetten is mislukt, stuur het opnieuw.", "rn_51001_map_renametitle_textview_text": "<PERSON><PERSON>", "rn_51001_schedule_weeklyoverview_textview_text": "Weekoverzicht", "rn_51001_panelhome_cancel_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_modify_thursday_textview_text": "Donderdag", "rn_51001_alarmsetting_durationleft_textview_text": "<PERSON><PERSON> van alarm", "rn_51001_guardagainsttheft_title_textview_text": "Antidiefstalbeveiliging", "rn_51001_dashboard_selectedareacuttingunit_textview_text": "m", "rn_51001_cutheight_done_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_panelhome_park_textview_text": "<PERSON><PERSON>", "rn_51001_resetrobotpwd_successtip3_textview_text": "Als uw probleem zich nog steeds voordoet, kunt u een dealer vinden.", "rn_51001_panelhome_status20_textview_text": "Terugkeren - Maaien voltooid", "rn_51001_headlight_color_textview_text": "Lichte kleur", "rn_51001_resetrobotpwd_successtip2end_textview_text": "om uw robot te starten.", "rn_51001_cutmode_selectedarea_textview_text": "Gese<PERSON>eerd gebied maaien", "rn_51001_geofencesensitivity_setting_textview_text": "Gevoeligheidsinstelling", "rn_51001_detaillist_remotecontrol_textview_text": "Afstandsbediening", "rn_51001_timerange_start_textview_text": "Starten", "rn_51001_resetrobotpwd_pwdnotcorrect_textview_text": "De verificatiecode is onjuist", "rn_51001_guardagainsttheft_titlenew_textview_text": "Antidiefstalinstelling", "rn_51001_cutmode_cuttingradius_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_panelhome_schedule_button_text": "<PERSON><PERSON><PERSON>", "rn_51001_dashboard_exceededrangewaning_textview_text": "U hebt het regelbare bereik van de afstandsbediening overschreden.", "rn_51001_backdistance_cm_textview_text": "Afstand aan achter<PERSON> (/cm)", "rn_51001_headlight_red_textview_text": "Rood", "rn_51001_resetrobotpwd_failtip1_textview_text": "Sorry, het resetten van het wachtwoord is mislukt.", "rn_51001_dashboard_modaltitle_textview_text": "Maaimotor starten", "rn_51001_headlight_schedule_textview_text": "Lichtschema", "rn_51001_resetrobotpwd_unregisteredtip3_textview_text": "reset het wachtwoord dan hier.", "rn_51001_statistics_totalstatistics_textview_text": "Totale statistieken", "rn_51001_headlight_on_textview_text": "Altijd AAN", "rn_51001_modify_addtime_textview_text": "Tijdsperiode", "rn_51001_cutmode_selectedarearadius_textview_text": "Maairadius (/m)", "rn_51001_statistics_title_textview_text": "Statistieken", "rn_51001_drivepastwire_titleleft_textview_text": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> d<PERSON>ad", "rn_51001_panelhome_status46_textview_text": "Opladen voltooid - <PERSON><PERSON> niet verholpen", "rn_51001_dashboard_alertmessage_textview_text": "Wilt u stoppen met deze taak?", "rn_51001_alarmsetting_alarmenable_textview_text": "Alarm bij opheffen", "rn_51001_modify_wed_button_text": "WO", "rn_51001_modify_mon_button_text": "MA", "rn_51001_backhomecorridor_title_textview_text": "<PERSON>", "rn_51001_drivepastwire_title_textview_text": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> d<PERSON>ad", "rn_51001_modify_friday_textview_text": "Vrijdag", "rn_51001_cutmode_workareanote_textview_text": "Opmerking: De robot maait het gazon automatisch volgens het schema en de persoonlijke instellingen.", "rn_51001_panelhome_updatemap_textview_text": "<PERSON><PERSON> resetten", "rn_51001_rainsensor_delay_textview_text": "Vertragingstijd (/min)", "rn_51001_cutmode_edgenote_textview_text": "Mededeling: De robot start in elk nieuw schema e<PERSON>t met het maaien langs de begrenzingsdraad van het gazon.", "rn_51001_cutmode_selectedareanote_textview_text": "<PERSON><PERSON> geselecteerd gebied maaien ingeschakeld is, zal de robot het gebied rond het huidige punt binnen een bepaalde radius maaien.", "rn_51001_headlight_period_textview_text": "Tijdsperiode instellen", "rn_51001_dashboard_stopalertmsg_textview_text": "Apparaat is in werking. Wilt u stoppen?", "rn_51001_resetrobotpwd_resettipstart_textview_text": "We hebben zonet een code van 6 cijfers naar het e-mailadres van uw geregistreerde robot gestuurd", "rn_51001_alarmsetting_title_textview_text": "Alarminstelling", "rn_51001_panelhome_secondarea_textview_text": "Tweede gebied", "rn_51001_panelhome_pickercentertitle_textview_text": "uur", "rn_51001_cutmode_sysnote_textview_text": "De robot begint in rechte, systematische lijnen te maaien.", "rn_51001_panelhome_status60_textview_text": "Ma<PERSON><PERSON> - hoofdgebied", "rn_51001_modify_sat_button_text": "ZA", "rn_51001_headlight_flashes_textview_text": "<PERSON><PERSON><PERSON><PERSON> bij <PERSON>", "rn_51001_modify_done_button_text": "<PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_unregisteredtip1_textview_text": "Sorry!", "rn_51001_geofencesensitivity_low_textview_text": "Laag"}, "es": {"rn_51001_smartexitdockingstation_note_textview_text": "Nota: ¡El robot retrocede a lo largo del cable hasta la distancia de entrada y, a continuación, sale para empezar a cortar!", "rn_51001_modify_validate_textview_text": "La hora de finalización debe ser posterior a la hora de inicio", "rn_51001_panelhome_mailsemptyerror_textview_text": "El correo electrónico está vacío, la verificación ha fallado", "rn_51001_geofencesensitivity_medium_textview_text": "Media", "rn_51001_modify_conflict_textview_text": "Conflicto con el período actual; por favor, reinicie", "rn_51001_panelhome_status40_textview_text": "Proceso de carga finalizado - Esperando el próximo programa de corte", "rn_51001_resetrobotpwd_toregister_button_text": "Al registro", "rn_51001_statistics_totalworkinghours_textview_text": "Total de horas de funcionamiento", "rn_51001_panelhome_updatemapfailnote_textview_text": "Leider lässt der aktuelle Roboterstatus kein Zurücksetzen der Karte zu. Bitte holen Si<PERSON> zu<PERSON>t den Mäher zurück.", "rn_51001_resetrobotpwd_done_button_text": "<PERSON><PERSON>", "rn_51001_batterydetail_title_textview_text": "Información sobre la batería", "rn_51001_panelhome_status45_textview_text": "Proceso de carga finalizado - Tapa abierta", "rn_51001_detaillist_cutheight_textview_text": "Altura de corte", "rn_51001_geofencesensitivity_high_textview_text": "Alta", "rn_51001_backhomecorridor_widthleft_textview_text": "Anchu<PERSON> del pasillo", "rn_51001_resetrobotpwd_unregisteredtip4_textview_text": "O puede contratar a su distribuidor para que le ayude.", "rn_51001_modify_addtime_button_text": "<PERSON><PERSON><PERSON>", "rn_51001_statistics_energysavingdata_textview_text": "Datos sobre ahorro de energía", "rn_51001_statistics_totaldistance_textview_text": "Distancia total recorrida", "rn_51001_modify_sunday_textview_text": "Domingo", "rn_51001_panelhome_status21_textview_text": "Regresando - Batería baja", "rn_51001_panelhome_interfaceerror_textview_text": "Interfaz no retornada, se informa de un error", "rn_51001_modal_slideronOff_textview_text": "Encendido/apagado", "rn_51001_cutmode_spiral_textview_text": "Corte en espiral", "rn_51001_override_48_textview_text": "48 h", "rn_51001_panelhome_parktiphour_textview_text": "La hora ajustada debe ser una hora más tarde que la hora actual.", "rn_51001_panelhome_updatemapmsg_textview_text": "Eine Karte wird gerade zurückgesetzt. Die neue Karte ersetzt die aktuelle. Fortfahren?", "rn_51001_headlight_white_textview_text": "<PERSON>", "rn_51001_panelhome_status30_textview_text": "Cargando", "rn_51001_panelhome_status22_textview_text": "Regresando - Lluvia", "rn_51001_resetrobotpwd_successtip1_textview_text": "La contraseña de su robot se ha restablecido correctamente.", "rn_51001_resetrobotpwd_pwdnotcompleted_textview_text": "Introduzca el código de verificación", "rn_51001_resetrobotpwd_failtip2_textview_text": "Asegúrese de tener una conexión activa a su cortacésped, ya sea mediante Bluetooth o la red móvil", "rn_51001_drivepastwire_note_textview_text": "Hinweis: Dies ist die Entfernung, mit der die Vorderkante des Roboters über das Begrenzungskabel hinausragen kann.", "rn_51001_cutheight_inch_textview_text": "Ajustar la altura de corte (/pulgadas)", "rn_51001_modify_sun_button_text": "DOM", "rn_51001_modify_wednesday_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rn_51001_panelhome_otaalertmsg_textview_text": "Si no se actualiza, el dispositivo no funcionará correctamente. Por favor, actualice ahora.", "rn_51001_modify_tue_button_text": "MAR", "rn_51001_resetrobotpwd_successtip2start_textview_text": "Utilice", "rn_51001_detaillist_antitheft_textview_text": "<PERSON><PERSON><PERSON>s <PERSON>", "rn_51001_statistics_totalchargenote_textview_text": "El tiempo total de carga es el tiempo que pasa el cortacésped en la estación de carga. Tenga en cuenta que el tiempo de carga no está incluido en el total de horas de funcionamiento.", "rn_51001_statistics_energynote_textview_text": "En comparación con otros productos, el cortacésped EGO produce menos dióxido de carbono en total durante su funcionamiento.", "rn_51001_panelhome_status50_textview_text": "<PERSON><PERSON><PERSON> - <PERSON><PERSON> principal", "rn_51001_override_24_textview_text": "24 h", "rn_51001_statistics_batterydata_textview_text": "Datos de la batería", "rn_51001_resetrobotpwd_resettipsend_textview_text": ". Introduzca el código siguiente para verificar su dirección de correo electrónico.", "rn_51001_resetrobotpwd_unregistertip_button_text": "Entschuldigung!\nAus Sicherheitsgründen muss Ihr Roboter zuerst registriert werden.\nsDanach setzen Sie das Passwort hier zurück.\nAlternativ können Sie das Handelsgeschäft um Hilfe bitten.", "rn_51001_statistics_chargingcyclenote_textview_text": "El ciclo de carga muestra el número de veces que la batería se ha cargado y agotado por completo.", "rn_51001_headlight_off_textview_text": "<PERSON><PERSON><PERSON><PERSON>", "rn_51001_headlight_green_textview_text": "Verde", "rn_51001_resetrobotpwd_sucesstitle_textview_text": "Ihr Roboterpasswort wurde erfolgreich zurückgesetzt. Starten Sie den Roboter mit „0000“. Wenden Sie sich an den Fachhandel, wenn das Problem weiterhin besteht.", "rn_51001_resetrobotpwd_failtip3_textview_text": "conexión activa a su cortacésped, ya sea mediante Bluetooth", "rn_51001_backhomecorridor_note_textview_text": "Hinweis: Dies ist die Breite zwischen dem Roboter und dem Begrenzungskabel bei der Rückkehr des Roboters. Um Streifen im Rasen gering zu halten, sollte die Breite so breit wie möglich sein.", "rn_51001_panelhome_status44_textview_text": "Proceso de carga finalizado - Esperando el inicio", "rn_51001_resetrobotpwd_failtip_textview_text": "Leider ist das Zurücksetzen des Passworts fehlgeschlagen.$$<PERSON>elle<PERSON> sic<PERSON>, dass eine aktive Verbindung zum Mäher entweder über Bluetooth$$ oder das Mobilfunknetz besteht.", "rn_51001_alarmsetting_duration_textview_text": "Duración de la alarma (/min)", "rn_51001_override_note_textview_text": "El robot cortará el césped de forma continua en función de su configuración.", "rn_51001_statistics_batteryhealth_textview_text": "Estado de la batería", "rn_51001_dashboard_exit_textview_text": "Salir", "rn_51001_timerange_end_textview_text": "Finalización", "rn_51001_otherslist_headlight_textview_text": "Faro", "rn_51001_modify_all_textview_text": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "rn_51001_resetrobotpwd_finddealer_button_text": "Buscar distribuidor", "rn_51001_panelhome_status41_textview_text": "Proceso de carga finalizado - El programa de corte no está establecido", "rn_51001_resetrobotpwd_fail_textview_text": "Error", "rn_51001_cutmode_workarea_textview_text": "Área de trabajo", "rn_51001_override_72_textview_text": "72 h", "rn_51001_resetrobotpwd_failtip4_textview_text": "o la red móvil.", "rn_51001_panelhome_updatemapfailnote4_textview_text": "Leider lässt der aktuelle Roboterstatus kein Zurücksetzen der Karte zu. Bitte holen Si<PERSON> zu<PERSON>t den Mäher zurück.", "rn_51001_cutmode_econote_textview_text": "Nota: ¡Si el robot detecta que el césped ha sido cortado, pasará rápidamente por encima hasta llegar a la hierba sin cortar!", "rn_51001_resetrobotpwd_success_textview_text": "Éxito", "rn_51001_panelhome_mainarea_textview_text": "Zona principal", "rn_51001_panelhome_status11_textview_text": "Pausa - Parada", "rn_51001_modify_saturday_textview_text": "Sábado", "rn_51001_backhomecorridor_width_textview_text": "Anchura del pasillo (/mm)", "rn_51001_panelhome_pickerlefttitle_textview_text": "fecha", "rn_51001_override_done_button_text": "<PERSON><PERSON>", "rn_51001_cutmode_workareasecond_textview_text": "Segunda zona", "rn_51001_modify_delete_button_text": "Bo<PERSON>r", "rn_51001_alarmsetting_switch_textview_text": "Ajuste de la alarma", "rn_51001_modify_fri_button_text": "VIE", "rn_51001_statistics_totaltravelnote_textview_text": "El recorrido total es la distancia recorrida por el cortacésped en todos los modos.", "rn_51001_resetrobotpwd_problemsolved_button_text": "Problema resuelto", "rn_51001_smartexitdockingstation_title_textview_text": "Salir de la estación de acoplamiento", "rn_51001_panelhome_status25_textview_text": "Regresando - Fuera de horario", "rn_51001_detaillist_others_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_panelhome_mapsettings_textview_text": "Ajustes del mapa", "rn_51001_panelhome_updatetitle_textview_text": "Nueva actualización disponible", "rn_51001_panelhome_status14_textview_text": "Pausa - Ajuste de la altura de corte", "rn_51001_detaillist_functionsettings_textview_text": "Ajustes de función", "rn_51001_panelhome_status27_textview_text": "Regresando - Restableciendo mapa", "rn_51001_backdistance_titleleft_textview_text": "Distancia de retorno", "rn_51001_panelhome_parktipmax_textview_text": "El tiempo de ajuste no deberá exceder de 99 h", "rn_51001_panelhome_updatemapfailnote7_textview_text": "Leider lässt der aktuelle Roboterstatus kein Zurücksetzen der Karte zu. Bitte holen Si<PERSON> zu<PERSON>t den Mäher zurück.", "rn_51001_dashboard_modalmessage_textview_text": "Pulse los dos botones a la vez para iniciar", "rn_51001_guardagainsttheft_alarmsetting_textview_text": "Ajuste de la alarma", "rn_51001_panelhome_devicedidreceivecmd_textview_text": "El dispositivo recibió correctamente el comando de restablecimiento de contraseña", "rn_51001_detaillist_bordermanagement_textview_text": "Gestión de límites", "rn_51001_panelhome_pickerrighttitle_textview_text": "minuto", "rn_51001_panelhome_updatemapfailnote6_textview_text": "Leider lässt der aktuelle Roboterstatus kein Zurücksetzen der Karte zu. Bitte holen Si<PERSON> zu<PERSON>t den Mäher zurück.", "rn_51001_cutmode_sys_textview_text": "Corte sistemático", "rn_51001_panelhome_status26_textview_text": "Regresando - Llamado por la estación de carga", "rn_51001_schedulemanagement_title_textview_text": "Gestión de horarios", "rn_51001_dashboard_alert2subtitle_textview_text": "Radio de corte (/ m)", "rn_51001_panelhome_mowingpath_textview_text": "<PERSON>uta de corte", "rn_51001_detaillist_availablesettings_textview_text": "Ajustes disponibles", "rn_51001_panelhome_status13_textview_text": "Pausa - Fallo", "rn_51001_bordermanagement_title_textview_text": "Gestión de límites", "rn_51001_guardagainsttheft_resetrobotpwd_textview_text": "Restablecer la contraseña del robot", "rn_51001_guardagainsttheft_geofencesetting_textview_text": "Ajustes de geocercas", "rn_51001_panelhome_status12_textview_text": "Pausa - Conflicto de señal", "rn_51001_statistics_chargingcycle_textview_text": "Ciclo de carga", "rn_51001_resetrobotpwd_successtipend_textview_text": "al poner en marcha su robot.$$Si el problema persiste, puede buscar un distribuidor.", "rn_51001_panelhome_updatemapfailnote5_textview_text": "Leider lässt der aktuelle Roboterstatus kein Zurücksetzen der Karte zu. Bitte holen Si<PERSON> zu<PERSON>t den Mäher zurück.", "rn_51001_cutmode_edge_textview_text": "Corte de cantos", "rn_51001_modify_period_textview_text": "Periodo de tiempo", "rn_51001_cutheight_title_textview_text": "Altura de corte", "rn_51001_weeklyoverview_title_textview_text": "Resumen semanal", "rn_51001_panelhome_status61_textview_text": "Cortando césped - Área secundaria", "rn_51001_resetrobotpwd_unregisteredtip2_textview_text": "<PERSON>r seguridad, su robot debe registrarse primero y", "rn_51001_cutheight_mm_textview_text": "Ajustar altura de corte (/mm)", "rn_51001_statistics_totalco2savings_textview_text": "Ahorro total de CO2", "rn_51001_panelhome_status24_textview_text": "Regresando - Conflicto de señal de límite", "rn_51001_schedule_override_textview_text": "Anular programa", "rn_51001_cutmode_spiralsensitivity_textview_text": "Nivel de sensibilidad", "rn_51001_cutheight_titleleft_textview_text": "Ajustar la altura de corte", "rn_51001_statistics_totalenergyconsumption_textview_text": "Consumo total de energía", "rn_51001_panelhome_satellitemap_textview_text": "Mapa por satélite", "rn_51001_otherslist_rainsensornote_textview_text": "Hinweis: Wenn Regen er<PERSON>nt wird, kehrt der Roboter zur Ladestation zurück und startet wieder, wenn der Regensensor trocken und die Wartezeit abgelaufen ist.", "rn_51001_modify_monday_textview_text": "<PERSON><PERSON>", "rn_51001_detaillist_schedule_textview_text": "Gestión de horarios", "rn_51001_panelhome_done_textview_text": "<PERSON><PERSON>", "rn_51001_resetrobotpwd_pwdcheckedsuccess_textview_text": "Verificación correcta del código de verificación, enviar comando de reinicio", "rn_51001_cutmode_workareamain_textview_text": "Zona principal", "rn_51001_panelhome_status42_textview_text": "Proceso de carga finalizado - Retraso por lluvia", "rn_51001_modify_noschedule_textview_text": "Sin horario", "rn_51001_geofencesetting_title_textview_text": "Ajuste de geocercas", "rn_51001_modify_setperiod_textview_text": "E<PERSON>cer periodo de tiempo", "rn_51001_panelhome_status10_textview_text": "Pausa", "rn_51001_headlight_blue_textview_text": "Azul", "rn_51001_cutmode_spiralnote_textview_text": "El robot cortará la hierba en espiral si la hierba es más larga en una zona determinada.", "rn_51001_resetrobotpwd_title_textview_text": "Restablecer la contraseña del robot", "rn_51001_resetrobotpwd_successtipstart_textview_text": "La contraseña de su robot se ha restablecido con éxito.$$Utilice «XXXX» para poner en marcha el robot.", "rn_51001_panelhome_geofencesetting_textview_text": "Ajuste de geocercas", "rn_51001_panelhome_status43_textview_text": "Proceso de carga finalizado - Estacionando", "rn_51001_panelhome_parkuntil_textview_text": "<PERSON><PERSON><PERSON> hasta", "rn_51001_resetrobotpwd_resetagain_button_text": "Restablecer de nuevo", "rn_51001_override_0_textview_text": "0 h", "rn_51001_panelhome_parktipmin_textview_text": "La hora de ajuste debe ser mayor que la hora actual.", "rn_51001_dashboard_alerttitle_textview_text": "Consejo", "rn_51001_modify_thu_button_text": "JUE", "rn_51001_statistics_consumptionunit_textview_text": "A/H", "rn_51001_otherslist_rainsensor_textview_text": "Ajuste del sensor de lluvia", "rn_51001_cutmode_eco_textview_text": "Corte ECO", "rn_51001_resetrobotpwd_unregisteredtip_textview_text": "Entschuldigung!Aus Sicherheitsgründen muss Ihr Roboter zuerst registriert werden. <PERSON><PERSON> setzen Sie das Passwort hier zurück. Alternativ können Sie das Handelsgeschäft um Hilfe bitten.", "rn_51001_statistics_totalchargetime_textview_text": "Tiempo total de carga", "rn_51001_drivepastwire_cm_textview_text": "Pasar el cable (/cm)", "rn_51001_panelhome_status51_textview_text": "Creando mapa - Área secundaria", "rn_51001_cutmode_title_textview_text": "Modo de corte", "rn_51001_panelhome_status23_textview_text": "Regresando - Estacionamiento", "rn_51001_modify_copyto_button_text": "Copiar en", "rn_51001_detaillist_cutmode_textview_text": "Modo de corte", "rn_51001_modify_tuesday_textview_text": "<PERSON><PERSON>", "rn_51001_dashboard_alert2title_textview_text": "Corte de la zona seleccionada", "rn_51001_guardagainsttheft_resetpwdfail_textview_text": "Error en el restablecimiento de la contraseña del robot, envíelo de nuevo.", "rn_51001_map_renametitle_textview_text": "Renombrar mapa", "rn_51001_schedule_weeklyoverview_textview_text": "Resumen semanal", "rn_51001_panelhome_cancel_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_modify_thursday_textview_text": "<PERSON><PERSON>", "rn_51001_alarmsetting_durationleft_textview_text": "Duración de la alarma", "rn_51001_guardagainsttheft_title_textview_text": "Protección antirrobo", "rn_51001_dashboard_selectedareacuttingunit_textview_text": "m", "rn_51001_cutheight_done_textview_text": "<PERSON><PERSON>", "rn_51001_panelhome_park_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_successtip3_textview_text": "Si el problema persiste, puede buscar un distribuidor.", "rn_51001_panelhome_status20_textview_text": "Regresando - Corte de césped finalizado", "rn_51001_headlight_color_textview_text": "Color claro", "rn_51001_resetrobotpwd_successtip2end_textview_text": "para poner en marcha el robot.", "rn_51001_cutmode_selectedarea_textview_text": "Corte de la zona seleccionada", "rn_51001_geofencesensitivity_setting_textview_text": "Ajuste de sensibilidad", "rn_51001_detaillist_remotecontrol_textview_text": "Mando a distancia", "rn_51001_timerange_start_textview_text": "<PERSON><PERSON>o", "rn_51001_resetrobotpwd_pwdnotcorrect_textview_text": "El código de verificación es incorrecto", "rn_51001_guardagainsttheft_titlenew_textview_text": "<PERSON><PERSON><PERSON>s <PERSON>", "rn_51001_cutmode_cuttingradius_textview_text": "Radio de corte", "rn_51001_panelhome_schedule_button_text": "<PERSON><PERSON><PERSON>", "rn_51001_dashboard_exceededrangewaning_textview_text": "Ha superado el alcance de control del mando a distancia.", "rn_51001_backdistance_cm_textview_text": "Distancia de retorno (/cm)", "rn_51001_headlight_red_textview_text": "<PERSON><PERSON><PERSON>", "rn_51001_resetrobotpwd_failtip1_textview_text": "<PERSON> sentimo<PERSON>, el restablecimiento de la contraseña ha fallado.", "rn_51001_dashboard_modaltitle_textview_text": "Arrancar el motor de corte", "rn_51001_headlight_schedule_textview_text": "Ho<PERSON>io de luz", "rn_51001_resetrobotpwd_unregisteredtip3_textview_text": "a continuación, restablezca la contraseña aquí.", "rn_51001_statistics_totalstatistics_textview_text": "Estadísticas totales", "rn_51001_headlight_on_textview_text": "Siempre encendido", "rn_51001_modify_addtime_textview_text": "Periodo de tiempo", "rn_51001_cutmode_selectedarearadius_textview_text": "Radio de corte (/m)", "rn_51001_statistics_title_textview_text": "Estadísticas", "rn_51001_drivepastwire_titleleft_textview_text": "Pasar el cable", "rn_51001_panelhome_status46_textview_text": "Proceso de carga finalizado - Fallo no eliminado", "rn_51001_dashboard_alertmessage_textview_text": "¿Quiere abandonar esta tarea?", "rn_51001_alarmsetting_alarmenable_textview_text": "Al<PERSON> cuando se eleva", "rn_51001_modify_wed_button_text": "MIÉ", "rn_51001_modify_mon_button_text": "LUN", "rn_51001_backhomecorridor_title_textview_text": "Pasillo de vuelta a la base", "rn_51001_drivepastwire_title_textview_text": "Pasar el cable", "rn_51001_modify_friday_textview_text": "Viernes", "rn_51001_cutmode_workareanote_textview_text": "Nota: El robot cortará el césped automáticamente según el programa y los ajustes personalizados.", "rn_51001_panelhome_updatemap_textview_text": "Restablecer mapa", "rn_51001_rainsensor_delay_textview_text": "Tiempo de retardo (/min)", "rn_51001_cutmode_edgenote_textview_text": "Nota: el robot empieza a cortar primero a lo largo del cable delimitador del césped en cada nueva programación.", "rn_51001_cutmode_selectedareanote_textview_text": "Cuando el corte de la zona seleccionada está activado, el robot cortará el área alrededor del punto actual dentro de un radio determinado.", "rn_51001_headlight_period_textview_text": "E<PERSON>cer periodo de tiempo", "rn_51001_dashboard_stopalertmsg_textview_text": "El aparato está en funcionamiento. ¿Quiere parar?", "rn_51001_resetrobotpwd_resettipstart_textview_text": "Acabamos de enviar un código de 6 dígitos a la dirección de correo electrónico de su robot registrado", "rn_51001_alarmsetting_title_textview_text": "Ajuste de la alarma", "rn_51001_panelhome_secondarea_textview_text": "Segunda zona", "rn_51001_panelhome_pickercentertitle_textview_text": "hora", "rn_51001_cutmode_sysnote_textview_text": "El robot comienza a cortar en líneas rectas y sistemáticas.", "rn_51001_panelhome_status60_textview_text": "<PERSON><PERSON><PERSON> c<PERSON> - <PERSON><PERSON> principal", "rn_51001_modify_sat_button_text": "SÁB", "rn_51001_headlight_flashes_textview_text": "Parpadea cuando se produce alerta de fallo", "rn_51001_modify_done_button_text": "<PERSON><PERSON>", "rn_51001_resetrobotpwd_unregisteredtip1_textview_text": "¡Lo sentimos!", "rn_51001_geofencesensitivity_low_textview_text": "Baja"}, "en-GB": {"rn_51001_smartexitdockingstation_note_textview_text": "Note: The robot backs along the wire to the input distance and then departs to mow!", "rn_51001_modify_validate_textview_text": "End time must be later than start time", "rn_51001_panelhome_mailsemptyerror_textview_text": "Email is empty, verification failed", "rn_51001_geofencesensitivity_medium_textview_text": "Medium", "rn_51001_modify_conflict_textview_text": "Conflict with current period; please reset", "rn_51001_panelhome_status40_textview_text": "Charging Completed - Waiting for next mowing schedule", "rn_51001_resetrobotpwd_toregister_button_text": "To register", "rn_51001_statistics_totalworkinghours_textview_text": "Total operating hours", "rn_51001_panelhome_updatemapfailnote_textview_text": "Sorry, the current robot status does not allow to reset map, please recove the mower first.", "rn_51001_resetrobotpwd_done_button_text": "Done", "rn_51001_batterydetail_title_textview_text": "Battery information", "rn_51001_panelhome_status45_textview_text": "Charging Completed - Lid Open", "rn_51001_detaillist_cutheight_textview_text": "Cut height", "rn_51001_geofencesensitivity_high_textview_text": "High", "rn_51001_backhomecorridor_widthleft_textview_text": "Corridor width", "rn_51001_resetrobotpwd_unregisteredtip4_textview_text": "Or you can contact your dealer for help.", "rn_51001_modify_addtime_button_text": "Add time", "rn_51001_statistics_energysavingdata_textview_text": "Energy savings data", "rn_51001_statistics_totaldistance_textview_text": "Total distance travelled", "rn_51001_modify_sunday_textview_text": "Sunday", "rn_51001_panelhome_status21_textview_text": "Returning - Low battery", "rn_51001_panelhome_interfaceerror_textview_text": "Interface not returned, error reported", "rn_51001_modal_slideronOff_textview_text": "On/Off", "rn_51001_cutmode_spiral_textview_text": "Spiral cutting", "rn_51001_override_48_textview_text": "48 hrs", "rn_51001_panelhome_parktiphour_textview_text": "The setting time must be one hour later than the current time.", "rn_51001_panelhome_updatemapmsg_textview_text": "You are reseting a map. The new map will replace the current one. Do you want to continue?", "rn_51001_headlight_white_textview_text": "White", "rn_51001_panelhome_status30_textview_text": "Charging", "rn_51001_panelhome_status22_textview_text": "Returning - <PERSON><PERSON>", "rn_51001_resetrobotpwd_successtip1_textview_text": "Your robot password has been reset successfully.", "rn_51001_resetrobotpwd_pwdnotcompleted_textview_text": "Please input the verify code", "rn_51001_resetrobotpwd_failtip2_textview_text": "Please make sure that you have an active connection to your mower, either using Bluetooth or the network.", "rn_51001_drivepastwire_note_textview_text": "Note: This is the distance that the front of the robot can move past the boundary wire.", "rn_51001_cutheight_inch_textview_text": "Adjust cut height (/inch)", "rn_51001_modify_sun_button_text": "SUN", "rn_51001_modify_wednesday_textview_text": "Wednesday", "rn_51001_panelhome_otaalertmsg_textview_text": "If you do not update, the device will not work properly. Please update now.", "rn_51001_modify_tue_button_text": "TUE", "rn_51001_resetrobotpwd_successtip2start_textview_text": "Please use", "rn_51001_detaillist_antitheft_textview_text": "Anti-theft settings", "rn_51001_statistics_totalchargenote_textview_text": "The total charging time is the time spent by the mower at the charging station. Please note that the charging time is not included in the total operating hours.", "rn_51001_statistics_energynote_textview_text": "Compared with other products, the EGO mower produces less total carbon dioxide when operating.", "rn_51001_panelhome_status50_textview_text": "Building Map - Main Area", "rn_51001_override_24_textview_text": "24 hrs", "rn_51001_statistics_batterydata_textview_text": "Battery data", "rn_51001_resetrobotpwd_resettipsend_textview_text": ". Please enter the code below to verify your email address.", "rn_51001_resetrobotpwd_unregistertip_button_text": "Sorry!\nFor safety, your robot must be register first and\n then reset the password here.\nOr you can contract your dealer for help.", "rn_51001_statistics_chargingcyclenote_textview_text": "The charging cycle shows the number of times the battery has been fully charged and discharged.", "rn_51001_headlight_off_textview_text": "Always OFF", "rn_51001_headlight_green_textview_text": "Green", "rn_51001_resetrobotpwd_sucesstitle_textview_text": "Your robot password has been reset successfully. Please use “0000” to start your robot If your problem is still there, you can find a dealer.", "rn_51001_resetrobotpwd_failtip3_textview_text": "connection to your mower, either using Bluetooth", "rn_51001_backhomecorridor_note_textview_text": "Note: This is the width between the robot and the boundary wire when robot returns. Make the width as wide as possible to reduce lines in the lawn.", "rn_51001_panelhome_status44_textview_text": "Charging Completed - Waiting for start", "rn_51001_resetrobotpwd_failtip_textview_text": "Sorry, reset password has failed.\nPlease make sure that you have an active\n connection to your mower, either using Bluetooth\n or the mobile network.", "rn_51001_alarmsetting_duration_textview_text": "Duration of alarm (/min)", "rn_51001_override_note_textview_text": "The robot will mow the lawn continuously based on your settings.", "rn_51001_statistics_batteryhealth_textview_text": "Battery health", "rn_51001_dashboard_exit_textview_text": "Exit", "rn_51001_timerange_end_textview_text": "Finish", "rn_51001_otherslist_headlight_textview_text": "Headlight", "rn_51001_modify_all_textview_text": "Select all", "rn_51001_resetrobotpwd_finddealer_button_text": "Find dealer", "rn_51001_panelhome_status41_textview_text": "Charging Completed - Mowing schedule not set", "rn_51001_resetrobotpwd_fail_textview_text": "Failed", "rn_51001_cutmode_workarea_textview_text": "Work area", "rn_51001_override_72_textview_text": "72 hrs", "rn_51001_resetrobotpwd_failtip4_textview_text": "or the mobile network.", "rn_51001_panelhome_updatemapfailnote4_textview_text": "Sorry, the current robot status does not allow to reset map, please recove the mower first.", "rn_51001_cutmode_econote_textview_text": "Note: If the robot detects that the lawn has been cut, it will drive over it quickly until it reaches uncut grass!", "rn_51001_resetrobotpwd_success_textview_text": "Success", "rn_51001_panelhome_mainarea_textview_text": "Main area", "rn_51001_panelhome_status11_textview_text": "Pause - Stop", "rn_51001_modify_saturday_textview_text": "Saturday", "rn_51001_backhomecorridor_width_textview_text": "Corridor width (/mm)", "rn_51001_panelhome_pickerlefttitle_textview_text": "date", "rn_51001_override_done_button_text": "Done", "rn_51001_cutmode_workareasecond_textview_text": "Second area", "rn_51001_modify_delete_button_text": "Delete", "rn_51001_alarmsetting_switch_textview_text": "Alarm Setting", "rn_51001_modify_fri_button_text": "FRI", "rn_51001_statistics_totaltravelnote_textview_text": "The total travel distance is the distance travelled by the mower in all modes.", "rn_51001_resetrobotpwd_problemsolved_button_text": "Problem solved", "rn_51001_smartexitdockingstation_title_textview_text": "Exit docking station", "rn_51001_panelhome_status25_textview_text": "Returning - Out of schedule", "rn_51001_detaillist_others_textview_text": "Others", "rn_51001_panelhome_mapsettings_textview_text": "Map settings", "rn_51001_panelhome_updatetitle_textview_text": "New update available", "rn_51001_panelhome_status14_textview_text": "Pause - Cut Height Adjusting", "rn_51001_detaillist_functionsettings_textview_text": "Function settings", "rn_51001_panelhome_status27_textview_text": "Returning - Rebuild Map", "rn_51001_backdistance_titleleft_textview_text": "Back distance", "rn_51001_panelhome_parktipmax_textview_text": "The setting time cannot exceed 99 hrs", "rn_51001_panelhome_updatemapfailnote7_textview_text": "    Sorry, the current robot status does not allow to reset map, please recove the mower first.", "rn_51001_dashboard_modalmessage_textview_text": "Press both buttons together to start", "rn_51001_guardagainsttheft_alarmsetting_textview_text": "Alarm setting", "rn_51001_panelhome_devicedidreceivecmd_textview_text": "The device successfully received the reset password command", "rn_51001_detaillist_bordermanagement_textview_text": "Border management", "rn_51001_panelhome_pickerrighttitle_textview_text": "minute", "rn_51001_panelhome_updatemapfailnote6_textview_text": "    Sorry, the current robot status does not allow to reset map, please recove the mower first.", "rn_51001_cutmode_sys_textview_text": "Systematic Cutting", "rn_51001_panelhome_status26_textview_text": "Returning - Called by Charging Station", "rn_51001_schedulemanagement_title_textview_text": "Schedule management", "rn_51001_dashboard_alert2subtitle_textview_text": "Cutting radius (/ m)", "rn_51001_panelhome_mowingpath_textview_text": "Mowing path", "rn_51001_detaillist_availablesettings_textview_text": "Available settings", "rn_51001_panelhome_status13_textview_text": "<PERSON><PERSON> <PERSON> <PERSON><PERSON>", "rn_51001_bordermanagement_title_textview_text": "Border management", "rn_51001_guardagainsttheft_resetrobotpwd_textview_text": "Reset Robot Password", "rn_51001_guardagainsttheft_geofencesetting_textview_text": "Geo-fence settings", "rn_51001_panelhome_status12_textview_text": "Pause - Signal Conflict", "rn_51001_statistics_chargingcycle_textview_text": "Charging cycle", "rn_51001_resetrobotpwd_successtipend_textview_text": "to start your robot.$$If your problem is still there, you can find a dealer.", "rn_51001_panelhome_updatemapfailnote5_textview_text": "    Sorry, the current robot status does not allow to reset map, please recove the mower first.", "rn_51001_cutmode_edge_textview_text": "Edge cutting", "rn_51001_modify_period_textview_text": "Time period", "rn_51001_cutheight_title_textview_text": "Cut height", "rn_51001_weeklyoverview_title_textview_text": "Weekly summary", "rn_51001_panelhome_status61_textview_text": "Mowing - Secondary Area", "rn_51001_resetrobotpwd_unregisteredtip2_textview_text": "For safety, your robot must be registered first and", "rn_51001_cutheight_mm_textview_text": "Adjust cut height (/mm)", "rn_51001_statistics_totalco2savings_textview_text": "Total CO2 savings", "rn_51001_panelhome_status24_textview_text": "Returning - Border Signal Conflicting", "rn_51001_schedule_override_textview_text": "Override schedule", "rn_51001_cutmode_spiralsensitivity_textview_text": "Sensitivity level", "rn_51001_cutheight_titleleft_textview_text": "Adjust cut height", "rn_51001_statistics_totalenergyconsumption_textview_text": "Total energy consumption", "rn_51001_panelhome_satellitemap_textview_text": "Satellite map", "rn_51001_otherslist_rainsensornote_textview_text": "Note: When rain is detected, the robot retuns to the charging station and restarts until the rain sensor is dry and delay time is over.", "rn_51001_modify_monday_textview_text": "Monday", "rn_51001_detaillist_schedule_textview_text": "Schedule management", "rn_51001_panelhome_done_textview_text": "Done", "rn_51001_resetrobotpwd_pwdcheckedsuccess_textview_text": "Verification code verification successful, send reset command", "rn_51001_cutmode_workareamain_textview_text": "Main area", "rn_51001_panelhome_status42_textview_text": "Charging Completed - Rain Delaying", "rn_51001_modify_noschedule_textview_text": "No schedule", "rn_51001_geofencesetting_title_textview_text": "Geo-fence setting", "rn_51001_modify_setperiod_textview_text": "Set time period", "rn_51001_panelhome_status10_textview_text": "Pause", "rn_51001_headlight_blue_textview_text": "Blue", "rn_51001_cutmode_spiralnote_textview_text": "The robot will cut the grass in a spiral pattern if the grass is longer in a certain area.", "rn_51001_resetrobotpwd_title_textview_text": "Reset Robot Password", "rn_51001_resetrobotpwd_successtipstart_textview_text": "Your robot password has been reset successfully. Please use \"XXXX\" to start your robot.", "rn_51001_panelhome_geofencesetting_textview_text": "Geo-fence Setting", "rn_51001_panelhome_status43_textview_text": "Charging Completed - Parking", "rn_51001_panelhome_parkuntil_textview_text": "Park until", "rn_51001_resetrobotpwd_resetagain_button_text": "Reset again", "rn_51001_override_0_textview_text": "0 hrs", "rn_51001_panelhome_parktipmin_textview_text": "The setting time must be greater than the current time.", "rn_51001_dashboard_alerttitle_textview_text": "Tip", "rn_51001_modify_thu_button_text": "THU", "rn_51001_statistics_consumptionunit_textview_text": "A/H", "rn_51001_otherslist_rainsensor_textview_text": "Rain sensor setting", "rn_51001_cutmode_eco_textview_text": "ECO cutting", "rn_51001_resetrobotpwd_unregisteredtip_textview_text": "Sorry!For safety, your robot must be register first and then reset the password here.Or you can contract your dealer for help.", "rn_51001_statistics_totalchargetime_textview_text": "Total charge time", "rn_51001_drivepastwire_cm_textview_text": "Drive past wire (/cm)", "rn_51001_panelhome_status51_textview_text": "Building Map - Secondary Area", "rn_51001_cutmode_title_textview_text": "Cutting mode", "rn_51001_panelhome_status23_textview_text": "Returning - Park", "rn_51001_modify_copyto_button_text": "Copy to", "rn_51001_detaillist_cutmode_textview_text": "Cutting mode", "rn_51001_modify_tuesday_textview_text": "Tuesday", "rn_51001_dashboard_alert2title_textview_text": "Selected area cutting", "rn_51001_guardagainsttheft_resetpwdfail_textview_text": "robot reset password failed，please send again.", "rn_51001_map_renametitle_textview_text": "Rename map", "rn_51001_schedule_weeklyoverview_textview_text": "Weekly overview", "rn_51001_panelhome_cancel_textview_text": "Cancel", "rn_51001_modify_thursday_textview_text": "Thursday", "rn_51001_alarmsetting_durationleft_textview_text": "Duration of alarm", "rn_51001_guardagainsttheft_title_textview_text": "Anti-theft protection", "rn_51001_dashboard_selectedareacuttingunit_textview_text": "m", "rn_51001_cutheight_done_textview_text": "Done", "rn_51001_panelhome_park_textview_text": "Park", "rn_51001_resetrobotpwd_successtip3_textview_text": "If your problem is still there, you can find a dealer.", "rn_51001_panelhome_status20_textview_text": "Returning - <PERSON><PERSON> Completed", "rn_51001_headlight_color_textview_text": "Light colour", "rn_51001_resetrobotpwd_successtip2end_textview_text": "to start your robot.", "rn_51001_cutmode_selectedarea_textview_text": "Selected Area cutting", "rn_51001_geofencesensitivity_setting_textview_text": "Sensitivity setting", "rn_51001_detaillist_remotecontrol_textview_text": "Remote control", "rn_51001_timerange_start_textview_text": "Start", "rn_51001_resetrobotpwd_pwdnotcorrect_textview_text": "The verify code is incorrect", "rn_51001_guardagainsttheft_titlenew_textview_text": "Anti theft setting", "rn_51001_cutmode_cuttingradius_textview_text": "Cutting radius", "rn_51001_panelhome_schedule_button_text": "Schedule", "rn_51001_dashboard_exceededrangewaning_textview_text": "You have exceeded the controllable range of the remote.", "rn_51001_backdistance_cm_textview_text": "Back distance (/cm)", "rn_51001_headlight_red_textview_text": "Red", "rn_51001_resetrobotpwd_failtip1_textview_text": "Sorry, reset password has failed.", "rn_51001_dashboard_modaltitle_textview_text": "Start cutting motor", "rn_51001_headlight_schedule_textview_text": "Light schedule", "rn_51001_resetrobotpwd_unregisteredtip3_textview_text": "then reset the password here.", "rn_51001_statistics_totalstatistics_textview_text": "Total statistics", "rn_51001_headlight_on_textview_text": "Always ON", "rn_51001_modify_addtime_textview_text": "Time period", "rn_51001_cutmode_selectedarearadius_textview_text": "Cutting radius (/m)", "rn_51001_statistics_title_textview_text": "Statistics", "rn_51001_drivepastwire_titleleft_textview_text": "Drive past wire", "rn_51001_panelhome_status46_textview_text": "Charging Completed - <PERSON><PERSON> not eliminated", "rn_51001_dashboard_alertmessage_textview_text": "Do you want to quit this task?", "rn_51001_alarmsetting_alarmenable_textview_text": "Alarm when lifted", "rn_51001_modify_wed_button_text": "WED", "rn_51001_modify_mon_button_text": "MON", "rn_51001_backhomecorridor_title_textview_text": "Back home corridor", "rn_51001_drivepastwire_title_textview_text": "Drive past wire", "rn_51001_modify_friday_textview_text": "Friday", "rn_51001_cutmode_workareanote_textview_text": "Note: The robot will automatically mow the lawn according to the schedule and personalised settings.", "rn_51001_panelhome_updatemap_textview_text": "Reset map", "rn_51001_rainsensor_delay_textview_text": "Delay time (/min)", "rn_51001_cutmode_edgenote_textview_text": "Notice: the robot starts cutting along the boundary wire of the lawn first in every new schedule.", "rn_51001_cutmode_selectedareanote_textview_text": "When selected area cutting is on, the robot will cut the area around the current point within a certain radius.", "rn_51001_headlight_period_textview_text": "Set time period", "rn_51001_dashboard_stopalertmsg_textview_text": "<PERSON><PERSON> is operating. Do you want to stop?", "rn_51001_resetrobotpwd_resettipstart_textview_text": "We have just sent a 6- digit code to your registered robot email address", "rn_51001_alarmsetting_title_textview_text": "Alarm setting", "rn_51001_panelhome_secondarea_textview_text": "Second area", "rn_51001_panelhome_pickercentertitle_textview_text": "hour", "rn_51001_cutmode_sysnote_textview_text": "The robot starts cutting in straight systematic lines.", "rn_51001_panelhome_status60_textview_text": "Mowing - Main Area", "rn_51001_modify_sat_button_text": "SAT", "rn_51001_headlight_flashes_textview_text": "Flashes for a fault alert", "rn_51001_modify_done_button_text": "Done", "rn_51001_resetrobotpwd_unregisteredtip1_textview_text": "Sorry!", "rn_51001_geofencesensitivity_low_textview_text": "Low"}}