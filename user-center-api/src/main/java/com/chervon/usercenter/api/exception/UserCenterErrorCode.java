package com.chervon.usercenter.api.exception;

import com.chervon.common.core.exception.ErrorCodeI;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 系统：用户中心 100
 *
 * <AUTHOR>
 * @date 2022/7/2 17:16
 */
@Getter
@AllArgsConstructor
public enum UserCenterErrorCode implements ErrorCodeI {
    /**
     * 通用模块  001
     * C端用户登录、注册模块  002
     * C端用户同步模块  003
     *
     * Admin用户登录模块  004
     * Admin用户同步模块  005
     *
     * SF同步模块 006
     * fleet模块 007
     */
    /**
     * 用户中心 002
     */
    USER_CENTER_EMAIL_VERIFY_CODE_ERROR("**********", "email verify code error", "email verify code error"),
    USER_CENTER_EMAIL_USED("**********", "Mailbox already exists. Please log in.", "MailBox: [%s] already exists. Please log in."),
    USER_CENTER_ACCOUNT_OR_PASSWORD_ERROR("**********", Messages.ACCOUNT_ERROR, Messages.ACCOUNT_ERROR),
    USER_CENTER_ACCOUNT_DISABLE("**********", "account disable", "account disable, email: %s"),
    USER_CENTER_REGISTER_PASSWORD_INVALID("**********", "invalid register password", "invalid register password"),
    USER_CENTER_USER_NOT_FOUND("**********", "user not found", "user not found, email: [%s]"),
    USER_CENTER_EMAIL_OPERATE_FREQUENTLY("**********", "send email frequently", "send email frequently"),
    USER_CENTER_OLD_PASSWORD_ERROR("**********", "old password error", "old password verification failed"),
    USER_CENTER_PASSWORD_INVALID("**********", "password set invalid", "password length is less than 8-16 characters and does not meet the letter + number regular check"),
    USER_CENTER_EMAIL_CODE_ERROR("**********", "verify code error", "email verify code with redis cache check failed!"),
    USER_CENTER_ACCOUNT_PASSWORD_ERROR("**********", Messages.ACCOUNT_ERROR, Messages.ACCOUNT_ERROR),
    USER_CENTER_MODIFY_PASSWORD_ERROR("**********", "modify password error", "modify password error, new password and old password same"),
    USER_CENTER_RESET_PASSWORD_ERROR("**********", "reset password error", "reset password error"),
    USER_CENTER_OLD_NEW_PASSWORD_NOT_EQUAL("**********", "old password cannot equal new password", "old password cannot equal new password"),
    USER_CENTER_PHONE_INVALID("**********", "phone invalid", "phone regular verification failed"),
    USER_CENTER_OPERATE_FREQUENTLY("**********", "operate frequently", "operate frequently, remind -> key: %s, count: %s"),
    USER_CENTER_SEND_MAIL_ERROR("**********", "send mail failed!", "send mail error, ex: %s"),
    USER_CENTER_USER_IS_EXIST("**********", "user is exist", "user is exist, userId: [%s]"),
    USER_CENTER_USER_NOT_EXIST("**********", "user is not exist", "user is not exist, email|userId: %s"),
    USER_CENTER_OBTAIN_AES_KEY_ERROR("**********","old password error", "failed to obtain AES key: [%s]"),
    USER_CENTER_DECRYPT_OLD_PASSWORD_ERROR("1000022022","old password error", "decryption old password failed"),
    USER_CENTER_CHECK_PASSWORD_ERROR("**********","old password error", "password verification failed"),
    USER_CENTER_EMAIL_CODE_ERROR1("**********", "verification code error", "obtain redis cached verification code failed or email verification code verification failed!"),
    USER_CENTER_OPERATE_FREQUENTLY1("**********", "operate frequently", "operate frequently, interval is less than 1 minute. remind -> key: %s, %s seconds later"),
    USER_CENTER_ACCOUNT_PASSWORD_ERROR1("**********", Messages.ACCOUNT_ERROR, "input email account not exists, email: [%s]"),
    USER_CENTER_ACCOUNT_PASSWORD_ERROR2("**********", Messages.ACCOUNT_ERROR, "password decrypt fail"),
    USER_CENTER_ACCOUNT_PASSWORD_ERROR3("**********", Messages.ACCOUNT_ERROR, "password error"),
    USER_CENTER_READ_NACOS_FAILED("**********", "read nacos error", "read nacos files error, group: %s, dataId: [%s]"),
    USER_CENTER_USER_REGION_ERROR("**********", "Region error. Please use a different email to register.", "Region error. Please use a different email to register."),

    // 其他平台用户首次登录APP相关错误码
    USER_CENTER_NOT_EGO_USER_FIRSTLY_LOGIN("**********", "not ego user firstly login", "not ego user firstly login"),
    USER_CENTER_OTHER_PLATFORM_USER_NOT_EXIST("**********", "This user information has not been synchronized to the current system", "This user information has not been synchronized to the current system, email|userId: %s"),
    USER_CENTER_EGO_USER_ERROR("**********", "ego user does not need to verify!", "ego user does not need to verify!"),


    /**
     * 系统用户 004
     */
    USER_CENTER_SYS_USER_NOT_SYNCHRONIZED("**********", " sysUser not synchronized", " sysUser not synchronized, user: [%s]"),
    USER_CENTER_SYS_USER_TOKEN_NOT_FOUND("1000042002", "sysUser token not found", "sysUser token not found"),
    USER_CENTER_SYS_USER_NOT_FOUND("1000042003", "sysUser not found", "sysUser not found, userId: [%s]"),
    USER_CENTER_ORG_NOT_FOUND("1000042004", "sysUser org not found", "sysUser org not found"),
    USER_CENTER_SYS_USER_LOCKED_THIRTY_MINUTES("1000042005", "sysUser locked 30 minutes", "sysUser locked 30 minutes"),


    /**
     * SF同步模块 006
     */
    USER_CENTER_SALE_FORCE_GET_TOKEN_ERROR("1000062001", "saleForce get token error", "saleForce get token error"),
    USER_CENTER_SALE_FORCE_LIST_KNOWLEDGE_ERROR("1000062002", "saleForce list knowledge error", "saleForce list knowledge error"),
    USER_CENTER_SALE_FORCE_REGISTER_WARRANTY_ERROR("1000062003", "saleForce register warranty error", "saleForce register warranty error, request: %s"),
    USER_CENTER_SALE_FORCE_LIST_PRODUCT_ERROR("1000062004", "saleForce list product error", "saleForce list product error"),
    USER_CENTER_SALE_FORCE_ADD_USER_ERROR("1000062005", "saleForce add user error", "saleForce add user error: %s"),
    USER_CENTER_SALE_FORCE_EDIT_FAIL("1000062006", "saleForce user update fail", "saleForce user update fail, request: %s"),
    USER_CENTER_SALE_FORCE_DELETE_FAIL("1000062007", "saleForce user delete fail", "saleForce user delete fail, request: %s"),
    USER_CENTER_SALE_FORCE_SUBMIT_SURVEY_ERROR("1000062008", "saleForce submit survey error", "saleForce submit survey error: %s"),
    USER_CENTER_SALE_FORCE_LIST_CUSTOMER_PRODUCT_ERROR("1000062009", "saleForce list customer product error", "saleForce list customer product error"),
    USER_CENTER_SALE_FORCE_LIST_PRODUCT_IN_APP_ERROR("1000062010", "saleForce list product in app error", "saleForce list product in app error"),
    USER_CENTER_SALE_FORCE_COMPOSITE_SOBJECTS_DELETE_ERROR("1000062011", "saleForce composite sobjects delete error", "saleForce composite sobjects delete error"),


    /**
     * fleet模块 007
     */
    USER_CENTER_FLEET_COMPANY_ID_NULL("1000072001", "fleet company id is null", "fleet company id is null"),
    USER_CENTER_FLEET_COMPANY_LOGOFF_PASSWORD_BRAND("1000072002", "fleet company logoff password is brand", "fleet company logoff password is brand"),
    USER_CENTER_FLEET_COMPANY_LOGOFF_PASSWORD_ERROR("**********", "fleet company logoff password is error", "fleet company logoff password is error"),
    USER_CENTER_FLEET_COMPANY_USER_ID_NULL("**********", "fleet company user id is null", "fleet company user id is null"),
    ;

    private final String code;

    private final String defaultMessage;

    private final String errorMessage;

    public static class Messages {
        public static final String ACCOUNT_ERROR = "Account or password error";
    }
}
