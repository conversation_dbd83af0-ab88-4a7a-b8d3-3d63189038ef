package com.chervon.usercenter.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class UserVo implements Serializable {

    /**
     * 主键
     */
    private Long id;
    /**
     * firstName
     */
    private String firstName;
    /**
     * lastName
     */
    private String lastName;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 电话号码
     */
    private String phone;

    /**
     * 头像
     */
    private String photo;

    private Integer age;

    private Integer gender;

    private String country;

    private String postCode;

    private String addressLine;

    private String nickName;

    /**
     * 手机系统版本
     **/
    @ApiModelProperty("手机系统版本")
    private String phoneOsVersion;

    /**
     * 手机型号
     **/
    @ApiModelProperty("手机型号")
    private String phoneModel;

    /**
     * APP宿主包版本
     **/
    @ApiModelProperty("APP宿主包版本")
    private String appVersion;

    /**
     * RN包版本
     **/
    @ApiModelProperty("RN包版本")
    private String rnVersion;

    /**
     * 用户来源:sf,ego
     **/
    @ApiModelProperty("用户来源:sf,ego")
    private String userSourceCode;

    /**
     * 适用app类型 1 ego 2 fleet
     */
    private int businessType = 1;

    /**
     * 用户 sfUserId
     */
    private String sfUserId;

    public UserVo(Long userId, Integer age, String nickName) {
        this.setId(userId);
        this.age = age;
        this.nickName = nickName;
    }

    public UserVo() {

    }
}
