package com.chervon.usercenter.api.service;

import com.chervon.usercenter.api.dto.sf.SfSurveySubmitDto;
import com.chervon.usercenter.api.dto.sf.SfUserAddDto;
import com.chervon.usercenter.api.dto.sf.SfUserEditDto;
import com.chervon.usercenter.api.dto.sf.SfWarrantyRegisterDto;
import com.chervon.usercenter.api.vo.sf.*;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-03-08 19:42
 **/
public interface SaleForceService {
    /**
     * 获取SF凭证+其他请求的instance_url
     *
     * @return SfTokenVo
     */
    SfTokenVo getSfToken(Boolean isRefresh);

    SfTokenVo getSfToken();

    /**
     * 增量获取知识库列表（SF删除状态暂时不考虑需SF支持）
     *
     * @return 知识库列表
     */
    List<SfHelpFaqRecord> listKnowledgeKav(LocalDateTime syncTime);

    /**
     * 根据SfUserId获取SF用户信息
     *
     * @param sfUserId 用户SFID
     * @return SF用户详情
     */
    SfUserRecord getSfUserBySfUserId(String sfUserId);

    /**
     * 根据邮箱获取SF用户信息
     *
     * @param email 用户邮箱
     * @return SF用户详情
     */
    SfUserRecord getSfUserBySfEmail(String email);

    /**
     * 获取最近一次从SF同步用户的时间
     * @return 经格式化的时间
     */
    String getUserSyncTime();

    /**
     * 获取最近5分钟更新SF用户列表
     *
     * @return SF用户记录列表
     */
    List<SfUserRecord> listSfUserUpdatedIn5Min();

    /**
     * 获取SF用户列表
     *
     * @param whereStr SQL语句查询条件字符串
     * @return SF用户列表
     */
    List<SfUserRecord> listSfUser(String whereStr);

    /**
     * 创建SF用户
     *
     * @param sfUserAddDto 创建SF用户Dto
     * @return 创建的用户SFID
     */
    String addSfUser(SfUserAddDto sfUserAddDto);

    /**
     * 编辑SF用户
     * @param sfUserId SF用户ID
     * @param sfUserEditDto 编辑SF用户Dto
     */
    void editSfUser(String sfUserId, SfUserEditDto sfUserEditDto);

    /**
     * 删除SF用户
     *
     * @param sfUserId 用户SFID
     */
    void deleteSfUser(@NotEmpty String sfUserId);

    /**
     * 注册设备质保
     *
     * @param sfWarrantyRegisterDto SF设备质保注册Dto
     * @return 注册结果
     */
    SfWarrantyRegisterVo registerSfWarranty(SfWarrantyRegisterDto sfWarrantyRegisterDto);

    /**
     * 获取设备质保信息
     *
     * @param sn 设备sn
     * @return 质保信息
     */
    SfWarrantyInfoVo getSfWarrantyInfo(String sn);

    /**
     * 获取SF产品列表
     *
     * @param productModel         产品Model
     * @param Country_of_Origin__c 产品所属国家(选填：United States/Australia/United Kingdom)
     * @return 产品查询Vo列表
     */
    List<SfProductQueryVo> listSfProduct(String productModel, String Country_of_Origin__c);

    /**
     * 根据设备sn获取维保的邮箱信息
     *
     * @param sn 设备sn
     * @return 邮箱信息
     */
    List<String> getWarrantyEmailBySn(String sn);

    /**
     * 根据设备sn获取SF平台的质保ID
     *
     * @param sn 设备sn
     * @return SF平台的质保ID
     */
    String getSfWarrantyId(@NotEmpty String sn);

    /**
     * 问卷调查数据同步至sf
     *
     * @param sfSurveySubmitDto
     * @return
     */
    void submitSurvey(SfSurveySubmitDto sfSurveySubmitDto);

    /**
     * 查询最近更新的SF设备质保列表
     * @param startTime 查询质保更新的起始时间
     * @return SF设备质保列表
     */
    List<SfWarrantyRecord> listSfWarrantyLastUpdated(Long startTime);

    /**
     * 更新最近一次从SF同步用户的时间，存redis
     * @param time 查询时间
     */
    void updateUserSyncTime(Long time);

    /**
     * 更新最近一次从SF同步质保的时间，存redis
     * @param time 查询时间
     */
    void updateWarrantySyncTime(Long time);

    /**
     * 查询redis最近一次从SF同步质保的时间
     * @return 同步质保时间戳
     */
    Long getWarrantySyncTime();

    /**
     * 批量查询质保
     * @param queryUrl 查询条件
     * @return 质保信息
     */
    SfQueryVo<SfWarrantyRecord> batchSfWarranty(String queryUrl);

    /**
     * 根据SN码查询质保信息
     *
     * @param sn 设备sn
     * @return 质保信息
     */
    List<SfWarrantyRecord> getWarrantyBySn(String sn);

    /**
     * 根据用户查询质保信息
     *
     * @param sfUserId sf平台用户ID
     * @return 质保信息
     */
    List<SfWarrantyRecord> getWarrantyByUser(String sfUserId);
}
