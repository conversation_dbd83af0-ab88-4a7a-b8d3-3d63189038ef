package com.chervon.usercenter.api.service;

import com.chervon.usercenter.api.dto.UserLoginDto;
import com.chervon.usercenter.api.vo.UserVo;
import org.springframework.scheduling.annotation.Async;

import java.util.List;
import java.util.Map;

public interface UserQueryService {

    /**
     * 注册校验有时像是否被使用
     *
     * @param email 邮箱
     * @return 是否被是否 已被使用：true  未被使用：false
     */
    boolean checkEmailUsed(String email);

    /**
     * 用户登录
     *
     * @param dto 操作对象
     * @return 用户基本信息
     */
    UserVo login(UserLoginDto dto);

    /**
     * 获取用户信息
     *
     * @return
     */
    UserVo getUserInfo(String email);

    /**
     * 获取用户信息
     * 支持模糊查询
     *
     * @return
     */
    List<UserVo> getUserInfoByLike(String email);

    /**
     * 根据用户Id获取用户信息
     *
     * @param userId
     * @return
     */
    UserVo getUserInfo(Long userId);

    /**
     * 延长或设置用户RedisAes加密盐
     * 模糊查询firstName like randomUser
     * TODO 压测临时代码,待删除
     */
    @Async
    void renewUserAes();

    /**
     * 根据sf平台的用户Id获取IoT平台的用户ID
     *
     * @param sfUserId sf平台的用户Id
     * @return IoT平台的用户ID
     */
    Long getUserIdBySfUserId(String sfUserId);

    /**
     * 根据userId查询用户信息
     * @param userIds
     * @return
     */
    Map<Long,UserVo> listUserMap(List<Long> userIds);

    /**
     * 根据邮箱查询UserId
     * @param email
     * @return
     */
    Long getUserIdByEmail(String email);
}
