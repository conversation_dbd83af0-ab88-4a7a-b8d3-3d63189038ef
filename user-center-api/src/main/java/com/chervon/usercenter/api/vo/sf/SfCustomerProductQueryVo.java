package com.chervon.usercenter.api.vo.sf;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author：flynn.wang
 * @Date：2024/2/19 14:11
 */
@Data
public class SfCustomerProductQueryVo implements Serializable {
    private Attributes attributes;
    @JsonProperty("Id")
    private String id;

    @Data
    public static class Attributes implements Serializable {
        private String type;
        private String url;
    }
}
