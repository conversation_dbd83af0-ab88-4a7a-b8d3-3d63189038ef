package com.chervon.usercenter.api.dto.enums;

import lombok.Data;

/**
 *
 * 调查问卷基础问答/答案枚举
 *
 *
 * @Author：flynn.wang
 * @Date：2024/2/2 15:03
 */
public enum SfSurveyQuestionAnswerEnum {
    FIRST_NAME("a3C0h000000ye6BEAQ","a3A0h00000303eqEAA",SfSurveyQuestionManualAnswerEnum.Y,SfSurveyQuestionTypeEnum.FREE_TEXT),
    LAST_NAME("a3C0h000000ye6BEAQ","a3A0h00000303erEAA",SfSurveyQuestionManualAnswerEnum.Y,SfSurveyQuestionTypeEnum.FREE_TEXT),
    ADDRESS("a3C0h000000ye6BEAQ","a3A0h00000303esEAA",SfSurveyQuestionManualAnswerEnum.Y,SfSurveyQuestionTypeEnum.FREE_TEXT),
    CITY("a3C0h000000ye6BEAQ","a3A0h00000303etEAA",SfSurveyQuestionManualAnswerEnum.Y,SfSurveyQuestionTypeEnum.FREE_TEXT),
    ZIP_CODE("a3C0h000000ye6BEAQ","a3A0h00000303euEAA",SfSurveyQuestionManualAnswerEnum.Y,SfSurveyQuestionTypeEnum.FREE_TEXT),
    COUNTRY("a3C0h000000ye6BEAQ","a3A0h00000303evEAA",SfSurveyQuestionManualAnswerEnum.Y,SfSurveyQuestionTypeEnum.FREE_TEXT),
    STATE_PROVINCE("a3C0h000000ye6BEAQ","a3A0h00000303ewEAA",SfSurveyQuestionManualAnswerEnum.Y,SfSurveyQuestionTypeEnum.FREE_TEXT),
    PHONE_NUMBER("a3C0h000000ye6BEAQ","a3A0h00000303exEAA",SfSurveyQuestionManualAnswerEnum.Y,SfSurveyQuestionTypeEnum.FREE_TEXT),
    EMAIL_ADDRESS("a3C0h000000ye6BEAQ","a3A0h00000303eyEAA",SfSurveyQuestionManualAnswerEnum.Y,SfSurveyQuestionTypeEnum.FREE_TEXT),
    PRODUCT_INFORMATION("a3C0h000000ye6AEAQ","a3A0h00000303f7EAA",SfSurveyQuestionManualAnswerEnum.N,SfSurveyQuestionTypeEnum.SINGLE_SELECT),
    QUESTION_ONE("a3C0h000000ye6FEAQ",SfSurveyQuestionOneAnswerEnum.ANSWER_7.getSfAnswerId(),SfSurveyQuestionManualAnswerEnum.Y,SfSurveyQuestionTypeEnum.FREE_TEXT),
    QUESTION_TWO("a3C0h000000ye6GEAQ",SfSurveyQuestionTwoAnswerEnum.ANSWER_8.getSfAnswerId(),SfSurveyQuestionManualAnswerEnum.Y,SfSurveyQuestionTypeEnum.FREE_TEXT),
    QUESTION_THREE("a3C0h000000ye6EEAQ",SfSurveyQuestionThreeAnswerEnum.ANSWER_17.getSfAnswerId(),SfSurveyQuestionManualAnswerEnum.Y,SfSurveyQuestionTypeEnum.FREE_TEXT),
    QUESTION_FOUR("a3C0h000000ye6DEAQ",SfSurveyQuestionFourAnswerEnum.ANSWER_9.getSfAnswerId(),SfSurveyQuestionManualAnswerEnum.Y,SfSurveyQuestionTypeEnum.FREE_TEXT),

    ;
    /**
     * 问题id
     */
    private String questionId;

    /**
     * 答案id
     */
    private String sfAnwserId;

    /**
     * 是否有手动填写内容
     */
    private SfSurveyQuestionManualAnswerEnum sfSurveyQuestionManualAnswerEnum;

    /**
     * 问题类型
     */
    private SfSurveyQuestionTypeEnum sfSurveyQuestionTypeEnum;


    SfSurveyQuestionAnswerEnum(String questionId, String sfAnwserId,SfSurveyQuestionManualAnswerEnum sfSurveyQuestionManualAnswerEnum,SfSurveyQuestionTypeEnum sfSurveyQuestionTypeEnum) {
        this.questionId = questionId;
        this.sfAnwserId=sfAnwserId;
        this.sfSurveyQuestionManualAnswerEnum = sfSurveyQuestionManualAnswerEnum;
        this.sfSurveyQuestionTypeEnum=sfSurveyQuestionTypeEnum;
    }


    public String getQuestionId(){
        return this.questionId;
    }


    public String getSfAnwserId() {
        return sfAnwserId;
    }

    public SfSurveyQuestionManualAnswerEnum getSfSurveyQuestionManualAnswerEnum() {
        return sfSurveyQuestionManualAnswerEnum;
    }

    public SfSurveyQuestionTypeEnum getSfSurveyQuestionTypeEnum() {
        return sfSurveyQuestionTypeEnum;
    }
}
