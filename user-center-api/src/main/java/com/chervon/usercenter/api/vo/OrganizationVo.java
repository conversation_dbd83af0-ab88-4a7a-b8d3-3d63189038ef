package com.chervon.usercenter.api.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022-06-20 17:53
 **/
@Data
public class OrganizationVo implements Serializable {
    /**
     * Id
     */
    private Long id;
    /**
     * 组织名称
     */
    private String orgName;
    /**
     * LDAP组织OU
     */
    private String ou;
    /**
     * LDAP组织GUID
     */
    private String ldapOrgGuid;
    /**
     * LDAP父组织GUID
     */
    private String ldapParentOrgGuid;
    /**
     * 创建者
     */
    private Long createBy;
    /**
     * 创建时间
     */
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
//    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private Date createTime;
    /**
     * 更新者
     */
    private Long updateBy;
    /**
     * 更新时间
     */
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
//    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private Date updateTime;
}