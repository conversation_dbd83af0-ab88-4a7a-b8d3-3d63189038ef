package com.chervon.usercenter.api.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class UserRegisterDto implements Serializable {
    /**
     * 用户邮箱
     */
    private String email;
    /**
     * 用户姓
     */
    private String firstName;
    /**
     * 用户名
     */
    private String lastName;
    /**
     * 用户密码
     */
    private String password;

    /**
     * 邮箱验证码
     */
    private String code;

    /**
     * 国家
     */
    private String country;

    /**
     * 城市
     */
    private String city;

    /**
     * 县
     */
    private String county;

    /**
     * 街道
     */
    private String street;

    /**
     * 邮编
     */
    private String postcode;

    /**
     * 手机号
     */
    private String phone;

}
