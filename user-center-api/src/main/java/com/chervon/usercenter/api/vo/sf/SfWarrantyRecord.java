package com.chervon.usercenter.api.vo.sf;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-03-20 09:56
 **/
@Data
public class SfWarrantyRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * Salesforce中客户记录的Id
     */
    @JsonProperty("Consumer__c")
    private String sfUserId;

    /**
     * 质保设备的SN码
     */
    @JsonProperty("Serial_Number__c")
    private String sn;

    /**
     * 设备记录ID
     */
    @JsonProperty("Id")
    private String id;

    /**
     * 发票状态
     */
    @JsonProperty("Receipt_Status__c")
    private String receiptStatus;

    /**
     * 发票图片地址
     */
    @JsonProperty("Receipt_Link__c")
    private String receiptUrl;


    /**
     * 字符串格式传入：yyyy-mm-dd
     */
    @JsonProperty("Purchase_Date__c")
    private String purchaseDate;

    /**
     * 选填：
     * Industrial/Professional/Commercial
     * Residential
     * Rental
     */
    @JsonProperty("Product_Use_Type2__c")
    private String useType;

    /**
     * 购买地址
     */
    @JsonProperty("Purchase_Place__c")
    private String purchasePlace;

    /**
     * 其他购买地址
     */
    @JsonProperty("Place_of_Purchase__c")
    private String purchasePlaceOther;

    /**
     * SF平台质保更新时间
     */
    @JsonProperty("LastModifiedDate")
    private String lastModifiedDate;

    private WAttributes attributes;

    @Data
    public static class WAttributes implements Serializable {
        /**
         * 固定值:Warranty_Item__c
         */
        private String type;

        /**
         * 用户详情地址
         */
        private String url;
    }

}
