package com.chervon.operation.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
* 消息模板查询dto
 **/
@Data
public class MessageTemplateQueryDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;
    /**
     * 模板名称
     */
    @ApiModelProperty("模板名称")
    private String name;
    /**
     * 模板标题
     */
    @ApiModelProperty("模板标题")
    private String title;
    /**
     * 模板类型：0设备消息
     */
    @ApiModelProperty("模板类型：0设备消息")
    private Integer type;

    @ApiModelProperty("语言")
    private String language;
}
