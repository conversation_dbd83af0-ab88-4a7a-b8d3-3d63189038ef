package com.chervon.operation.api.enums;

import java.util.Arrays;
import java.util.Objects;

/**
 * 维保状态类型:
 * 0: 关闭
 * 1: 正常
 * 2: 过期
 */
public enum MaintenanceStatusEnum {

    /**
     * 0: 关闭
     */
    OFF(0, "OFF"),
    /**
     * 1: 正常
     */
    ON(1, "Normal"),
    DUE(2, "Service Due"),
    ;

    /**
     * 类型
     */
    private int type;
    /**
     * 描述,fleet_platform.t_bi_maintenance表中对应字段值
     */
    private String desc;

    MaintenanceStatusEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取描述
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
            .filter(x -> x.getType() == type)
            .map(MaintenanceStatusEnum::getDesc)
            .findFirst()
            .orElse(null);
    }

    /**
     * 获取枚举
     */
    public static MaintenanceStatusEnum getEnum(int type) {
        return Arrays.stream(values())
            .filter(x -> x.getType() == type)
            .findFirst()
            .orElse(null);
    }

    /**
     * 根据数据库值获取对应key
     *
     * @param desc 数据库值
     * @return key
     */
    public static Integer getTypeByDesc(String desc) {
        return Arrays.stream(values())
            .filter(x -> Objects.equals(x.getDesc(), desc))
            .map(MaintenanceStatusEnum::getType)
            .findFirst()
            .orElse(null);
    }

    /**
     * 获取类型
     */
    public int getType() {
        return type;
    }

    /**
     * 获取描述
     */
    public String getDesc() {
        return desc;
    }
}
