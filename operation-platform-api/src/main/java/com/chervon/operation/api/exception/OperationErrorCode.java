package com.chervon.operation.api.exception;

import com.chervon.common.core.exception.ErrorCodeI;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 系统：运营平台 109
 *
 * <AUTHOR>
 * @date 2022/7/15 14:50
 */
@Getter
@AllArgsConstructor
public enum OperationErrorCode implements ErrorCodeI {
    /**
     * 产品模块   001
     * 品牌模块   002
     * 品类模块   003
     * 配件模块   004
     * 分组模块   005
     * 经销商模块 006
     * app协议   007
     * app列表顺序 008
     * app系统消息 009
     * app营销消息 010
     * 引导页模块  011
     * 设备协议模块 012
     * 消息模板    013
     * 通用操作指导  014
     * 通用faq  015
     * 售后内容管理  016
     * 操作指导  017
     * faq  018
     * android version 019
     * 帮助中心faq 020
     * 用户手册 021
     * 链接 022
     * 产品配件 023
     * 处理建议 024
     * fleet模块 025
     */

    /**
     * 产品模块  001
     */
    OPERATION_PRODUCT_NOT_EXIST("1090012001", "product does not exist", "product does not exist, productId: [%s]"),


    OPERATION_DOWNLOAD_PRODUCT_TEMPLATE("1090012007", "download product template error", "download product template error"),
    OPERATION_PRODUCT_READ_EXCEL_ERROR("1090012008", "product read excel error", "product read excel error"),
    OPERATION_PRODUCT_READ_EXCEL_EMPTY("1090012009", "product read excel is empty", "product read excel is empty"),
    OPERATION_PRODUCT_READ_EXCEL_MORE_THAN_5000("1090012010", "product read excel more than 5000", "product read excel more than 5000"),
    OPERATION_PRODUCT_BRAND_NOT_EXIST("1090012013", "product edit brand is not exist", "product edit brand is not exist, brandId: [%s]"),

    /**
     * 品牌模块  002
     */
    OPERATION_BRAND_ALREADY_EXIST("1090022001", "operation brand already exist", "operation brand already exist, brandName: [%s]"),

    OPERATION_BRAND_NOT_EXIST("1090022002", "operation brand not exist", "operation brand not exist, bradId: [%s]"),
    OPERATION_BRAND_ALREADY_BE_USED("1090022003", "this brand is used, cannot deleted", "this brand is used, cannot deleted, brandId: [%s]"),

    /**
     * 品类模块  003
     */
    OPERATION_CATEGORY_ALREADY_EXIST("1090032001", "operation category already exist", "operation category already exist, categoryName: [%s]"),

    OPERATION_CATEGORY_NOT_EXIST("1090032002", "operation category not exist", "operation category not exist, categoryId: [%s]"),
    OPERATION_CATEGORY_ALREADY_BE_USED("1090032003", "this category is used, cannot deleted", "this category is used, cannot deleted, id: [%s]"),

    /**
     * 分组模块  005
     */
    OPERATION_CONDITION_TYPE_ERROR("1090052001", "operation group condition type error", "operation group condition type error"),
    OPERATION_GROUP_NAME_ALREADY_EXISTS("1090052002", "group name already exists", "group name already exists, groupName: [%s]"),
    OPERATION_CONDITION_VALUE_ERROR("1090052003", "operation group condition value error", "operation group condition value must be number"),
    OPERATION_CONDITION_CAN_NOT_BE_EMPTY("1090052004", "operation group condition can not be empty", "operation group condition can not be empty"),

    /**
     * 经销商模块 006
     */
    OPERATION_DEALER_ID_NULL("1090062001", "dealer id is null", "dealer id is null"),
    OPERATION_DEALER_ADD_REQ_NULL("1090062002", "dealer add req is null", "dealer add req is null"),
    OPERATION_DEALER_EDIT_REQ_NULL("1090062003", "dealer edit req is null", "dealer edit req is null"),
    OPERATION_DEALER_NAME_BLANK("1090062004", "dealer name is blank", "dealer name is blank"),
    OPERATION_DEALER_COUNTRY_BLANK("1090062005", "dealer country is blank", "dealer country is blank"),
    OPERATION_DEALER_STATE_BLANK("1090062006", "dealer state is blank", "dealer state is blank"),
    OPERATION_DEALER_CITY_BLANK("1090062007", "dealer city is blank", "dealer city is blank"),
    OPERATION_DEALER_ADDRESS_BLANK("1090062008", "dealer address is blank", "dealer address is blank"),
    OPERATION_DEALER_ZIPCODE_BLANK("1090062009", "dealer zipcode is blank", "dealer zipcode is blank"),
    OPERATION_DEALER_TELEPHONE_BLANK("1090062010", "dealer telephone is blank", "dealer telephone is blank"),
    OPERATION_DEALER_EMAIL_BLANK("1090062011", "dealer email is blank", "dealer email is blank"),
    OPERATION_DEALER_WEBSITE_BLANK("1090062012", "dealer website is blank", "dealer website is blank"),
    OPERATION_DEALER_LAT_BLANK("1090062013", "dealer lat is blank", "dealer lat is blank"),
    OPERATION_DEALER_LNG_BLANK("1090062014", "dealer lng is blank", "dealer lng is blank"),
    OPERATION_DEALER_CATEGORY_BLANK("1090062015", "dealer category is empty", "dealer category is empty"),
    OPERATION_DEALER_NOT_EXIST("1090062016", "dealer is not exist", "dealer is not exist"),
    OPERATION_DEALER_READ_EXCEL_ERROR("1090062017", "dealer read excel error", "dealer read excel error"),
    OPERATION_DEALER_READ_EXCEL_EMPTY("1090062018", "dealer read excel is empty", "dealer read excel is empty"),
    OPERATION_DEALER_READ_EXCEL_UPPER_LIMIT("1090062019", "dealer read excel more than: [%s]", "dealer read excel more than: [%s]"),
    OPERATION_DEALER_LAT_ILLEGAL("1090062020", "dealer lat is illegal", "dealer lat is illegal"),
    OPERATION_DEALER_LNG_ILLEGAL("1090062021", "dealer lng is illegal", "dealer lng is illegal"),

    OPERATION_DEALER_EU_ID_NULL("1090062022", "dealer eu id is null", "dealer eu id is null"),
    OPERATION_DEALER_EU_TITLE_BLANK("1090062023", "dealer eu title is blank", "dealer eu title is blank"),
    OPERATION_DEALER_EU_COUNTRY_BLANK("1090062024", "dealer eu country is blank", "dealer eu country is blank"),
    OPERATION_DEALER_EU_POSTCODE_BLANK("1090062025", "dealer eu postcode is blank", "dealer eu postcode is blank"),
    OPERATION_DEALER_EU_TITLE_EXIST("1090062026", "dealer eu title is exist", "dealer eu title is exist"),
    OPERATION_DEALER_EU_NOT_EXIST("1090062027", "dealer eu is not exist", "dealer eu is not exist"),
    OPERATION_DEALER_EU_BATCH_REMOVE_IDS_EMPTY("1090062028", "dealer eu batch remove ids is empty", "dealer eu batch remove ids is empty"),

    OPERATION_DEALER_IMPORT_HEAD_ERROR("1090062029", "Template error, please check the imported template", "Template error, please check the imported template"),

    /**
     * 配件模块 004
     */
    OPERATION_PARTS_TYPE_ERROR("1090042001", "parts type is error", "parts type is error"),
    OPERATION_PARTS_NAME_NULL("1090042002", "parts name is null", "parts name is null"),
    OPERATION_PARTS_URL_NULL("1090042003", "parts url is null", "parts url is null"),
    OPERATION_PARTS_UPLOAD_ICON_NAME_NULL("1090042004", "parts upload icon name is null", "parts upload icon name is null"),
    OPERATION_PARTS_ID_NULL("1090042005", "parts id is null", "parts id is null"),
    OPERATION_PARTS_NULL("1090042006", "parts is null", "parts is null, partsId: [%s]"),
    OPERATION_PARTS_TYPE1_NULL("1090042007", "parts type1 is null", "parts type1 is null"),
    OPERATION_PARTS_MODEL_ALREADY_EXIST("1090042008", "parts commodityModel already exist", "parts commodityModel already exist, model: [%s]"),
    OPERATION_PARTS_PRODUCT_ALREADY_RELATED("1090042009", "parts existing associated products cannot be deleted", "parts existing associated products cannot be deleted"),
    OPERATION_PARTS_READ_EXCEL_ERROR("1090042010", "parts read excel error", "parts read excel error"),
    OPERATION_PARTS_READ_EXCEL_EMPTY("1090042011", "parts read excel is empty", "parts read excel is empty"),
    OPERATION_PARTS_READ_EXCEL_MORE_THAN_5000("1090042012", "parts read excel more than 5000", "parts read excel more than 5000"),
    OPERATION_PARTS_IMPORT_ERROR("1090042013", "parts import failed", "parts import failed\n:%s"),
    OPERATION_PARTS_EDIT_ID_NULL("1090042014", "parts edit id is null", "parts edit id is null"),
    OPERATION_PARTS_EDIT_ITEM_BLANK("1090042015", "parts edit item is blank", "parts edit item is blank"),
    OPERATION_PARTS_USER_MANUAL_ID_NULL("1090042016", "parts user manual id is null", "parts user manual id is null"),
    OPERATION_PARTS_USER_MANUAL_NULL("1090042017", "parts user manual is null", "parts user manual is null, id: [%s]"),
    OPERATION_PARTS_OPERATION_GUIDANCE_ID_NULL("1090042018", "parts operation guidance id is null", "parts operation guidance id is null"),
    OPERATION_PARTS_OPERATION_GUIDANCE_NULL("1090042019", "parts operation guidance is null", "parts operation guidance is null, id: [%s]"),
    OPERATION_PARTS_OPERATION_GUIDANCE_ORDER_ID_LIST_EMPTY("1090042020", "parts operation guidance order id list is empty", "parts operation guidance order id list is empty"),
    OPERATION_PARTS_FAQ_ID_NULL("1090042021", "parts faq id is null", "parts faq id is null"),
    OPERATION_PARTS_FAQ_NULL("1090042022", "parts faq is null", "parts faq is null, id: [%s]"),
    OPERATION_PARTS_FAQ_ORDER_ID_LIST_EMPTY("1090042023", "parts faq order id list is empty", "parts faq order id list is empty"),
    OPERATION_PARTS_LINK_ID_NULL("1090042024", "parts link id is null", "parts link id is null"),
    OPERATION_PARTS_LINK_NULL("1090042025", "parts link is null", "parts link is null, id: [%s]"),

    /**
     * app协议   007
     */
    OPERATION_APP_AGREEMENT_OPERATE_ILLEGAL("1090072001", "app agreement operate request is illegal", "app agreement operate or state is invalid, state: %s"),

    OPERATION_APP_AGREEMENT_TITLE_NULL("1090072002", "app agreement title is null", "app agreement title is null"),

    OPERATION_APP_AGREEMENT_CONTENT_TEST_GROUP_EMPTY("1090072003", "app agreement content test group is empty", "app agreement content test group is empty"),

    OPERATION_APP_AGREEMENT_ID_NULL("1090072004", "app agreement id is null", "app agreement id is null"),

    OPERATION_APP_AGREEMENT_NULL("1090072005", "app agreement is null", "app agreement is null, id: [%s]"),

    OPERATION_APP_AGREEMENT_OPERATE_REJECTED("1090072006", "app agreement operate request is rejected", "app agreement operate request is rejected, operation: [%s]"),

    OPERATION_APP_AGREEMENT_TYPE_NULL("1090072007", "app agreement type is null", "app agreement type is null"),

    OPERATION_APP_AGREEMENT_CONTENT_VERSION_NULL("1090072008", "app agreement version is null", "app agreement version is null"),

    OPERATION_APP_AGREEMENT_CONTENT_NULL("1090072009", "app agreement content is null", "app agreement content is null, id: [%s]"),

    OPERATION_APP_AGREEMENT_CONTENT_ID_NULL("1090072010", "app agreement content id is null", "app agreement content id is null"),

    OPERATION_APP_AGREEMENT_CONTENT_FROM_ID_NULL("1090072011", "app agreement content from id is null", "app agreement content from id is null"),
    OPERATION_APP_AGREEMENT_TITLE_SAME("1090072012", "app agreement title same", "app agreement title same, titleName: [%s]"),
    OPERATION_APP_AGREEMENT_VERSION_SMALL("1090072013", "app agreement version small", "app agreement version small"),
    OPERATION_APP_AGREEMENT_CAN_NOT_EDIT_TYPE_OR_TITLE("1090072014", "app agreement can not edit type or title", "app agreement can not edit type or title"),
    OPERATION_APP_AGREEMENT_TEST_GROUP_NOT_EXIST("1090072015", "app agreement test group not exist", "app agreement test group [%s] not exist"),
    OPERATION_APP_AGREEMENT_PRD_GROUP_NOT_EXIST("1090072016", "app agreement prd group not exist", "app agreement prd group [%s] not exist"),
    OPERATION_APP_AGREEMENT_BUSINESS_TYPE_ERROR("1090072017", "app agreement business type is null", "app agreement business type is null"),

    OPERATION_APP_AGREEMENT_CONTENT_NULL2("1090072018", "app agreement content is null", "app agreement content is null"),

    /**
     * app列表顺序 008
     */
    OPERATION_APP_LIST_ORDER_ITEM_ILLEGAL("1090082001", "app list order item is illegal", "app list order item is illegal"),

    OPERATION_APP_LIST_ORDER_ITEM_NULL("1090082002", "app list order item is null", "app list order item is null"),
    /**
     * app系统消息 009
     */
    OPERATION_APP_SYS_MSG_OPERATE_ILLEGAL("1090092001", "app sys msg operate request is illegal", "app sys msg operate or state is invalid, code: %s"),

    OPERATION_APP_SYS_MSG_TITLE_NULL("1090092002", "app sys msg title is null", "app sys msg title is null"),

    OPERATION_APP_SYS_MSG_TEST_GROUP_EMPTY("1090092003", "app sys msg test group is empty", "app sys msg test group is empty"),

    OPERATION_APP_SYS_MSG_START_ERROR("1090092004", "app sys msg start error", "app sys msg start error"),

    OPERATION_APP_SYS_MSG_END_ERROR("1090092005", "app sys msg end error", "app sys msg end error"),

    OPERATION_APP_SYS_MSG_END_BEFORE_START("1090092006", "app sys msg end is before start", "app sys msg end is before start"),

    OPERATION_APP_SYS_MSG_PUSH_TYPE_EMPTY("1090092007", "app sys msg push type is empty", "app sys msg push type is empty"),

    OPERATION_APP_SYS_MSG_PUSH_RATE_NULL("1090092008", "app sys msg push rate is null", "app sys msg push rate is null"),

    OPERATION_APP_SYS_MSG_ID_NULL("1090092009", "app sys msg id is null", "app sys msg id is null"),

    OPERATION_APP_SYS_MSG_FROM_ID_NULL("1090092010", "app sys msg from id is null", "app sys msg from id is null"),

    OPERATION_APP_SYS_MSG_NULL("1090092011", "app sys msg is null", "app sys msg is null, id: [%s]"),

    OPERATION_APP_SYS_MSG_OPERATE_REJECTED("1090092012", "app sys msg operate request is rejected", "app sys msg operate request is rejected, operation: [%s]"),
    OPERATION_APP_SYS_MSG_TITLE_EXIST("1090092013", "app sys msg title is exist", "app sys msg title is exist, titleName: [%s]"),

    INVOKE_XXL_GET_EXECUTOR_FAILED("1090092014", "invoke xxl get executor failed", "invoke xxl get executor failed, responseBody: %s"),
    INVOKE_XXL_ADD_EXECUTOR_FAILED("1090092015", "invoke xxl add executor failed", "invoke xxl add executor failed, responseBody: %s"),
    INVOKE_XXL_GET_COOKIE_FAILED("1090092016", "invoke xxl get cookie failed", "invoke xxl get cookie failed, responseBody: %s"),
    OPERATION_MESSAGE_NOT_EXIST("1090092017", "message not exist", "message not exist, id: [%s]"),
    INVOKE_XXL_GET_TASK_FAILED("1090092018", "invoke xxl get task failed", "invoke xxl get task failed, responseBody: %s"),
    INVOKE_XXL_REMOVE_TASK_FAILED("1090092019", "invoke xxl remove task failed", "invoke xxl remove task failed, responseBody: %s"),
    OPERATION_APP_SYS_MSG_TEST_GROUP_NOT_EXIST("1090092020", "app sys msg test group not exist", "app sys msg test group:%s not exist"),
    OPERATION_APP_SYS_MSG_PRD_GROUP_NOT_EXIST("1090092021", "app sys msg prd group not exist", "app sys msg prd group:%s not exist"),
    INVOKE_XXL_STOP_TASK_FAILED("1090092022", "invoke xxl stop task failed", "invoke xxl stop task failed, responseBody: %s"),
    INVOKE_XXL_CREATE_JOB_GROUP_FAILED("1090092023", "invoke xxl create job group failed", "invoke xxl create job group failed, responseBody: %s"),
    INVOKE_XXL_DELETE_JOB_FAILED("1090092024", "invoke xxl delete job failed", "invoke xxl delete job failed, jobId: [%s], responseBody: %s"),
    /**
     * app营销消息 010
     */
    OPERATION_APP_MARKETING_MSG_OPERATE_ILLEGAL("1090102001", "app marketing msg operate request is illegal", "app marketing msg operate or state is invalid, code: %s"),

    OPERATION_APP_MARKETING_MSG_TITLE_NULL("1090102002", "app marketing msg title is null", "app marketing msg title is null"),

    OPERATION_APP_MARKETING_MSG_TEST_GROUP_EMPTY("1090102003", "app marketing msg test group is empty", "app marketing msg test group is empty"),

    OPERATION_APP_MARKETING_MSG_START_ERROR("1090102004", "app marketing msg start error", "app marketing msg start error"),

    OPERATION_APP_MARKETING_MSG_END_ERROR("1090102005", "app marketing msg end error", "app marketing msg end error"),

    OPERATION_APP_MARKETING_MSG_END_BEFORE_START("1090102006", "app marketing msg end is before start", "app marketing msg end is before start"),

    OPERATION_APP_MARKETING_MSG_PUSH_TYPE_EMPTY("1090102007", "app marketing msg push type is empty", "app marketing msg push type is empty"),

    OPERATION_APP_MARKETING_MSG_PUSH_RATE_NULL("1090102008", "app marketing msg push rate is null", "app marketing msg push rate is null"),

    OPERATION_APP_MARKETING_MSG_ID_NULL("1090102009", "app marketing msg id is null", "app marketing msg id is null"),

    OPERATION_APP_MARKETING_MSG_FROM_ID_NULL("1090102010", "app marketing msg from id is null", "app marketing msg from id is null"),

    OPERATION_APP_MARKETING_MSG_NULL("1090102011", "app marketing msg is null", "app marketing msg is null, id: [%s]"),

    OPERATION_APP_MARKETING_MSG_OPERATE_REJECTED("1090102012", "app marketing msg operate request is rejected", "app marketing msg operate request is rejected, operation: [%s]"),
    OPERATION_APP_MARKETING_MSG_TITLE_EXIST("1090102013", "app marketing msg title is exist", "app marketing msg title is exist, titleName: [%s]"),
    OPERATION_APP_MARKETING_MSG_TEST_GROUP_NOT_EXIST("1090102014", "app marketing msg test group not exist", "app marketing msg test group [%s] not exist"),
    OPERATION_APP_MARKETING_MSG_PRD_GROUP_NOT_EXIST("1090102015", "app marketing msg prd group not exist", "app marketing msg prd group [%s] not exist"),
    /**
     * 引导页模块 011
     */
    OPERATION_INTRODUCTION_ONLY_ONE("1090112001", "there can only be one code scanning introduction page", "there can only be one code scanning introduction page"),

    OPERATION_INTRODUCTION_NOT_EXIST("1090112002", "introduction not exist", "introduction not exist, introductionId: [%s]"),
    OPERATION_INTRODUCTION_TYPE_INVALID("1090112003", "introduction type is invalid", "introduction type [%s] is invalid"),
    OPERATION_INTRODUCTION_ICON_TYPE_INVALID("1090112004", "introduction icon type is invalid", "introduction icon type [%s] is invalid"),
    OPERATION_INTRODUCTION_ICON_URL_BLANK("1090112005", "introduction icon url is blank", "introduction icon url is blank"),
    OPERATION_INTRODUCTION_ICON_URL_INVALID("1090112006", "introduction icon url is invalid", "introduction icon url [%s] is invalid"),
    OPERATION_INTRODUCTION_CONTENT_BLANK("1090112007", "introduction content is blank", "introduction content is blank"),
    OPERATION_INTRODUCTION_CAN_NOT_DELETE_ONLY_ONE("1090112008", "introduction can not delete the only one", "introduction can not delete the only one"),
    /**
     * 设备协议模块 012
     */
    OPERATION_DEVICE_AGREEMENT_NOT_EXIST("1090122001", "device agreement not exist", "device agreement not exist, agreementId: [%s]"),

    OPERATION_DEVICE_AGREEMENT_INVALID_VERSION("1090122002", "old device agreement version and new agreement version same", "old and new agreement version same, old: [%s], new: [%s]"),
    OPERATION_DEVICE_AGREEMENT_INVALID_VERSION2("1090122004", "device agreement version exists", "device agreement version exists, agreementId: [%s], agreementVersion: [%s]"),
    OPERATION_DEVICE_AGREEMENT_INVALID_VERSION3("1090122005", "old device agreement version greater than new", "old device agreement version greater than new, old: [%s], new: [%s]"),

    /**
     * 消息模板 013
     */
    MESSAGE_TEMPLATE_NOT_EXIST("1080132001", "message template does not exist", "message template does not exist, templateId: [%s]"),

    MESSAGE_TEMPLATE_BEING_USED("1080132002", "message template being used", "message template being used, usedTimes: [%s]"),

    /**
     * 通用操作指导   014
     */
    OPERATION_COMMON_OPERATION_GUIDANCE_OPERATE_ILLEGAL("1090142001", "common operation guidance operate request is illegal", "common operation guidance operate or state is invalid, code: %s"),
    OPERATION_COMMON_OPERATION_GUIDANCE_NULL("1090142002", "common operation guidance is null", "common operation guidance is null"),
    OPERATION_COMMON_OPERATION_GUIDANCE_TEST_GROUP_NULL("1090142003", "common operation guidance test group is null", "common operation guidance test group is null"),
    OPERATION_COMMON_OPERATION_GUIDANCE_ID_NULL("1090142004", "common operation guidance id is null", "common operation guidance id is null"),
    OPERATION_COMMON_OPERATION_GUIDANCE_OPERATE_REJECTED("1090142005", "common operation guidance operate request is rejected", "common operation guidance operate request is rejected"),
    OPERATION_COMMON_OPERATION_GUIDANCE_READ_EXCEL_ERROR("1090142006", "common operation guidance read excel error", "common operation guidance read excel error"),
    OPERATION_COMMON_OPERATION_GUIDANCE_READ_EXCEL_EMPTY("1090142007", "common operation guidance read excel is empty", "common operation guidance read excel is empty"),
    OPERATION_COMMON_OPERATION_GUIDANCE_READ_EXCEL_MORE_THAN_5000("1090142008", "common operation guidance read excel more than 5000", "common operation guidance read excel more than 5000"),
    OPERATION_COMMON_OPERATION_GUIDANCE_IMPORT_ERROR("1090142009", "common operation guidance import error", "common operation guidance import error %s"),
    OPERATION_COMMON_OPERATION_GUIDANCE_RELATED_PRODUCT_CANNOT_DELETE("1090142010", "common operation guidance cannot delete related product", "common operation guidance cannot delete related product"),
    OPERATION_COMMON_OPERATION_GUIDANCE_TEST_GROUP_NOT_EXIST("1090142011", "common operation guidance test group:%s not exist", "common operation guidance test group:%s not exist"),
    OPERATION_COMMON_OPERATION_GUIDANCE_PRODUCT_ALREDY_BINDED("1090142012", "common operation guidance already bind product", "common operation guidance already bind product,pids:%s "),


    /**
     * 操作指导   017
     */
    OPERATION_OPERATION_GUIDANCE_TYPE_ERROR("1090172001", "operation guidance type is error", "operation guidance type is error"),
    OPERATION_OPERATION_GUIDANCE_NAME_NULL("1090172002", "operation guidance name is null", "operation guidance name is null"),
    OPERATION_OPERATION_GUIDANCE_URL_ILLEGAL("1090172003", "operation guidance url is illegal", "operation guidance url is illegal"),
    OPERATION_OPERATION_GUIDANCE_UPLOAD_FILE_NAME_NULL("1090172004", "operation guidance upload file name is null", "operation guidance upload file name is null"),
    OPERATION_OPERATION_GUIDANCE_ID_NULL("1090172005", "operation guidance id is null", "operation guidance id is null"),
    OPERATION_OPERATION_GUIDANCE_NULL("1090172006", "operation guidance is null", "operation guidance is null, id: [%s]"),

    /**
     * 通用faq   015
     */
    OPERATION_COMMON_FAQ_OPERATE_ILLEGAL("1090152001", "common faq operate request is illegal", "common faq operate or state is invalid, code: %s"),
    OPERATION_COMMON_FAQ_NULL("1090152002", "common faq is null", "common faq is null"),
    OPERATION_COMMON_FAQ_ID_NULL("1090152004", "common faq id is null", "common faq id is null"),
    OPERATION_COMMON_FAQ_OPERATE_REJECTED("1090152005", "common faq operate request is rejected", "common faq operate request is rejected"),
    OPERATION_COMMON_FAQ_READ_EXCEL_ERROR("1090152006", "common faq read excel error", "common faq read excel error"),
    OPERATION_COMMON_FAQ_READ_EXCEL_EMPTY("1090152007", "common faq read excel is empty", "common faq read excel is empty"),
    OPERATION_COMMON_FAQ_READ_EXCEL_MORE_THAN_5000("1090152008", "common faq read excel more than 5000", "common faq read excel more than 5000"),
    OPERATION_COMMON_FAQ_IMPORT_ERROR("1090152009", "common faq import error", "common faq import error\n:%s"),
    OPERATION_COMMON_FAQ_RELATED_PRODUCT_CANNOT_DELETE("1090152010", "common faq cannot delete related product", "common faq cannot delete related product"),
    OPERATION_COMMON_FAQ_PRODUCT_ALREDY_BINDED("1090152011", "common faq already bind product", "already bind product,pids:%s"),

    /**
     * faq   018
     */
    OPERATION_FAQ_TYPE_ERROR("1090182001", "faq type is error", "faq type is null"),
    OPERATION_FAQ_TITLE_NULL("1090182002", "faq title is null", "faq title is null"),
    OPERATION_FAQ_ANSWER_NULL("1090182003", "faq answer is null", "faq answer is null"),
    OPERATION_FAQ_ID_NULL("1090182004", "faq id is null", "faq id is null"),
    OPERATION_FAQ_NULL("1090182005", "faq is null", "faq is null, id: [%s]"),

    /**
     * 售后内容   016
     */
    OPERATION_POST_SALE_USER_MANUAL_ID_NULL("1090162001", "post sale user manual id is null", "post sale user manual id is null"),
    OPERATION_POST_SALE_USER_MANUAL_NULL("1090162002", "post sale user manual is null", "post sale user manual is null, id: [%s]"),
    OPERATION_POST_SALE_OPERATION_GUIDANCE_NULL("1090162004", "post sale operation guidance is null", "post sale operation guidance is null, id: [%s]"),
    OPERATION_POST_SALE_OPERATION_GUIDANCE_ID_NULL("1090162005", "post sale operation guidance id is null", "post sale operation guidance id is null"),
    OPERATION_POST_SALE_OPERATION_GUIDANCE_COMMON_CANNOT_MODIFY("1090162006", "post sale operation guidance common cannot modify", "post sale operation guidance common cannot modify"),
    OPERATION_POST_SALE_OPERATION_GUIDANCE_ORDER_ID_LIST_EMPTY("1090162008", "post sale operation guidance order id list is empty", "post sale operation guidance order id list is empty"),
    OPERATION_POST_SALE_FAQ_NULL("1090162009", "post sale faq is null", "post sale faq is null, id: [%s]"),
    OPERATION_POST_SALE_FAQ_ID_NULL("1090162010", "post sale faq id is null", "post sale faq id is null"),
    OPERATION_POST_SALE_FAQ_COMMON_CANNOT_MODIFY("1090162011", "post sale faq common cannot modify", "post sale faq common cannot modify"),
    OPERATION_POST_SALE_FAQ_ORDER_ID_LIST_EMPTY("1090162013", "post sale faq order id list is empty", "post sale faq order id list is empty"),
    OPERATION_POST_SALE_EDIT_PRODUCT_ID_NULL("1090162014", "post sale edit product id is null", "post sale edit product id is null"),
    OPERATION_POST_SALE_EDIT_ITEM_BLANK("1090162015", "post sale edit item is blank", "post sale edit item is blank"),

    /**
     * android version
     */
    OPERATION_ANDROID_VERSION_ID_NULL("1090192001", "android version id is null", "android version id is null"),
    OPERATION_ANDROID_VERSION_ADD_REQ_NULL("1090192002", "android version add req is null", "android version add req is null"),
    OPERATION_ANDROID_VERSION_ADD_REQ_VERSION_ILLEGAL("1090192003", "android version add req version is illegal", "android version add req version is illegal"),
    OPERATION_ANDROID_VERSION_ADD_REQ_NAME_BLANK("1090192004", "android version add req name is blank", "android version add req name is blank"),
    OPERATION_ANDROID_VERSION_ADD_REQ_UPDATE_CONTENT_BLANK("1090192005", "android version add req update content is blank", "android version add req update content is blank"),
    OPERATION_ANDROID_VERSION_ADD_REQ_VERSION_SMALL("1090192006", "android version add req version is small", "android version add req version is small"),
    OPERATION_ANDROID_VERSION_BUSINESS_TYPE_ERROR("1090192006", "android version business type is error", "android version business type is error"),

    /**
     * 帮助中心faq   020
     */
    OPERATION_HELP_FAQ_NULL("1090202001", "help faq is null", "help faq is null, id: [%s]"),
    OPERATION_HELP_FAQ_TITLE_NULL("1090202002", "help faq title is null", "help faq title is null"),
    OPERATION_HELP_FAQ_ANSWER_NULL("1090202003", "help faq answer is null", "help faq answer is null"),
    OPERATION_HELP_FAQ_MODEL_EMPTY("1090202004", "help faq model is empty", "help faq model is empty"),
    OPERATION_HELP_FAQ_ID_NULL("1090202005", "help faq id is null", "help faq id is null"),
    OPERATION_HELP_FAQ_CANNOT_EDIT("1090202006", "help faq cannot edit or delete", "help faq cannot edit or delete"),
    OPERATION_HELP_FAQ_ALREADY_SHOW("1090202007", "help faq is already show", "help faq is already show"),
    OPERATION_HELP_FAQ_ALREADY_HIDE("1090202008", "help faq is already hidden", "help faq is already hidden"),
    OPERATION_HELP_FAQ_READ_EXCEL_ERROR("1090202009", "help faq read excel error", "help faq read excel error"),
    OPERATION_HELP_FAQ_READ_EXCEL_EMPTY("1090202010", "help faq read excel is empty", "help faq read excel is empty"),
    OPERATION_HELP_FAQ_READ_EXCEL_MORE_THAN_5000("1090202011", "help faq read excel more than 5000", "help faq read excel more than 5000"),
    OPERATION_HELP_FAQ_IMPORT_ERROR("1090202012", "help faq import error", "help faq import error: %s"),
    OPERATION_HELP_FAQ_INCREASING_READ_COUNT_ERROR("1090202013", "help faq increasing read count error", "help faq increasing read count error"),
    OPERATION_HELP_FAQ_UPDATE_PRAISE_COUNT_ERROR("1090202014", "help faq update priase count error", "help faq increasing priase count error"),

    /**
     * 用户手册   021
     */
    OPERATION_USER_MANUAL_TYPE_ERROR("1090212001", "user manual type is error", "user manual type is error, type: [%s]"),
    OPERATION_USER_MANUAL_NAME_NULL("1090212002", "user manual name is null", "user manual name is null"),
    OPERATION_USER_MANUAL_URL_ILLEGAL("1090212003", "user manual url is illegal", "user manual url is illegal, url: [%s]"),
    OPERATION_USER_MANUAL_UPLOAD_FILE_NAME_NULL("1090212004", "user manual upload file name is null", "user manual upload file name is null"),
    OPERATION_USER_MANUAL_ID_NULL("1090212005", "user manual id is null", "user manual id is null"),
    OPERATION_USER_MANUAL_NULL("1090212006", "user manual is null", "user manual is null, id: [%s]"),

    /**
     * 链接   022
     */
    OPERATION_LINK_NULL("1090222001", "link is null", "link is null, id: [%s]"),
    OPERATION_LINK_ID_NULL("1090222002", "link id is null", "link id is null"),
    OPERATION_LINK_URL_NULL("1090222003", "link url is null", "link url is null"),

    /**
     * 产品配件 023
     */
    OPERATION_PRODUCT_PARTS_ADD_PARTS_ID_LIST_EMPTY("1090232001", "product parts add parts id list is empty", "product parts add parts id list is empty"),
    OPERATION_PRODUCT_PARTS_ADD_PARTS_ID_EXIST("1090232002", "product parts add parts id is exist", "product parts add parts id is exist"),
    OPERATION_PRODUCT_PARTS_INSTANCE_ID_NULL("1090232003", "product parts instance id is null", "product parts instance id is null"),
    OPERATION_PRODUCT_PARTS_NULL("1090232004", "product parts is null", "product parts is null, id: [%s]"),
    OPERATION_PRODUCT_PARTS_EDIT_MAINTENANCE_PERIOD_ILLEGAL("1090232005", "product parts edit maintenance period is illegal", "product parts edit maintenance period cannot less than zero"),
    OPERATION_PRODUCT_PARTS_ORDER_PRODUCT_PARTS_ID_LIST_EMPTY("1090232006", "product parts order product parts id list is empty", "product parts order product parts id list is empty"),
    OPERATION_PRODUCT_PARTS_ORDER_ERROR("1090232007", "product parts order error", "product parts order error"),
    OPERATION_PRODUCT_PARTS_ADD_PARTS_PART_ID_NOT_EXIST("1090232008", "product parts add parts part id not exist", "product parts add parts part id not exist, partsId: [%s]"),
    OPERATION_PRODUCT_PARTS_MAINTENANCE_PARAM_ERROR("1090232009", "product parts maintenance param is null", "The maintenance parameters for accessories cannot be empty and should be a positive integer"),
    OPERATION_PRODUCT_PARTS_MAINTENANCE_CYCLE_ERROR("1090232009", "The maintenance cycle should be greater than the reminder cycle", "The maintenance cycle should be greater than the reminder cycle"),
    /**
     * 处理建议   024
     */
    OPERATION_SUGGESTION_NULL("1090242001", "suggestion is null", "suggestion is null, suggestionId: [%s]"),
    OPERATION_SUGGESTION_TITLE_NULL("1090242002", "suggestion title is null", "suggestion title is null"),
    OPERATION_SUGGESTION_CONTENT_NULL("1090242003", "suggestion content is null", "suggestion content is null"),
    OPERATION_SUGGESTION_EXTRA_ILLEGAL("1090242004", "suggestion extra is illegal", "suggestion extra is illegal, extra: [%s]"),
    OPERATION_SUGGESTION_ID_NULL("1090242005", "suggestion id is null", "suggestion id is null"),
    OPERATION_SUGGESTION_CANNOT_DELETE("1090242006", "suggestion cannot delete, used by message template", "suggestion cannot delete, used by message template"),
    OPERATION_SUGGESTION_READ_EXCEL_ERROR("1090242007", "suggestion read excel error", "suggestion read excel error"),
    OPERATION_SUGGESTION_READ_EXCEL_EMPTY("1090242008", "suggestion read excel is empty", "suggestion read excel is empty"),
    OPERATION_SUGGESTION_READ_EXCEL_MORE_THAN_5000("1090242009", "suggestion read excel more than 5000", "suggestion read excel more than 5000"),
    OPERATION_SUGGESTION_IMPORT_ERROR("1090242010", "suggestion import error", "suggestion import error\n:%s"),

    /**
     * fleet模块 025
     */
    OPERATION_FLEET_COMPANY_ID_NULL("1090252001", "fleet company id is null", "fleet company id is null"),
    OPERATION_FLEET_COMPANY_LOGOFF_PASSWORD_BRAND("1090252002", "fleet company logoff password is brand", "fleet company logoff password is brand"),
    OPERATION_FLEET_COMPANY_LOGOFF_PASSWORD_ERROR("1090252003", "fleet company logoff password is error", "fleet company logoff password is error"),
    OPERATION_FLEET_COMPANY_USER_ID_NULL("1090252004", "fleet company user id is null", "fleet company user id is null"),

    /**
     * 运营平台通用异常 026
     */
    OPERATION_PARAM_ERROR("1090262001", "param is null", "param is null"),
    /**
     * 网络文件不存在
     */
    OPERATION_NETWORK_FILE_NOT_EXIST_ERROR("1090262002", "file not exist", "file not exist"),
    //参数未提供通用异常
    PARAMETER_NOT_PROVIDED("1090262003", "Parameter not provided: [%s]", "Parameter not provided: [%s]"),
    ;

    private final String code;

    private final String defaultMessage;

    private final String errorMessage;
}
