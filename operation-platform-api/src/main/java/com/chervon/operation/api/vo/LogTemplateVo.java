package com.chervon.operation.api.vo;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.chervon.common.core.domain.MultiLanguageVo;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 消息模板列表VO
 * <AUTHOR>
 * @since 2024-11-04 10:50
 **/
@Data
public class LogTemplateVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 模板Id
     */
    private Long id;
    /**
     * 模板名称
     */
    @Valid
    private MultiLanguageVo name;
    /**
     * 模板标题
     */
    @Valid
    private MultiLanguageVo title;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建时间
     */
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;
    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 更新时间
     */
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;
}
