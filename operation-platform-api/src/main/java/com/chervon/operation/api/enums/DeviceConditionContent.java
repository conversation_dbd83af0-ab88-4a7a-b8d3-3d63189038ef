package com.chervon.operation.api.enums;

/**
 * 分组设备条件
 * <AUTHOR>
 * @date 12:11 2022/7/26
 **/
public enum DeviceConditionContent {

    /**
     * 产品型号
     **/
    PRODUCT_MODEL("thingTypeName", "P.model"),

    /**
     * 固件版本
     **/
    FIRMWARE_VERSION("attributes.customVersion",
        "D.custom_version"),

    /**
     * 设备id
     **/
    DEVICE_ID("thingName", "AUD.device_id");

    private final String deviceGroupContent;

    private final String userGroupContent;

    DeviceConditionContent(String deviceGroupContent, String userGroupContent) {
        this.deviceGroupContent = deviceGroupContent;
        this.userGroupContent = userGroupContent;
    }

    public String getDeviceGroupContent() {
        return deviceGroupContent;
    }

    public String getUserGroupContent() {
        return userGroupContent;
    }

}
