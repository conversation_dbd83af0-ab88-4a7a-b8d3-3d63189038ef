package com.chervon.operation.api;

import com.chervon.common.core.domain.PageResult;
import com.chervon.operation.api.dto.MessageTemplateDto;
import com.chervon.operation.api.dto.MessageTemplateIdDto;
import com.chervon.operation.api.dto.MessageTemplateListDto;
import com.chervon.operation.api.dto.MessageTemplateQueryDto;
import com.chervon.operation.api.vo.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 远程消息模板Rpc服务
 *
 * <AUTHOR>
 * @since 2022-09-07 19:59
 **/
public interface RemoteMessageTemplateService {
    /**
     * 通过类型获取消息模板列表
     *
     * @param type 消息模板类型(该参数维护在字典中)
     * @return 消息模板Id+标题+内容的列表
     */
    List<MessageTemplateVo> listByType(Integer type);
    /**
     * 批量根据模板id列表获取消息模板列表
     * @return 下拉列表Vo列表
     */
    List<MessageTemplateVo> listByTemplateIds(List<Long> listTemplateId);
    /**
     * 获取消息模板列表
     *
     * @param messageTemplateDto 查询条件
     * @return 消息模板Bo列表
     */
    List<MessageTemplateBo> list(MessageTemplateDto messageTemplateDto);

    /**
     * 获取消息模板
     *
     * @param lang 多语言语种 zh en jp fr
     * @param id   消息模板Id
     * @return 消息模板Bo
     */
    MessageTemplateBo get(String lang, Long id);

    /**
     * 获取消息模板下拉列表
     *
     * @param queryDto 查询条件
     * @return 下拉列表Vo列表
     */
    List<RemoteSpinnerVo> listSpinner(MessageTemplateQueryDto queryDto);

    /**
     * 分页获取消息模板
     *
     * @param messageTemplateListDto 查询条件
     * @return 分页结果
     */
    PageResult<MessageTemplateListVo> page(String lang, MessageTemplateListDto messageTemplateListDto);

    /**
     * 获取消息模板模板
     *
     * @param messageTemplateIdDto 消息模板Id
     * @return 消息模板详情Vo
     */
    MessageTemplateDetailVo detail(String lang, MessageTemplateIdDto messageTemplateIdDto);

    /**
     * 根据模板id获取标题的多语言信息
     *
     * @param templateId 模板id
     * @return 标题多语言信息
     */
    Map<String, Object> fetchTitleInfo(Long templateId);

    /**
     * 根据ID查询消息模板是否有效
     *
     * @param messageTemplateId 消息模板ID
     * @return 消息模板数据是否有效
     */
    Boolean check(Long messageTemplateId);

    /**
     * 根据消息模板ID获取Content
     *
     * @param messageTemplateIds 消息模板ID列表
     * @return key-消息模板ID, value-(key-多语言类型 value-多语言内容)
     */
    Map<Long, Map<String, String>> listContentForProductFault(Set<Long> messageTemplateIds);

    /**
     * 根据消息模板ID获取Title
     *
     * @param messageTemplateIds 消息模板ID列表
     * @return key-消息模板ID, value-(key-多语言类型 value-多语言内容)
     */
    Map<Long, Map<String, String>> listTitleForProductFault(Set<Long> messageTemplateIds);
}
