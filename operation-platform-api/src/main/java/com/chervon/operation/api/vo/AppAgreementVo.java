package com.chervon.operation.api.vo;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/9/2 16:36
 */
@Data
@ApiModel(description = "app协议输出对象")
public class AppAgreementVo implements Serializable {

    @ApiModelProperty(value = "app协议类型 用户协议：user   用户隐私协议：secret")
    private String type;

    @ApiModelProperty(value = "协议版本")
    private String version;

    @ApiModelProperty(value = "协议标题，已经国际化")
    private String title;

    @ApiModelProperty(value = "协议更新时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "协议内容，已经国际化，富文本")
    private String content;
}

