package com.chervon.operation.api.vo;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.chervon.common.core.domain.MultiLanguageVo;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date
 */
@Data
public class CategoryVo implements Serializable {

    /**
     * 品类Id
     */
    @ApiModelProperty("品类Id")
    private Long id;
    /**
     * 品类名字
     */
    @ApiModelProperty("品类名称")
    private MultiLanguageVo categoryName;
    /**
     * 品类图标
     */
    @ApiModelProperty("品类图标")
    private String categoryIcon;
    /**
     * 品类描述
     */
    @ApiModelProperty("品类图标")
    private String description;

    @ApiModelProperty("图片类型：0图片上传，1图片链接")
    private Integer iconType;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("更新者")
    private String updateBy;

    private String categoryLangId;

    @ApiModelProperty("是否在app展示 0：不展示 1：展示 ,默认 0")
    private Integer appShowStatus;

    @ApiModelProperty("app展示序号")
    private Integer appShowOrder;
}
