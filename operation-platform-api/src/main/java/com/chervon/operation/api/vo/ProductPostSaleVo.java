package com.chervon.operation.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/7/12 15:40
 */
@Data
@ApiModel(description = "产品售后数据对象")
public class ProductPostSaleVo implements Serializable {

    @ApiModelProperty(value = "产品技术规格")
    private String technicalSpecification;

    @ApiModelProperty(value = "用户手册")
    private FileVo manual = new FileVo();

}
