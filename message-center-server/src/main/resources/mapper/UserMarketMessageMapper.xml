<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chervon.message.mapper.UserMarketMessageMapper">
    <sql id="tableName">
        t_user_market_message
    </sql>

    <sql id="baseColumn">
        id,title,content,payload_data,user_id,token,device_type,system_message_id,uuid,create_time,if_read,is_deleted,push_type,push_types,push_result
    </sql>

    <select id="countPushResultNum" resultType="com.chervon.operation.api.dto.MessagePushResultCountDto">
        select system_message_id as systemMessageId,
        SUM(CASE WHEN push_result = 1 THEN 1 ELSE 0 END) AS successNum,
        SUM(CASE WHEN push_result = 0 THEN 1 ELSE 0 END) AS failNum
        FROM t_user_market_message where is_deleted = 0
        <if test="countMessageDto.listSystemMessageId != null and countMessageDto.listSystemMessageId.size() > 0">
            and system_message_id in
            <foreach collection="countMessageDto.listSystemMessageId" item="messageId" open="(" separator="," close=")">
                #{messageId}
            </foreach>
        </if>
        <if test="countMessageDto.beginDate != null ">
            and create_time &gt;= #{countMessageDto.beginDate}
        </if>
        <if test="countMessageDto.endDate != null ">
            and create_time &lt;= #{countMessageDto.endDate}
        </if>
        GROUP by system_message_id
    </select>

</mapper>