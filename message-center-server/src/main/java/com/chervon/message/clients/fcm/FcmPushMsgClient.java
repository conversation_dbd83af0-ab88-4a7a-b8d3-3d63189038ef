package com.chervon.message.clients.fcm;

import com.chervon.message.domain.dto.IotPushDto;
import com.chervon.message.domain.entity.PushResult;
import lombok.extern.slf4j.Slf4j;

/**
 * fcm推送
 * <AUTHOR>
 * @date 2022-08-26
 */
@Slf4j
public class FcmPushMsgClient {

    private static final FcmPushMsgClient INSTANCE = new FcmPushMsgClient();

    private FcmPushMsgClient() {
    }

    public static FcmPushMsgClient getInstance() {
        return INSTANCE;
    }

    static FcmSender builder = null;

    static {
        try {
            builder = FcmSender.create();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * send
     *
     * @param messageMongo messageMongo
     * @throws Exception exception
     */
    public PushResult send(IotPushDto messageMongo){
        return builder.send(messageMongo);
    }
}
