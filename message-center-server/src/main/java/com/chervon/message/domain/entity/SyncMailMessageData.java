package com.chervon.message.domain.entity;

import com.chervon.common.core.mail.MailBody;
import lombok.Data;

import java.io.Serializable;

/**
 * 邮件消息发送和存储信息
 * @date 20230222
 * <AUTHOR>
 */
@Data
public class SyncMailMessageData implements Serializable {

    private MailBody mailBody;

    private MessageResult systemStore;

    private MessageResult marketingStore;

    private MessageResult messageResult;

    public SyncMailMessageData (MailBody mailBody, MessageResult systemStore, MessageResult marketingStore,
                                MessageResult messageResult) {
        this.mailBody = mailBody;
        this.systemStore = systemStore;
        this.marketingStore = marketingStore;
        this.messageResult = messageResult;
    }
}
