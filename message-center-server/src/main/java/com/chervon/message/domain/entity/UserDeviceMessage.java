package com.chervon.message.domain.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import java.util.Map;

import com.chervon.message.service.JsonTypeHandler.PayloadHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * (t_user_device_message)实体类
 *
 * <AUTHOR>
 * @since 2024-07-18 17:10:17
 * @description 设备证书关系
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "t_user_device_message",autoResultMap = true)
public class UserDeviceMessage extends Model<UserDeviceMessage> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId
	private Long id;
    /**
     * title
     */
    private String title;
    /**
     * content
     */
    private String content;
    /**
     * payloadData
     */
    @TableField(value="payload_data",typeHandler = PayloadHandler.class)
    private Map<String, String> payloadData;
    /**
     * userId
     */
    private Long userId;
    /**
     * token
     */
    private String token;
    /**
     * deviceType
     */
    private String deviceType;
    /**
     * systemMessageId
     */
    private String systemMessageId;
    /**
     * uuid
     */
    private String uuid;
    /**
     * productId
     */
    private Long productId;
    /**
     * deviceId
     */
    private String deviceId;
    /**
     * createTime
     */
    private Date createTime;
    /**
     * ifRead
     */
    private Integer ifRead;
    /**
     * isDeleted
     */
    private Integer isDeleted;
    /**
     * pushType
     */
    private Integer pushType;
    /**
     * pushTypes
     */
    private String pushTypes;
    /**
     * pushResult
     */
    private Integer pushResult;
    /**
     * 推送失败原因
     */
    private String reason;
    /**
     * app是否显示: 0不展示 1展示
     */
    private Integer appShow;
}