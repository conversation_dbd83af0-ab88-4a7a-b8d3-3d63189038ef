package com.chervon.message.util;

import com.chervon.common.core.utils.StringUtils;
import com.google.common.collect.Lists;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 日期工具类
 *
 * <AUTHOR>
 * @date 2022年12月21日
 */
public class DateToolUtil {

    /**
     * 获取 时间段内的 日期数组
     *
     * @param startTime eg:2022-12-21
     * @param endTime   eg:2022-12-23
     * @return [20221221, 20221222, 20221223]
     */
    public static List<String> getDateList(String startTime, String endTime, String requestType) {
        SimpleDateFormat sdfV3 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdfV2 = new SimpleDateFormat("yyyyMMdd");
        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            if ("1".equals(requestType)) {
                return getDateListByNum(120);
            }
            return getDefaultDateList();
        }

        Date startDate;
        Date endDate;
        try {
            startDate = sdfV3.parse(startTime);
            endDate = sdfV3.parse(endTime);
        } catch (ParseException e) {
            e.printStackTrace();
            startDate = new Date();
            endDate = new Date();
        }
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(endDate);
        Calendar startCalendar = Calendar.getInstance();
        startCalendar.setTime(startDate);
        List<String> result = Lists.newArrayList();
        while (endCalendar.compareTo(startCalendar) >= 0) {
            String format = sdfV2.format(endCalendar.getTime());
            result.add(format);
            endCalendar.add(Calendar.DATE, -1);
        }
        return result;
    }

    /**
     * 没有时间的时候默认最近7天
     *
     * @return
     * @throws ParseException
     */
    public static List<String> getDefaultDateList() {
        return getDateListByNum(30);
    }

    /**
     * 获取日期list 可以指定最近天数
     *
     * @return
     * @throws ParseException
     */
    public static List<String> getDateListByNum(Integer dayNum) {
        SimpleDateFormat sdfV2 = new SimpleDateFormat("yyyyMMdd");
        Date endDate = new Date();
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(endDate);
        List<String> result = Lists.newArrayList();
        for (int i = 0; i < dayNum; i++) {
            String format = sdfV2.format(endCalendar.getTime());
            result.add(format);
            endCalendar.add(Calendar.DATE, -1);
        }
        return result;
    }

    public static void main(String[] args) {
        final LocalDateTime monthBegin = getMonthBegin(5);
        System.out.println(monthBegin);
    }
    public static LocalDateTime getMonthBegin(Integer monthAgo){
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime thisMonthBegin = now.with(TemporalAdjusters.firstDayOfMonth());
        LocalDateTime fourMonthAgo = thisMonthBegin.minusMonths(monthAgo);
        LocalDateTime fourMonthBegin = fourMonthAgo.withHour(0).withMinute(0).withSecond(0).withNano(0);
        return fourMonthBegin;
    }

}
