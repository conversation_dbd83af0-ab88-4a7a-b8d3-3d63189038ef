package com.chervon.message.service.pushHandler;

import com.chervon.common.core.utils.StringUtils;
import com.chervon.message.api.dto.MessageDto;
import com.chervon.message.api.enums.PushTypeHandlerEnum;
import com.chervon.message.api.enums.SwitchFlagEnum;
import com.chervon.message.domain.entity.MessageResult;
import com.chervon.message.domain.entity.PushResult;
import com.chervon.message.service.saveHandler.IMessageHandler;
import com.chervon.message.service.saveHandler.MessageFactory;
import com.chervon.message.util.AwsSNSUtils;
import com.chervon.technology.api.RemoteSmsChargingRecordService;
import com.chervon.technology.api.dto.charging.SmsChargingRecordDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class Push_SmsHandler implements IPushHandler{
    @Autowired
    private AwsSNSUtils awsSNSUtils;
    @DubboReference
    private RemoteSmsChargingRecordService remoteSmsChargingRecordService;
    @Override
    public PushTypeHandlerEnum getPushType() {
        return PushTypeHandlerEnum.MESSAGE;
    }

    @Override
    public List<MessageDto> filter(List<MessageDto> deviceMessages) {
        return deviceMessages.stream().filter(a-> SwitchFlagEnum.OPEN.getType().equals(a.getSnsSwitch())
                && a.getPushTypes().contains(this.getPushType().getPushTypes())
                && (StringUtils.isNotEmpty(a.getPhone()))
        ).collect(Collectors.toList());
    }

    @Override
    public void pushMessage(List<MessageDto> listMessage, Map<String, Object> context) {
        List<SmsChargingRecordDto> listRecords=new ArrayList<>();
        List<MessageResult> resultList=new ArrayList<>(listMessage.size());
        for(MessageDto message: listMessage) {
            // 短信推送
            PushResult sendResult = awsSNSUtils.sendMessage(message.getPhone(), message.getContent());
            if (sendResult.getPushResult()) {
                //构建短信推送计费信息
                MessagePusher.buildSnsRecord(message, listRecords,getPushType().name());
            }
            //构建推送结果消息记录
            MessageResult result = MessagePusher.buildMessageResult(message,sendResult,this.getPushType().getPushTypes());
            resultList.add(result);
        }
        // 短信记录入库charging_sms
        if (!CollectionUtils.isEmpty(listRecords)) {
            remoteSmsChargingRecordService.handleSmsChargingRecords(listRecords);
        }
        //批量记录推送结果
        Integer messageType= listMessage.get(0).getMessageType();
        final IMessageHandler messageInstance = MessageFactory.getMessageInstance(messageType);
        messageInstance.saveMessageResult(resultList);
    }


}
