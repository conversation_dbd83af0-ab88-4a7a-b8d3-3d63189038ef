package com.chervon.message.service.pushHandler;

import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.core.utils.SnowFlake;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.message.api.MessageConstant;
import com.chervon.message.api.dto.MessageDto;
import com.chervon.message.api.enums.*;
import com.chervon.message.domain.entity.MessageResult;
import com.chervon.message.domain.entity.PushResult;
import com.chervon.technology.api.dto.charging.SmsChargingRecordDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 消息推送器
 * <AUTHOR> 2024/9/3
 */
@Slf4j
public class MessagePusher {
    public static void start(List<MessageDto> messageDtos) {
        if(messageDtos.stream().map(a->a.getMessageType()).distinct().count()==1){
            doPushMessage(messageDtos);
        }else{
            //根据消息类型分组推送
            final Map<Integer, List<MessageDto>> groupTypeMap = messageDtos.stream().collect(Collectors.groupingBy(MessageDto::getMessageType));
            for (Map.Entry<Integer, List<MessageDto>> entry : groupTypeMap.entrySet()) {
                doPushMessage(entry.getValue());
            }
        }
    }

    /**
     * 推送消息
     * @param messageDtos
     */
    private static void doPushMessage(List<MessageDto> messageDtos) {
        final List<PushTypeHandlerEnum> statisticActionEnumList = PushFactory.getPushTypeByMessage(messageDtos);
        //执行推送处理
        Map<String,Object> context=new HashMap<>();
        Set<String> appShow=new HashSet<>();
        for(PushTypeHandlerEnum processAction:statisticActionEnumList) {
            final IPushHandler pushHandler = PushFactory.getInstance(processAction);
            if (Objects.isNull(pushHandler)) {
                log.error("pushHandler:{} not found!", processAction);
                continue;
            }
            log.info("pushHandler:{} begin>>>", pushHandler.getPushType().getValue());
            final List<MessageDto> filter = pushHandler.filter(messageDtos);
            if (!CollectionUtils.isEmpty(filter)) {
                filter.forEach(message -> {
                    String key = message.getSystemMessageId() + message.getUserId();
                    if (!appShow.contains(key) && isAppShow(message.getPushTypes())) {
                        message.setAppShow(AppShowEnum.YES.getType());
                        appShow.add(key);
                    }else{
                        message.setAppShow(AppShowEnum.NO.getType());
                    }
                });
                pushHandler.pushMessage(filter, context);
            }
            log.info("pushHandler:{} end<<<", pushHandler.getPushType().getValue());
        }
    }

    private static boolean isAppShow(List<Integer> pushTypes){
        return pushTypes.contains(PushMethodEnum.TOMBSTONE.getPushTypes())
                || pushTypes.contains(PushMethodEnum.POPUP.getPushTypes())
                || pushTypes.contains(PushMethodEnum.BANNER.getPushTypes());
    }

    public static String getUuid(MessageDto messageDto){
        if(StringUtils.isEmpty(messageDto.getUuid())){
            String uuid = String.valueOf(SnowFlake.nextId());
            messageDto.setUuid(uuid);
        }
        return messageDto.getUuid();
    }
    /**
     * 组装消息标题
     * @param message
     * @return
     */
    public static String getMessageTitle(MessageDto message){
        //设备标题：设备昵称：+标题
        return Optional.ofNullable(message.getDeviceNickName()).map(x -> x.concat(": " + message.getTitle())).orElse(message.getTitle());
    }

    /**
     * 构建消息载荷信息体
     * @param message
     * @param uuid
     * @return
     */
    public static Map<String, String> buildMessagePayload(MessageDto message, String uuid) {
        Map<String, String> payloadData = message.getPayloadData();
        if (CollectionUtils.isEmpty(payloadData)) {
            payloadData = new HashMap<>();
        }
        if(message.getMessageType().equals(MessageTypeEnum.DEVICE_MSG.getValue())){
            payloadData.put(MessageConstant.DEVICE_ID, message.getDeviceId());
            payloadData.put(MessageConstant.PRODUCT_ID, message.getProductId());
        }
        payloadData.put(MessageConstant.UUID, uuid);
        payloadData.put(MessageConstant.MESSAGE_TYPE, message.getMessageType().toString());
        payloadData.put(MessageConstant.CREATE_TIME, String.valueOf(new Date().getTime()));
        return payloadData;
    }

    /**
     * 根据消息构建推送结果对象
     * @param messageDto 消息对象
     * @param result 推送结果
     * @param pushType 推送类型
     * @return
     */
    public static MessageResult buildMessageResult(MessageDto messageDto, PushResult result, Integer pushType){
        final MessageResult messageResult = BeanCopyUtils.copy(messageDto, MessageResult.class);
        messageResult.setPushResult(result.getPushResult());
        messageResult.setReason(result.getReason());
        messageResult.setCreateTime(new Date());
        messageResult.setIfRead(ReadFlagEnum.NO.getType());
        messageResult.setPushType(pushType);
        messageResult.setUuid(getUuid(messageDto));
        messageResult.setDeviceType(messageDto.getDeviceType().getName());
        messageResult.setIsDeleted(DeletedFlagEnum.NORMAL.getType());
        if(pushType.equals(PushMethodEnum.MESSAGE.getPushTypes()) ||
                pushType.equals(PushMethodEnum.PHONE_VOICE.getPushTypes())){
            //追加载荷特殊字段
            messageDto.getPayloadData().put("phone", messageDto.getPhone());
        }
        messageResult.setPayloadData(messageDto.getPayloadData());
        return messageResult;
    }

    /**
     * 构建短信记录对象
     * @param message
     * @param listRecords
     */
    public static void buildSnsRecord(MessageDto message, List<SmsChargingRecordDto> listRecords,String pushType) {
        SmsChargingRecordDto record = new SmsChargingRecordDto();
        record.setMsgId(Long.valueOf(message.getSystemMessageId()));
        String uuid = String.valueOf(SnowFlake.nextId());
        record.setMsgRecordId(uuid);
        record.setMsgContent(message.getContent());
        record.setToUserId(message.getUserId());
        record.setToUserPhoneNumber(message.getPhone());
        record.setPushTime(LocalDateTime.now());
        record.setPushType(pushType);
        listRecords.add(record);
    }

}
