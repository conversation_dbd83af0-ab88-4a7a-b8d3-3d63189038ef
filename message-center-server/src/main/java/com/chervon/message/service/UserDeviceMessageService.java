package com.chervon.message.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.message.api.dto.*;
import com.chervon.message.api.vo.MessageVo;
import com.chervon.message.domain.entity.UserDeviceMessage;
import com.chervon.operation.api.dto.MessagePushResultCountDto;
import com.chervon.technology.api.dto.FaultMessageResultCountDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 服务接口
 *
 * <AUTHOR>
 * @since 2024-07-18 17:10:17
 * @description 设备消息服务接口
 */
public interface UserDeviceMessageService extends IService<UserDeviceMessage> {
    /**
     * 获取用户最后一条未读消息
     * @param lastMessage
     * @return
     */
    List<MessageVo> getUserLastMessage(LastMessageDto lastMessage);

    /**
     * 查询设备是否有未读消息
     * @param lastMessage
     * @return
     */
    boolean checkUserUnReadMessage(LastMessageDto lastMessage);

    /**
     * 更新消息为已读
     * @param requestDto
     * @return
     */
    boolean updateMessageRead(MessageEditDto requestDto);

    /**
     * 获取消息分页列表接口
     * @param searchMessageInfo 查询对象
     * @return 分页结果
     */
    PageResult<MessageVo> getPageList(SearchMessageInfoDto searchMessageInfo);
    /**
     * 消息分页搜索列表接口
     * @param searchMessageDto 搜索对象
     * @return 分页结果
     */
    PageResult<MessageVo> SearchPageList(SearchMessageDto searchMessageDto);

    /**
     * 消息搜索列表接口(不分页)
     * @param searchMessageDto 搜索对象
     * @return 列表结果
     */
    List<MessageVo> getList(SearchMessageDto searchMessageDto);
    /**
     * 统计消息推送结果数量
     * @param countMessageDto
     * @return
     */
    List<FaultMessageResultCountDto> countPushResultNum(@Param("countMessageDto") CountMessageDto countMessageDto);
    /**
     * 获取消息详情
     * @param messageDetail 查询对象
     * @return 消息详情结果
     */
    MessageVo getDetail(MessageDetailDto messageDetail);
    /**
     * 删除消息
     * @param messageDetail 请求对象
     * @return
     */
    void delete(MessageDetailDto messageDetail);

    /**
     * （用户注销账号场景：）删除指定用户消息
     * @param userId 用户id
     */
    void deleteByUserId(Long userId, List<DeviceInfoDto> deviceInfoList);
}
