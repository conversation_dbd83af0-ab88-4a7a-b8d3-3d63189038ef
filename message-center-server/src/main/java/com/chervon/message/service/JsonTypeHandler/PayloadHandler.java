package com.chervon.message.service.JsonTypeHandler;

import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.chervon.iot.middle.api.pojo.thingmodel.DataType;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR> 2024/5/21
 * 注意： 加 autoResultMap = true
 * 表名上的注解：@TableName(value = "t_user_system_message",autoResultMap = true)
 */
@MappedTypes({DataType.class})
@MappedJdbcTypes({JdbcType.VARCHAR})
public class PayloadHandler<T> extends AbstractJsonTypeHandler<Object> {
    private static final Logger log = LoggerFactory.getLogger(com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class);
    private static ObjectMapper OBJECT_MAPPER;
    private final Class<T> type;

    public PayloadHandler(Class<T> type) {
        if (log.isTraceEnabled()) {
            log.trace("JacksonTypeHandler(" + type + ")");
        }
        Assert.notNull(type, "Type argument cannot be null", new Object[0]);
        this.type = type;
    }

    protected Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, new TypeReference<Map<String, String>>() {
            });
        } catch (IOException var3) {
            throw new RuntimeException(var3);
        }
    }
    protected String toJson(Object obj) {
        try {
            return getObjectMapper().writeValueAsString(obj);
        } catch (JsonProcessingException var3) {
            throw new RuntimeException(var3);
        }
    }
    public static ObjectMapper getObjectMapper() {
        if (null == OBJECT_MAPPER) {
            OBJECT_MAPPER = new ObjectMapper();
        }

        return OBJECT_MAPPER;
    }
}