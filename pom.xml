<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.chervon.iot</groupId>
    <artifactId>iot-app-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>iot-app</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>

        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <chervon.version>1.1.0-SNAPSHOT</chervon.version>
        <spring-boot.version>2.6.8</spring-boot.version>
        <spring-cloud.version>2021.0.3</spring-cloud.version>
        <hutool.version>5.8.2</hutool.version>
        <user-center.version>1.0.5-SNAPSHOT</user-center.version>
        <iot-platform-api.version>1.0.2-SNAPSHOT</iot-platform-api.version>
        <iot-middle-platform-api.version>1.0.1-SNAPSHOT</iot-middle-platform-api.version>
        <message-center-api.version>1.0.3-SNAPSHOT</message-center-api.version>
        <configuration-center-sdk-language.version>1.0.0-SNAPSHOT</configuration-center-sdk-language.version>
        <configuration-center-api.version>1.0.0-SNAPSHOT</configuration-center-api.version>
        <operation-platform.version>1.0.1-SNAPSHOT</operation-platform.version>
        <iot-feedback.version>1.0.1-SNAPSHOT</iot-feedback.version>
        <logback-core.version>1.2.11</logback-core.version>
        <logstash-logback-encoder.version>4.10</logstash-logback-encoder.version>
        <maven.deploy.skip>false</maven.deploy.skip>
        <satoken.version>1.34.0</satoken.version>
        <xxl-job-core.version>2.3.1</xxl-job-core.version>
        <spring.profiles.active>dev</spring.profiles.active>

    </properties>

    <modules>
        <module>iot-app-api</module>
        <module>iot-app-server</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash-logback-encoder.version}</version>
            </dependency>

            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback-core.version}</version>
            </dependency>
            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- alibaba cloud 的依赖配置-->
            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-alibaba-bom</artifactId>
                <version>${chervon.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- hutool 的依赖配置-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--Project modules-->
            <dependency>
                <groupId>com.chervon.iot</groupId>
                <artifactId>iot-app-api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon.iot</groupId>
                <artifactId>iot-platform-api</artifactId>
                <version>${iot-platform-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon.iot</groupId>
                <artifactId>iot-middle-platform-api</artifactId>
                <version>${iot-middle-platform-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>user-center-api</artifactId>
                <version>${user-center.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>message-center-api</artifactId>
                <version>${message-center-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>operation-platform-api</artifactId>
                <version>${operation-platform.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon.iot</groupId>
                <artifactId>iot-feedback-api</artifactId>
                <version>${iot-feedback.version}</version>
            </dependency>
            <!--Project modules End-->

            <!-- common 的依赖配置-->
            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>chervon-common-bom</artifactId>
                <version>${chervon.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>configuration-center-sdk-language</artifactId>
                <version>${configuration-center-sdk-language.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chervon</groupId>
                <artifactId>configuration-center-api</artifactId>
                <version>${configuration-center-api.version}</version>
            </dependency>

            <!-- Sa-Token 整合redis (使用jackson序列化方式) -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-dao-redis-jackson</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job-core.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <name>User Porject Release</name>
            <url>http://nexus.chervon.com.cn:8081/repository/${spring.profiles.active}-maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>User Porject Snapshot</name>
            <url>http://nexus.chervon.com.cn:8081/repository/${spring.profiles.active}-maven-snapshots/</url>
            <uniqueVersion>true</uniqueVersion>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <configuration>
                        <skip>${maven.deploy.skip}</skip>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>2.6.2</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <repositories>
        <repository>
            <id>maven-releases</id>
            <url>http://nexus.chervon.com.cn:8081/repository/${spring.profiles.active}-maven-releases/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
        </repository>
        <repository>
            <id>maven-snapshots</id>
            <url>http://nexus.chervon.com.cn:8081/repository/${spring.profiles.active}-maven-snapshots/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
        </repository>
    </repositories>
</project>
