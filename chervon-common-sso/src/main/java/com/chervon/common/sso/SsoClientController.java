package com.chervon.common.sso;

import cn.dev33.satoken.config.SaSsoConfig;
import cn.dev33.satoken.sso.SaSsoProcessor;
import cn.dev33.satoken.stp.StpUtil;
import com.chervon.common.core.domain.LoginSysUser;
import com.chervon.common.core.domain.LoginUserContext;
import com.dtflys.forest.Forest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Sa-Token-SSO Client端 Controller
 *
 * <AUTHOR>
 */
@RestController
public class SsoClientController {

    // SSO-Client端：处理所有SSO相关请求
    @RequestMapping("/sso/*")
    public Object ssoRequest() {
        return SaSsoProcessor.instance.clientDister();
    }

    // 重写 /sso/login 增加逻辑：在登录后获取 User 对象缓存到当前Client端的 Session 上 
    @RequestMapping("/sso/login")
    public Object ssoDoLogin(String ticket) {
        Object value = SaSsoProcessor.instance.ssoLogin();
        if (ticket != null && StpUtil.isLogin()) {
            LoginSysUser loginSysUser = SsoMethodUtil.getCurrUser();
            loginSysUser.setAccessToken(StpUtil.getTokenValue());
            loginSysUser.setLoginType("user");
            // 设置login context
            LoginUserContext.setUser(loginSysUser);
            StpUtil.getSession().set(StpUtil.getLoginIdAsString(), loginSysUser);
        }
        return value;
    }

    // 配置SSO相关参数
    @Autowired
    private void configSso(SaSsoConfig sso) {

        // 配置 Http 请求处理器
        sso.setSendHttp(url -> {
            System.out.println("发起请求：" + url);
            return Forest.get(url).executeAsString();
        });
    }

}
