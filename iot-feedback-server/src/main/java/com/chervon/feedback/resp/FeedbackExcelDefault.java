package com.chervon.feedback.resp;

import cn.hutool.core.annotation.Alias;
import com.chervon.common.core.utils.CsvUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/17 20:34
 */
@Data
public class FeedbackExcelDefault implements Serializable {

    private static final long serialVersionUID = -5085516610603105473L;
    @<PERSON>as("Feedback ID")
    private Long feedbackId;

    @<PERSON><PERSON>("User ID")
    private Long userId;

    @Alias("Mailbox")
    private String email;

    @<PERSON>as("Mobile phone model")
    private String commitPhoneModel;

    @Alias("System version number")
    private String commitPhoneOsVersion;

    @Alias("APP type")
    private String commitPhoneType;

    @Alias("App version number")
    private String commitAppVersion;

    @Alias("Question type")
    private String feedbackCategory;

    @<PERSON>as("Type name")
    private String categoryName;

    @Alias("Device SN")
    private String deviceSn;

    @Alias("RN version number")
    private String rnVersion;

    @<PERSON>as("Problem status")
    private String replyState;

    @Alias("Mobile phone number")
    private String areaCodeAndPhone;

    @Alias("Problem description")
    private String feedbackContent;

    @Alias("Feedback pictures")
    private List<String> feedbackPictures = new ArrayList<>();

    @Alias("Feedback time")
    private String feedbackTime;

    @Alias("Customer service remark")
    private String csRemark;

    @Alias("Reply type")
    private String replyType;

    @Alias("Reply content")
    private String replyContent;

    @Alias("Reply pictures")
    private List<String> replyPictures = new ArrayList<>();

    @Alias("Reply time")
    private String replyTime;

    public String getFeedbackId() {
        return CsvUtil.format(this.feedbackId == null ? "" : this.feedbackId + "");
    }

    public String getUserId() {
        return CsvUtil.format(this.userId == null ? "" : this.userId + "");
    }

    public String getCommitPhoneModel() {
        return CsvUtil.format(this.commitPhoneModel);
    }

    public String getCommitPhoneOsVersion() {
        return CsvUtil.format(this.commitPhoneOsVersion);
    }

    public String getCommitPhoneType() {
        return CsvUtil.format(this.commitPhoneType);
    }

    public String getCommitAppVersion() {
        return CsvUtil.format(this.commitAppVersion);
    }

    public String getFeedbackCategory() {
        return CsvUtil.format(this.feedbackCategory);
    }

    public String getCategoryName() {
        return CsvUtil.format(this.categoryName);
    }

    public String getDeviceSn() {
        return CsvUtil.format(this.deviceSn);
    }

    public String getRnVersion() {
        return CsvUtil.format(this.rnVersion);
    }

    public String getReplyState() {
        return CsvUtil.format(this.replyState);
    }

    public String getAreaCodeAndPhone() {
        return CsvUtil.format(this.areaCodeAndPhone);
    }

    public String getFeedbackContent() {
        return CsvUtil.format(this.feedbackContent);
    }

    public String getFeedbackPictures() {
        return CsvUtil.format(this.feedbackPictures);
    }

    public String getFeedbackTime() {
        return CsvUtil.format(this.feedbackTime);
    }

    public String getCsRemark() {
        return CsvUtil.format(this.csRemark);
    }

    public String getReplyType() {
        return CsvUtil.format(this.replyType);
    }

    public String getReplyContent() {
        return CsvUtil.format(this.replyContent);
    }

    public String getReplyPictures() {
        return CsvUtil.format(this.replyPictures);
    }

    public String getReplyTime() {
        return CsvUtil.format(this.replyTime);
    }
}
