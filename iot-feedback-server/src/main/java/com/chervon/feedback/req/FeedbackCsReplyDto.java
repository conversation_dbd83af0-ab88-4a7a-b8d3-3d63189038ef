package com.chervon.feedback.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/17 20:24
 */
@Data
@ApiModel(description = "客服回复内容")
public class FeedbackCsReplyDto implements Serializable {

    @ApiModelProperty("用户反馈id")
    private Long feedbackId;

    @ApiModelProperty("正对回复再次回复的id，如果针对用户反馈进行回复，则可以为空")
    private Long replyId;

    @ApiModelProperty("回复内容")
    private String content;

    @ApiModelProperty("回复提交的图片数组")
    private List<String> pictures = new ArrayList<>();
}
