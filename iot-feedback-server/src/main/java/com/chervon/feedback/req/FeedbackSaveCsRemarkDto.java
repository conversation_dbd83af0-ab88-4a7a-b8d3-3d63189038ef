package com.chervon.feedback.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/17 20:21
 */
@Data
@ApiModel(description = "用户反馈分页查询条件对象")
public class FeedbackSaveCsRemarkDto implements Serializable {

    @ApiModelProperty("用户反馈id")
    private Long feedbackId;

    @ApiModelProperty("客服备注")
    private String csRemark;
}
