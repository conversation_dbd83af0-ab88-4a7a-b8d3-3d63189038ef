package com.chervon.feedback.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/9 19:39
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("feedback_reply")
public class FeedbackReply extends BaseDo implements Serializable {

    /**
     * 用户反馈id
     */
    private Long feedbackId;

    /**
     * 回复类型 user 用户 cs 客服
     */
    private String type;

    /**
     * 针对那个回复进行回复的，可以为空
     */
    private Long replyId;

    /**
     * 回复id的链路，即回复的父子关系，包含祖宗，用英文逗号分割
     */
    private String replyPath;

    /**
     * 回复内容
     */
    private String content;

    /**
     * 回复提交的图片，多个，用[SPLIT]分割
     */
    private String pictures;

}
