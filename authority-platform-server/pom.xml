<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.chervon</groupId>
        <artifactId>authority-platform-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>authority-platform-server</artifactId>
    <name>authority-platform-server</name>
    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <sk.version>8.15.0</sk.version>
    </properties>

    <dependencies>
                <!-- https://mvnrepository.com/artifact/ch.qos.logback/logback-core -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>

        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>authority-platform-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>authority-platform-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>



        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- Chervon Common Log -->
        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-dubbo</artifactId>
        </dependency>



        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-oss</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.springframework.ldap</groupId>-->
<!--            <artifactId>spring-ldap-core</artifactId>-->
<!--        </dependency>-->

        <!--mybatis-datalimit-->
        <dependency>
            <groupId>com.caixunshi</groupId>
            <artifactId>mybatis-datalimit</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>user-center-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>configuration-center-sdk-language</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-swagger</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-sso</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-security</artifactId>
        </dependency>

        <!--skywalking traceId 记录到logback日志，请与安装的服务器版本对应-->
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
            <version>${sk.version}</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
