<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chervon.authority.mapper.PlatformMapper">
    <select id="resourceByPlatformList" resultType="com.chervon.authority.domain.vo.ResourceByPlatformVo">
        SELECT
        a.id as id,
        c.platform_name_lang_code as platformName,
        a.resource_name_lang_code as resourceName
        FROM
        authority_resource a
        INNER JOIN authority_platform c ON c.app_id = a.app_id
        WHERE
        a.is_deleted = 0 and c.is_deleted=0
        AND a.app_id=#{appId}
    </select>
    <select id="roleByResourceIdList" resultType="com.chervon.authority.domain.vo.RoleVo">
        SELECT
            b.id as id,
            b.role_name_lang_code as roleName
        FROM
            authority_role_resource a
                INNER JOIN authority_role b on a.role_id=b.id
        WHERE
            a.is_deleted=0 and b.is_deleted=0
          and a.resource_id = #{resourceId}
    </select>
</mapper>
