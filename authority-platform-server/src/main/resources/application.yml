# Tomcat
server:
  port: 8003

# Spring
spring:
  application:
    # 应用名称
    name: authority-platform
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
--- # nacos 配置
spring:
  cloud:
    nacos:
      # nacos 服务地址
      # server-addr: ${NACOS_SERVER_ADDR}
      discovery:
        # 注册组
        group: IOT_GROUP
        namespace: iot
      config:
        # 配置组
        group: IOT_GROUP
        namespace: iot
  config:
    import:
      - optional:nacos:application-common.yml
      - optional:nacos:${spring.application.name}.yml
