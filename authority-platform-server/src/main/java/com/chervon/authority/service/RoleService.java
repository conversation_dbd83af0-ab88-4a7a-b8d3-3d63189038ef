package com.chervon.authority.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.authority.domain.dto.role.*;
import com.chervon.authority.domain.entity.Role;
import com.chervon.authority.domain.vo.DataRoleDetailVo;
import com.chervon.authority.domain.vo.RoleDetailVo;
import com.chervon.authority.domain.vo.RoleListVo;
import com.chervon.common.core.domain.PageResult;

import java.util.List;

/**
 * <p>
 * 角色信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-27
 */
public interface RoleService extends IService<Role> {

    /**
     * 根据名称模糊查询id
     *
     * @param query:
     * @return java.lang.Long
     * <AUTHOR>
     * @date 18:33 2022/6/27
     **/
    List<Long> selectIdByName(String query);

    /**
     * 添加功能角色
     *
     * @param functionRoleAddDto 添加功能角色Dto
     */
    void addFunctionRole(FunctionRoleAddDto functionRoleAddDto);

    /**
     * 添加数据角色
     *
     * @param roleMetaAddOrEditDto 添加数据角色Dto
     */
    void addDataRole(RoleMetaAddOrEditDto roleMetaAddOrEditDto);

    /**
     * 编辑功能角色
     *
     * @param functionRoleEditDto 修改功能角色dto
     */
    void editFunctionRole(FunctionRoleEditDto functionRoleEditDto);

    /**
     * 编辑数据角色
     *
     * @param roleMetaAddOrEditDto 编辑数据角色Dto
     */
    void editDataRole(RoleMetaAddOrEditDto roleMetaAddOrEditDto);

    /**
     * 通过角色Id获取功能角色详情
     *
     * @param roleId 角色Id
     * @return 功能角色详情Vo
     */
    RoleDetailVo getVoById(Long roleId);

    /**
     * 通过角色Id获取数据角色详情
     *
     * @param roleId 角色Id
     * @return 数据角色详情Vo
     */
    DataRoleDetailVo getDataRoleDetailById(Long roleId);

    /**
     * 分页获取列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    PageResult<RoleListVo> listPage(RoleListDto query);

    /**
     * 删除角色
     *
     * @param roleId 角色id
     */
    void delete(Long roleId);

    /**
     * 修改角色状态
     *
     * @param roleStatusUpdateDto 角色id+状态dto
     */
    void updateStatus(RoleStatusUpdateDto roleStatusUpdateDto);

    /**
     * 根据查询条件返回角色列表
     *
     * @param query:
     * @return com.chervon.authority.domain.vo.RoleListVo
     * <AUTHOR>
     * @date 23:37 2022/6/30
     **/
    List<RoleListVo> listAll(RoleListAllDto query);

    /**
     * 根据角色类型获取id列表
     *
     * @param roleType:
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @date 19:15 2022/7/1
     **/
    List<Long> listIdByType(Integer roleType);

    /**
     * 根据角色id获取平台id
     *
     * @param roleId:
     * @return java.lang.Long
     * <AUTHOR>
     * @date 9:26 2022/7/2
     **/
    Long getPlatformByRoleId(Long roleId);

}
