package com.chervon.authority.service;

import com.chervon.usercenter.api.vo.SysUserVo;

public interface RpcService {

    /**
     * 根据组织guid获取组织名称
     *
     * @param guid:
     * @return java.lang.String
     * <AUTHOR>
     * @date 17:34 2022/7/25
     **/
    String getOrgNameByGuid(String guid);

    /**
     * 根据用户guid获取用户信息
     *
     * @param guid:
     * @return com.chervon.usercenter.api.vo.SysUserVo
     * <AUTHOR>
     * @date 17:34 2022/7/25
     **/
    SysUserVo getUserByGuid(String guid);
}
