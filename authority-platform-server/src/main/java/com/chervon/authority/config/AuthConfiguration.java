package com.chervon.authority.config;

import com.chervon.authority.service.ResourceService;
import com.chervon.common.security.config.CheckPermissionProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @date 2023/2/21 0:00
 */
@Configuration
public class AuthConfiguration implements WebMvcConfigurer {

    public AuthConfiguration(ResourceService resourceService) {
        this.resourceService = resourceService;
    }

    @Bean
    public CheckPermissionProperties getCheckPermissionProperties() {
        return new CheckPermissionProperties();
    }

    private final ResourceService resourceService;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        InterceptorRegistration interceptorRegistration = registry
                .addInterceptor(new AuthPermissionInterceptor(getCheckPermissionProperties(), resourceService))
                .order(20).addPathPatterns("/**");
        interceptorRegistration.excludePathPatterns("/sso/**");
        if (getCheckPermissionProperties().getExcludeUrls() != null) {
            interceptorRegistration.excludePathPatterns(getCheckPermissionProperties().getExcludeUrls());
        }
    }
}
