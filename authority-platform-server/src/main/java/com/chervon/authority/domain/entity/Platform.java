package com.chervon.authority.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.chervon.common.mybatis.config.BaseDo;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 平台表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-27
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("authority_platform")
public class Platform extends BaseDo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 系统ID
     **/
    private String appId;

    /**
     * 平台的密钥，单点登录所用
     **/
    private String appSecret;

    /**
     * 平台名称，系统名称
     **/
    private String platformName;

    private Long platformNameLangId;

    private String platformNameLangCode;

    /**
     * 系统URL
     **/
    private String baseUrl;

    /**
     * 是否启用:1启用，0不启用
     **/
    private Integer platformStatus;

    /**
     * 描述
     **/
    private String description;

    /**
     * 排序
     **/
    private Integer orderNum;

    /**
     * 平台图标
     **/
    private String icon;
}
