package com.chervon.authority.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 添加审计日志Dto
 *
 * <AUTHOR>
 * @since 2022-09-03 10:38
 **/
@Data
public class AuditLogAddDto implements Serializable {
    /**
     * 浏览器类型
     */
    @ApiModelProperty("浏览器类型")
    private String browser;
    /**
     * 设备Mac
     */
    @ApiModelProperty("设备Mac")
    private String deviceMac;
    /**
     * 操作类型
     */
    @ApiModelProperty("操作类型")
    private String operation;
    /**
     * 操作模块
     */
    @ApiModelProperty("操作模块")
    private String operationModel;
    /**
     * 操作内容
     */
    @ApiModelProperty("操作内容")
    private String operationContent;
    /**
     * 入参
     */
    @ApiModelProperty("入参")
    private String input;
    /**
     * 出参
     */
    @ApiModelProperty("出参")
    private String output;
    /**
     * 操作结果: 0失败 1成功
     */
    @ApiModelProperty("操作结果: 0失败 1成功")
    private Integer operationResult;
    /**
     * 操作时间(Long型时间戳)
     */
    @ApiModelProperty("操作时间(Long型时间戳)")
    private Long operationTime;
}
