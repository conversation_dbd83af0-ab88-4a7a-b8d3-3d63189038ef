package com.chervon.authority.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2022-09-03 10:26
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("audit_log")
public class AuditLog extends BaseDo {
    /**
     * 用户Id(内部用户)
     */
    @ApiModelProperty("用户Id(内部用户)")
    private Long userId;
    /**
     * 用户名
     */
    @ApiModelProperty("用户名")
    private String userName;
    /**
     * 浏览器类型
     */
    @ApiModelProperty("浏览器类型")
    private String browser;
    /**
     * 设备Mac
     */
    @ApiModelProperty("设备Mac")
    private String deviceMac;
    /**
     * ip
     */
    @ApiModelProperty("ip")
    private String ip;
    /**
     * 操作类型
     */
    @ApiModelProperty("操作类型")
    private String operation;
    /**
     * 操作模块
     */
    @ApiModelProperty("操作模块")
    private String operationModel;
    /**
     * 操作内容
     */
    @ApiModelProperty("操作内容")
    private String operationContent;
    /**
     * 入参
     */
    @ApiModelProperty("入参")
    private String input;
    /**
     * 出参
     */
    @ApiModelProperty("出参")
    private String output;
    /**
     * 操作结果: 0失败 1成功
     */
    @ApiModelProperty("操作结果: 0失败 1成功")
    private Integer operationResult;
    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间")
    private LocalDateTime operationTime;
}
