package com.chervon.authority.domain.dto;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-09-03 11:26
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class AuditLogListDto extends PageRequest implements Serializable {
    /**
     * 用户Id
     */
    @ApiModelProperty("用户Id")
    private String userId;
    /**
     * 用户名
     */
    @ApiModelProperty("用户名")
    private String userName;
    /**
     * 操作类型
     */
    @ApiModelProperty("操作类型")
    private String operation;
    /**
     * 操作模块
     */
    @ApiModelProperty("操作模块")
    private String operationModel;
}
