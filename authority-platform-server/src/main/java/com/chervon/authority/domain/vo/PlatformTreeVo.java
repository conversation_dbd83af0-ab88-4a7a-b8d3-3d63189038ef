package com.chervon.authority.domain.vo;


import com.chervon.authority.domain.entity.Platform;
import com.chervon.authority.domain.entity.Resource;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022-06-22
 */
@Data
@AllArgsConstructor
public class PlatformTreeVo {

    /**
     * 平台信息
     */
    private Platform platform;

    /**
     * 资源信息
     */
    private List<ResourceVo> resources;

}
