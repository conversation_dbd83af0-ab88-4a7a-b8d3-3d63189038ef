package com.chervon.authority.domain.dto.role;

import lombok.Data;

/**
 * <AUTHOR>
 * @className UserRoleQuery
 * @description
 * @date 2022/6/17 9:55
 */
@Data
public class UserRolePageDto {
    /**
     * 当前页
     * <AUTHOR>
     * @date 9:55 2022/6/17
     **/
    private Long pageNum;
    /**
     * 每页的数量
     * <AUTHOR>
     * @date 9:55 2022/6/17
     **/
    private Long pageSize;
    /**
     * 角色名称或ID
     * <AUTHOR>
     * @date 9:55 2022/6/17
     **/
    private String query;

    /**
     * 角色类型 0功能角色 1数据角色 null不过滤
     * <AUTHOR>
     * @date 9:58 2022/6/17
     **/
    private Integer roleType;

}
