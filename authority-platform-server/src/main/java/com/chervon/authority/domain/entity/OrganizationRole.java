package com.chervon.authority.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.chervon.common.mybatis.config.BaseDo;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户和角色关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-27
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("authority_organization_role")
public class OrganizationRole extends BaseDo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     **/
    private Long userId;

    /**
     * 角色ID
     **/
    private Long roleId;

    /**
     * 描述
     **/
    private String description;
}
