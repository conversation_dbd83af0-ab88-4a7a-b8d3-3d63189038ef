package com.chervon.authority.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.chervon.common.mybatis.config.BaseDo;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 角色和数据权限关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-27
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("authority_role_resource")
public class RoleResource extends BaseDo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 角色ID
     **/
    private Long roleId;

    /**
     * 资源ID
     **/
    private Long resourceId;
}
