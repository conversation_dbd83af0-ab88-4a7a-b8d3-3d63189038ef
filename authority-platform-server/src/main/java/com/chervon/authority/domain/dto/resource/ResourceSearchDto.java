package com.chervon.authority.domain.dto.resource;

import com.chervon.common.core.domain.PageRequest;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022-05-05 10:22
 **/
@Data
public class ResourceSearchDto implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    /**
     * 系统名称、名称、元素ID、URL
     */
    private String name;

    /**
     * 资源类型(M菜单 P页面 B按钮 R请求)
     */
    private String resourceType;
    /**
     * 系统ID
     **/
    private String appId;
    /**
     * 元素ID
     **/
    private String elementId;
    /**
     * 地址
     */
    private String path;
    /**
     * 父id
     */
    private Long parentId;

}
