package com.chervon.authority.domain.dto.platform;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 创建平台
 *
 * <AUTHOR>
 * @date 2022-06-20
 */
@Data
public class PlatformEditDto implements Serializable {

    @NotNull
    private Long id;

    @NotEmpty
    private String platformName;

    @NotEmpty
    private String appId;

    @NotEmpty
    private String baseUrl;

    private String description;

    /**
     * 图标
     */
    private String icon;

}
