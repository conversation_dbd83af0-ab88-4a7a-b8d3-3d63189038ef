package com.chervon.authority.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.authority.domain.entity.Resource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 资源信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-27
 */
@Mapper
public interface ResourceMapper extends BaseMapper<Resource> {

    /**
     * 获取最大的排序号
     *
     * @param appId    平台appId
     * @param parentId 父级资源Id
     * @return 最大排序数
     */
    Integer getResourceMaxOrderNum(@Param("appId") String appId, @Param("parentId") Long parentId);

    /**
     * 根据角色Id获取资源列表
     *
     * @param roleIds       角色Ids
     * @param resourceTypes 需要获取的资源类型
     * @param appId         平台Id
     * @return 资源聚合根列表
     */
    List<Resource> getResourceIdByRoleIds(@Param("roleIds") List<Long> roleIds,
                                          @Param("resourceTypes") List<String> resourceTypes,
                                          @Param("appId") String appId);

    List<String> getRequestResource(@Param("guids") List<String> guids, @Param("appId") String appId);

}
