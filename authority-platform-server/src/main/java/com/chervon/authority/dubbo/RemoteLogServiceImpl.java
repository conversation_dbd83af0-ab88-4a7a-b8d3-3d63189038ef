package com.chervon.authority.dubbo;

import com.chervon.authority.api.core.SysLogDto;
import com.chervon.authority.api.service.RemoteLogService;
import com.chervon.authority.domain.entity.AuditLog;
import com.chervon.authority.service.AuditLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2022/12/27 15:23
 */
@DubboService
@Slf4j
public class RemoteLogServiceImpl implements RemoteLogService {

    @Autowired
    private AuditLogService auditLogService;

    @Override
    public void saveLog(SysLogDto sysLog) {
        AuditLog log = new AuditLog();
        BeanUtils.copyProperties(sysLog, log);
        auditLogService.save(log);
    }
}
