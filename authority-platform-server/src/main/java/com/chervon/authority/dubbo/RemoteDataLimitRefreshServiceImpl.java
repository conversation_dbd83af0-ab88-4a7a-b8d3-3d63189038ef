package com.chervon.authority.dubbo;

import com.caixunshi.datalimit.config.DataLimitConfig;
import com.chervon.authority.api.service.RemoteDataLimitRefreshService;
import org.apache.dubbo.common.constants.ClusterRules;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * <AUTHOR>
 * @date 2023/4/27 11:32
 */
@DubboService(cluster = ClusterRules.BROADCAST)
public class RemoteDataLimitRefreshServiceImpl implements RemoteDataLimitRefreshService {

    @Override
    public void refresh() {
        DataLimitConfig.refreshConfig();
    }
}
