package com.chervon.authority.dubbo;


import com.chervon.authority.api.service.RemoteSysUserRoleService;
import com.chervon.authority.service.UserRoleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2023/4/16 17:39
 */
@DubboService
@Slf4j
@Service
public class RemoteSysUserRoleServiceImpl implements RemoteSysUserRoleService {

    @Autowired
    private UserRoleService userRoleService;

    @Override
    public List<String> getSysUserRoleByUserGuid(String lang, String userGuid) {
        LocaleContextHolder.setLocale(new Locale(lang));
        return userRoleService.getRoleNames(userGuid);
    }
}
