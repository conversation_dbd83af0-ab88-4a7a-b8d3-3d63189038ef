package com.chervon.authority.controller;

import com.chervon.authority.api.core.ResourceTreeElementVo;
import com.chervon.authority.domain.dto.resource.ResourceAddDto;
import com.chervon.authority.domain.dto.resource.ResourceEditDto;
import com.chervon.authority.domain.dto.resource.ResourceSearchDto;
import com.chervon.authority.domain.dto.resource.ResourceSortDto;
import com.chervon.authority.domain.vo.ResourceDetailVo;
import com.chervon.authority.domain.vo.ResourcePageVo;
import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.authority.service.ResourceService;
import com.chervon.common.core.domain.ListInfoReq;
import com.chervon.common.core.domain.SingleInfoReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-05-06 18:00
 */
@RestController
@RequestMapping("/resource")
@Slf4j
@Api(tags = "资源相关接口")
public class ResourceController {

    @Autowired
    ResourceService resourceService;

    /**
     * 获取当前登录用户有权限资源树
     *
     * @return List<ResourceTreeElementVo> 资源树List
     */
    @ApiOperation("获取当前登录用户有权限资源树")
    @PostMapping("/list")
    @Log(businessType = BusinessType.VIEW)
    public List<ResourceTreeElementVo> list(@RequestBody SingleInfoReq<String> appIdReq) {
        return resourceService.listTree(appIdReq.getReq());
    }

    /**
     * 获取当前登录用户有权限的请求R的路径
     *
     * @return List<ResourceTreeElementVo> 资源树List
     */
    @ApiOperation("获取当前登录用户有权限的请求R的路径")
    @PostMapping("/own/request/list")
    @Log(businessType = BusinessType.VIEW)
    public List<String> requestList(@RequestHeader("AppId") String appId, @RequestHeader("EmployeeNumber") String employeeNumber) {
        return resourceService.getRequestPermissions(appId, employeeNumber);
    }

    /**
     * 根据搜索条件获取资源列表
     *
     * @param query 搜索条件
     * @return 资源Vo列表
     */
    @ApiOperation("根据搜索条件获取资源列表")
    @PostMapping("/search/list")
    @Log(businessType = BusinessType.VIEW)
    public List<ResourcePageVo> searchList(@Validated @RequestBody ResourceSearchDto query) {
        return resourceService.searchList(query);
    }

    /**
     * 新增资源
     *
     * @param resourceAddDto 创建资源信息
     */
    @ApiOperation("新增资源")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Log(businessType = BusinessType.INSERT)
    public void addResource(@Validated @RequestBody ResourceAddDto resourceAddDto) {
        resourceService.addResource(resourceAddDto);
    }

    /**
     * 编辑资源
     *
     * @param resourceEditDto 更新资源信息
     */
    @ApiOperation("编辑资源")
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @Log(businessType = BusinessType.EDIT)
    public void editResource(@Validated @RequestBody ResourceEditDto resourceEditDto) {
        resourceService.editResource(resourceEditDto);
    }

    /**
     * 删除资源
     *
     * @param req 资源id
     */
    @ApiOperation("删除资源")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @Log(businessType = BusinessType.DELETE)
    public void delete(@Validated @RequestBody ListInfoReq<Long> req) {
        resourceService.deleteResource(req.getInfo());
    }

    /**
     * 停用资源
     *
     * @param req 资源Id
     */
    @ApiOperation("停用资源")
    @RequestMapping(value = "/stop", method = RequestMethod.POST)
    @Log(businessType = BusinessType.DEACTIVATE)
    public void stop(@Validated @RequestBody SingleInfoReq<Long> req) {
        resourceService.stop(req.getReq());
    }

    /**
     * 启用资源
     *
     * @param req 资源Id
     */
    @ApiOperation("启用资源")
    @RequestMapping(value = "/start", method = RequestMethod.POST)
    @Log(businessType = BusinessType.ENABLE)
    public void start(@Validated @RequestBody SingleInfoReq<Long> req) {
        resourceService.start(req.getReq());
    }

    /**
     * 资源详情
     *
     * @param req 系统AppId
     * @return 返回信息
     */
    @ApiOperation("资源详情")
    @RequestMapping(value = "/platform/resource", method = RequestMethod.POST)
    @Log(businessType = BusinessType.VIEW)
    public List<ResourceDetailVo> getListByAppId(@Validated @RequestBody SingleInfoReq<String> req) {
        return resourceService.getListByAppId(req);
    }

    /**
     * 调整资源顺序
     *
     * @param command 排序信息
     */
    @ApiOperation("调整资源顺序")
    @RequestMapping(value = "/sort", method = RequestMethod.POST)
    @Log(businessType = BusinessType.OTHER)
    public void sort(@Validated @RequestBody ResourceSortDto command) {
        resourceService.sort(command);
    }

}
