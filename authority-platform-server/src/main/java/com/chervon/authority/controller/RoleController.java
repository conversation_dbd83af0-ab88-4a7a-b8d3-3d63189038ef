package com.chervon.authority.controller;

import com.chervon.authority.domain.dto.role.*;
import com.chervon.authority.domain.vo.DataRoleDetailVo;
import com.chervon.authority.domain.vo.RoleDetailVo;
import com.chervon.authority.domain.vo.RoleListVo;
import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.authority.service.RoleService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.SingleInfoReq;
import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @since 2022-05-06 18:05
 */
@RestController
@RequestMapping("/role")
@Api(tags = "角色相关接口")
public class RoleController {
    @Autowired
    private RoleService roleService;

    /**
     * 新增功能角色
     *
     * @param functionRoleAddDto 功能角色添加command
     */
    @ApiOperation("新增功能角色")
    @Log(businessType = BusinessType.INSERT)
    @PostMapping("/function/add")
    public void functionRoleAdd(@Validated @RequestBody FunctionRoleAddDto functionRoleAddDto) {
        roleService.addFunctionRole(functionRoleAddDto);
    }

    /**
     * 新增数据角色
     *
     * @param roleMetaAddOrEditDto 数据角色添加
     */
    @ApiOperation("新增数据角色")
    @Log(businessType = BusinessType.INSERT)
    @PostMapping("/data/add")
    public void dataRoleAdd(@Validated @RequestBody RoleMetaAddOrEditDto roleMetaAddOrEditDto) {
        roleService.addDataRole(roleMetaAddOrEditDto);
    }

    /**
     * 编辑数据角色
     *
     * @param roleMetaAddOrEditDto 数据角色修改
     */
    @ApiOperation("编辑数据角色")
    @Log(businessType = BusinessType.EDIT)
    @PostMapping("/data/edit")
    public void dataRoleEdit(@Validated @RequestBody RoleMetaAddOrEditDto roleMetaAddOrEditDto) {
        roleService.editDataRole(roleMetaAddOrEditDto);
    }

    /**
     * 功能角色详情
     *
     * @param roleId 角色Id
     * @return 角色Vo
     */
    @ApiOperation("功能角色详情")
    @Log(businessType = BusinessType.VIEW)
    @PostMapping("/function/get")
    public RoleDetailVo getById(@Validated @RequestBody SingleInfoReq<Long> roleId) {
        return roleService.getVoById(roleId.getReq());
    }

    /**
     * 数据角色详情
     *
     * @param roleId 角色Id
     * @return 角色Vo
     */
    @ApiOperation("数据角色详情")
    @Log(businessType = BusinessType.VIEW)
    @PostMapping("/data/get")
    public DataRoleDetailVo getDataRoleDetailById(@Validated @RequestBody SingleInfoReq<Long> roleId) {
        return roleService.getDataRoleDetailById(roleId.getReq());
    }

    /**
     * 获取角色分页
     *
     * @param query 角色查询条件
     * @return 分页结果
     */
    @ApiOperation("获取角色分页")
    @Log(businessType = BusinessType.VIEW)
    @PostMapping("/list")
    public PageResult<RoleListVo> list(@Validated @RequestBody RoleListDto query) {
        return roleService.listPage(query);
    }

    /**
     * 获取角色列表
     *
     * @param query 角色查询条件
     * @return 分页结果
     */
    @ApiOperation("获取角色列表")
    @Log(businessType = BusinessType.VIEW)
    @PostMapping("/list/all")
    public List<RoleListVo> listAll(@Validated @RequestBody RoleListAllDto query) {
        return roleService.listAll(query);
    }

    /**
     * 编辑功能角色
     *
     * @param functionRoleEditDto 角色更新command
     */
    @ApiOperation("编辑功能角色")
    @Log(businessType = BusinessType.EDIT)
    @PostMapping("/function/edit")
    public void edit(@Validated @RequestBody FunctionRoleEditDto functionRoleEditDto) {
        roleService.editFunctionRole(functionRoleEditDto);
    }

    @ApiOperation("删除角色")
    @Log(businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public void delete(@RequestBody SingleInfoReq<Long> req) {
        roleService.delete(req.getReq());
    }

    /**
     * 更新角色状态
     *
     * @param roleStatusUpdateDto 更新角色状态
     */
    @ApiOperation("更新角色状态")
    @Log(businessType = BusinessType.UPDATE)
    @PostMapping("/status")
    public void updateStatus(@Validated @RequestBody RoleStatusUpdateDto roleStatusUpdateDto) {
        roleService.updateStatus(roleStatusUpdateDto);
    }
}
