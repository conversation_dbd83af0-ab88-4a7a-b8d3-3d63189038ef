package com.chervon.authority.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.chervon.authority.api.exception.AuthorityErrorCode;
import com.chervon.authority.config.ExceptionMessageUtil;
import com.chervon.authority.domain.dto.role.InitOrgUserTreeDto;
import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.ListInfoReq;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.oss.uitl.S3Util;
import com.chervon.usercenter.api.service.OrganizationCommandService;
import com.chervon.usercenter.api.service.OrganizationQueryService;
import com.chervon.usercenter.api.service.OrganizationUserTreeQueryService;
import com.chervon.usercenter.api.vo.OrgUserNodeVo;
import com.chervon.usercenter.api.vo.OrganizationTreeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-06-07 10:05
 **/
@RestController
@RequestMapping("/organization")
@Slf4j
@Api(tags = "组织相关接口")
public class OrganizationController {
    @DubboReference
    private OrganizationCommandService organizationCommandService;
    @DubboReference
    private OrganizationQueryService organizationQueryService;
    @DubboReference
    private OrganizationUserTreeQueryService organizationUserTreeQueryService;
    @Autowired
    private S3Util s3Util;
    @Autowired
    private AwsProperties awsProperties;

    /**
     * 全量同步组织
     */
    @ApiOperation("全量同步组织")
    @Log(businessType = BusinessType.SYNC)
    @PostMapping("/full/sync")
    public void fullSyncOrganization() {
        organizationCommandService.fullSyncOrganization();
    }

    /**
     * 获取部门下面的部门和用户
     */
    @ApiOperation("获取部门下面的部门和用户")
    @Log(businessType = BusinessType.VIEW)
    @PostMapping("/get/children")
    public OrganizationTreeVo getChildren(@RequestBody SingleInfoReq<String> req) {
        return organizationQueryService.getOrganizationTree(req.getReq());
    }

    /**
     * 根据已有的部门或用户guid，找到一颗最小的树
     *
     * @param req 请求参数
     * @return 初始化树
     */
    @ApiOperation("根据已有的部门或用户guid，找到一颗最小的树")
    @Log(businessType = BusinessType.VIEW)
    @PostMapping("/initTreeBySelected")
    public OrgUserNodeVo initTreeBySelected(@RequestBody ListInfoReq<InitOrgUserTreeDto> req) {
        if (!CollectionUtils.isEmpty(req.getInfo())) {
            Map<Integer, List<InitOrgUserTreeDto>> collect = req.getInfo().stream().collect(Collectors.groupingBy(InitOrgUserTreeDto::getType));
            List<String> orgGuids = collect.getOrDefault(1, new ArrayList<>()).stream().map(InitOrgUserTreeDto::getAuthorizedGuid).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            List<String> userGuids = collect.getOrDefault(0, new ArrayList<>()).stream().map(InitOrgUserTreeDto::getAuthorizedGuid).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            return organizationQueryService.initTreeBySelected(orgGuids, userGuids);
        }
        return new OrgUserNodeVo();
    }

    /**
     * 根据输入内容查询符合条件的树
     *
     * @param req 请求参数
     * @return 初始化树
     */
    @ApiOperation("根据输入内容查询符合条件的树")
    @Log(businessType = BusinessType.VIEW)
    @PostMapping("/initTreeBySearch")
    public OrgUserNodeVo initTreeBySearch(@RequestBody SingleInfoReq<String> req) {
        if (StringUtils.isNotBlank(req.getReq())) {
            return organizationQueryService.initTreeBySearch(req.getReq());
        }
        return new OrgUserNodeVo();
    }

    /**
     * 通过文件获取全量组织用户结构树
     *
     * @return 返回JSON结构体
     */
    @ApiOperation("获取全量组织用户结构树")
    @Log(businessType = BusinessType.VIEW)
    @GetMapping("/getAllOrgUserTree")
    public R<?> getOrgLongText() throws IOException {
        String fileName = organizationUserTreeQueryService.getLatest();
        if (StringUtils.isBlank(fileName)) {
            // 如果数据库没有最新记录，PRC调用用户中心同步到S3以及数据库
            organizationQueryService.createAllOrgUserFile();
            throw ExceptionMessageUtil.getException(AuthorityErrorCode.AUTHORITY_ORG_USER_TREE_NOT_EXIST, fileName);
        }
        String localFilePath = "static/uploadFile/";
        File file = new File(localFilePath + fileName);
        if (!file.exists()) {
            // 如果本地容器中没有，则从S3下载
            s3Util.download(awsProperties.getPictureBucket().getName(), "allOrgUserTree/" + fileName, file);
        }
        if (!file.exists()) {
            // 如果S3中也没有，PRC调用用户中心同步到S3以及数据库
            organizationQueryService.createAllOrgUserFile();
            throw ExceptionMessageUtil.getException(AuthorityErrorCode.AUTHORITY_ORG_USER_TREE_NOT_EXIST, file.getName());
        }
        JSONObject jsonObject = JSONUtil.readJSONObject(file, StandardCharsets.UTF_8);
        return R.ok(jsonObject);
    }
}
