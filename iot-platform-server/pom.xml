<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.chervon.iot</groupId>
        <artifactId>iot-platform-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>iot-platform-server</artifactId>
    <name>iot-platform-server</name>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <sk.version>8.15.0</sk.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
            <version>${sk.version}</version>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-excel</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/ch.qos.logback/logback-core -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>

        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon.iot</groupId>
            <artifactId>iot-platform-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chervon.iot</groupId>
            <artifactId>iot-middle-platform-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chervon.iot</groupId>
            <artifactId>iot-app-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>message-center-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- Chervon Common Log -->
        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>configuration-center-sdk-language</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>configuration-center-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-oss</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>operation-platform-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sargeraswang.util</groupId>
            <artifactId>excel-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>user-center-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-mqtt</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.49</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
            <version>1.49</version>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>authority-platform-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-sso</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-i18n</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon.iot</groupId>
            <artifactId>fleet-web-api</artifactId>
        </dependency>

        <!--地图服务依赖-->
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>auth</artifactId>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>http-client-spi</artifactId>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>regions</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
