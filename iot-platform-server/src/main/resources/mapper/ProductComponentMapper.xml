<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chervon.technology.mapper.ProductComponentMapper">

    <select id="getComponentsByPid"
            resultType="com.chervon.technology.domain.dataobject.Component">
        select *
        from component COM
                 left join product_component PC
                           on COM.component_no = PC.component_no
        where PC.product_id = #{productId}
    </select>

    <select id="getSourceProductInfoByComponentsNo"
            resultType="com.chervon.technology.domain.vo.product.ProductBaseInfoVo">
        select distinct p.id as pId, p.category_id as categoryId, p.model as model, p.commodity_model as commodityModel
        from iot_platform.product p
                 left join product_component pc on pc.product_id = p.id
        where pc.component_no = #{componentNo}
          and pc.is_deleted = 0
          and p.is_deleted = 0
    </select>

    <select id="getProductInfoByComponentsNo" resultType="com.chervon.technology.domain.vo.product.ProductBaseInfoVo">
        SELECT
            p.id AS pId,
            p.category_id AS categoryId,
            p.model AS model,
            p.commodity_model AS commodityModel
        FROM
        product p  LEFT JOIN product_component pc ON pc.product_id = p.id
        WHERE
            pc.component_no = #{componentNo} and pc.is_deleted=0 and p.is_deleted=0
        order by p.create_time desc
    </select>
</mapper>
