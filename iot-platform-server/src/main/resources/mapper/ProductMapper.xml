<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chervon.technology.mapper.ProductMapper">

    <select id="selectByPage" parameterType="com.chervon.technology.api.dto.ProductDto"
            resultType="com.chervon.technology.domain.dataobject.Product">
        SELECT
        DISTINCT p.id, p.model, p.product_sn_code, p.product_name,
        p.product_type, p.commodity_model,p.product_icon, p.category_id, p.question_template,
        p.description, p.status, p.create_by, p.create_time, p.update_by,p.update_time, p.icon_type
        FROM product p
        where
        p.is_deleted = 0
        <if test="status != null">
            and p.status in
            <foreach collection="status" item="index" index="index" open="(" separator="," close=")">
                #{index}
            </foreach>
        </if>
        <if test="releaseStatus != null">
            and p.release_status in
            <foreach collection="releaseStatus" item="index" index="index" open="(" separator="," close=")">
                #{index}
            </foreach>
        </if>
        <if test="search.snCode != '' and  search.snCode != null">
            and p.product_sn_code like concat('%', #{search.snCode}, '%')
        </if>

        <if test="search.productType != null">
            and p.product_type=#{search.productType}
        </if>

        <if test="search.productName != '' and search.productName != null">
            and p.product_name like concat('%', #{search.productName}, '%')
        </if>

        <if test="search.networkModes != null">
            and network_mode_code in
            <foreach collection="search.networkModes" item="code" index="index" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="search.categoryIds != null">
            and category_id in
            <foreach collection="search.categoryIds" item="categoryId" index="index" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
        </if>
        <if test="search.commodityModels != null">
            and commodity_model in
            <foreach collection="search.commodityModels" item="model" index="index" open="(" separator="," close=")">
                #{model}
            </foreach>
        </if>
    </select>


    <select id="getProductSource" parameterType="com.chervon.technology.domain.dto.product.SearchProductDto"
            resultType="com.chervon.technology.domain.dataobject.Product">
        SELECT p.id as id, p.product_name as productName, p.model as model, p.commodity_model as commodityModel,
        p.product_sn_code as productSnCode, p.product_icon as productIcon, p.category_id as categoryId,
        p.brand_id as brandId, p.product_type as productType, p.description as description, p.status as status,
        p.create_by as createBy, p.update_by as updateBy, p.create_time as createTime, p.update_time as updateTime, p.business_type as businessType
        from product p
        where p.is_deleted = 0 and create_type = 0
        <if test="search.categoryId != null">
            and p.category_id=#{search.categoryId}
        </if>
        <if test="search.status != '' and search.status != null">
            and p.status=#{search.status}
        </if>
        <if test="search.pId != '' and search.pId != null">
            and p.id like concat('%', #{search.pId}, '%')
        </if>
        <if test="search.model != null and search.model != ''">
            and p.model like concat('%', #{search.model}, '%')
        </if>
        <if test="search.commodityModel != null and search.commodityModel != ''">
            and p.commodity_model like concat('%', #{search.commodityModel}, '%')
        </if>
        <!--创建时间更新时间区间搜索-->
        <if test="search.createStartTime != null and search.createStartTime != ''">
            and p.create_time &gt;= #{search.createStartTime}
        </if>
        <if test="search.createEndTime != null and search.createEndTime != ''">
            and p.create_time &lt;= #{search.createEndTime}
        </if>
        <if test="search.updateStartTime != null and search.updateStartTime != ''">
            and p.update_time &gt;= #{search.updateStartTime}
        </if>
        <if test="search.updateEndTime != null and search.updateEndTime != ''">
            and p.update_time &lt;= #{search.updateEndTime}
        </if>
        <if test="search.businessTypeStr != null and search.businessTypeStr != ''">
            and p.business_type = #{search.businessTypeStr}
        </if>
        order by p.create_time desc
    </select>


    <select id="getOperationProductList" parameterType="com.chervon.technology.api.dto.ProductOperationSearchDto"
            resultType="com.chervon.technology.domain.dataobject.Product">
        select p.id as id, p.product_name as productName, p.model as model, p.commodity_model as
        commodityModel,
        p.product_sn_code as productSnCode, p.product_icon as productIcon, p.category_id as categoryId,
        p.brand_id as brandId, p.product_type as productType, p.operation_remark as operationRemark, p.status as status,
        p.release_status as releaseStatus, p.create_type as createType, p.question_template as questionTemplate,
        p.create_by as createBy, p.update_by as updateBy, p.create_time as createTime, p.update_time as updateTime,
        p.icon_type as iconType
        from product p
        where p.is_deleted = 0
        <if test="search.categoryId != null">
            and p.category_id=#{search.categoryId}
        </if>
        <if test="search.id != '' and search.id != null">
            and p.id like concat('%', #{search.id}, '%')
        </if>
        <if test="search.model != null and search.model != ''">
            and p.model like concat('%', #{search.model}, '%')
        </if>
        <if test="search.commodityModel != null and search.commodityModel != ''">
            and p.commodity_model like concat('%', #{search.commodityModel}, '%')
        </if>
        <if test="search.productSnCode != null and search.productSnCode != ''">
            and p.product_sn_code like concat('%', #{search.productSnCode}, '%')
        </if>
        <if test="search.status != null and search.status != ''">
            and p.status = #{search.status}
        </if>
        <if test="search.releaseStatus != null and search.releaseStatus != ''">
            and p.release_status = #{search.releaseStatus}
        </if>
        <if test="search.brandId != null">
            and p.brand_id = #{search.brandId}
        </if>
        <if test="productNameLangIds != null">
            and p.product_name in
            <foreach collection="productNameLangIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="search.productType != null and search.productType != '' ">
            and p.product_type = #{search.productType}
        </if>
        <if test="search.createStartTime != null and search.createStartTime != ''">
            and p.create_time &gt;= #{search.createStartTime}
        </if>
        <if test="search.createEndTime != null and search.createEndTime != ''">
            and p.create_time &lt;= #{search.createEndTime}
        </if>
        <if test="search.updateStartTime != null and search.updateStartTime != ''">
            and p.update_time &gt;= #{search.updateStartTime}
        </if>
        <if test="search.updateEndTime != null and search.updateEndTime != ''">
            and p.update_time &lt;= #{search.updateEndTime}
        </if>
        order by p.create_time desc
    </select>

    <select id="getOperationReleaseList" parameterType="com.chervon.technology.api.dto.ProductOperationSearchDto"
            resultType="com.chervon.technology.domain.vo.product.ProductReleaseMergeVo">
        SELECT distinct p.id as id, p.product_name as productName, p.model as model, p.commodity_model as
        commodityModel, p.create_time as createTime,
        p.product_sn_code as productSnCode, p.product_icon as productIcon, p.category_id as categoryId,
        p.brand_id as brandId, p.product_type as productType, p.operation_remark as operationRemark,
        p.release_status as releaseStatus, opd.id as draftId, opd.is_deleted as draftDeleted, opd.commodity_model as
        draftCommodityModel, opd.product_name as draftProductName,
        opd.product_sn_code as draftProductSnCode, opd.product_icon as draftProductIcon, opd.brand_id as draftBrandId,
        opd.release_status as draftReleaseStatus, pr.apply_time as applyTime, pr.apply_by as applyBy,
        pr.approve_by as approveBy, pr.approve_time as approveTime, opd.operation_remark as draftOperationRemark
        FROM product_release pr
        left join product p
        on p.id=pr.p_id
        left join operation_product_draft opd
        on opd.p_id=p.id
        where p.is_deleted = 0 and
        (
        ((opd.id is null
        or opd.is_deleted=1
        <if test="pIds != null">
            or p.id not in
            <foreach collection="pIds" item="pId" index="index" open="(" separator="," close=")">
                #{pId}
            </foreach>
        </if>
        )
        <if test="search.categoryId != null">
            and p.category_id=#{search.categoryId}
        </if>
        <if test="search.id != '' and search.id != null">
            and p.id like concat('%', #{search.id}, '%')
        </if>
        <if test="search.productSnCode != null and search.productSnCode != '' ">
            and p.product_sn_code like concat('%', #{search.productSnCode}, '%')
        </if>
        <if test="search.applyStartTime != null and search.applyStartTime != ''">
            and pr.apply_time &gt;= #{search.applyStartTime}
        </if>
        <if test="search.applyEndTime != null and search.applyEndTime != ''">
            and pr.apply_time &lt;= #{search.applyEndTime}
        </if>
        <if test="search.approveStartTime != null and search.approveStartTime != ''">
            and pr.approve_time &gt;= #{search.approveStartTime}
        </if>
        <if test="search.approveEndTime != null and search.approveEndTime != ''">
            and pr.approve_time &lt;= #{search.approveEndTime}
        </if>
        <if test="search.applyBy != null and search.applyBy != '' ">
            and pr.apply_by like concat('%', #{search.applyBy}, '%')
        </if>
        <if test="search.approveBy != null and search.approveBy != '' ">
            and pr.approve_by like concat('%', #{search.approveBy}, '%')
        </if>
        <if test="search.model != null and search.model != ''">
            and p.model like concat('%', #{search.model}, '%')
        </if>
        <if test="search.commodityModel != null and search.commodityModel != ''">
            and p.commodity_model like concat('%', #{search.commodityModel}, '%')
        </if>
        <if test="search.status != null and search.status != ''">
            and p.status = #{search.status}
        </if>
        <if test="search.releaseStatus != null and search.releaseStatus != ''">
            and p.release_status = #{search.releaseStatus}
        </if>
        <if test="search.brandId != null">
            and p.brand_id = #{search.brandId}
        </if>
        <if test="productNameLangIds != null">
            and p.product_name in
            <foreach collection="productNameLangIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="search.productType != null and search.productType != '' ">
            and p.product_type = #{search.productType}
        </if>
        ) or (
        opd.is_deleted = 0
        <if test="search.categoryId != null">
            and p.category_id=#{search.categoryId}
        </if>
        <if test="search.id != '' and search.id != null">
            and opd.p_id like concat('%', #{search.id}, '%')
        </if>
        <if test="search.productSnCode != null and search.productSnCode != '' ">
            and opd.product_sn_code like concat('%', #{search.productSnCode}, '%')
        </if>
        <if test="search.applyStartTime != null and search.applyStartTime != ''">
            and pr.apply_time &gt;= #{search.applyStartTime}
        </if>
        <if test="search.applyEndTime != null and search.applyEndTime != ''">
            and pr.apply_time &lt;= #{search.applyEndTime}
        </if>
        <if test="search.approveStartTime != null and search.approveStartTime != ''">
            and pr.approve_time &gt;= #{search.approveStartTime}
        </if>
        <if test="search.approveEndTime != null and search.approveEndTime != ''">
            and pr.approve_time &lt;= #{search.approveEndTime}
        </if>
        <if test="search.applyBy != null and search.applyBy != '' ">
            and pr.apply_by like concat('%', #{search.applyBy}, '%')
        </if>
        <if test="search.approveBy != null and search.approveBy != '' ">
            and pr.approve_by like concat('%', #{search.approveBy}, '%')
        </if>
        <if test="search.model != null and search.model != ''">
            and p.model like concat('%', #{search.model}, '%')
        </if>
        <if test="search.commodityModel != null and search.commodityModel != ''">
            and opd.commodity_model like concat('%', #{search.commodityModel}, '%')
        </if>
        <if test="search.status != null and search.status != ''">
            and p.status = #{search.status}
        </if>
        <if test="search.releaseStatus != null and search.releaseStatus != ''">
            and opd.release_status = #{search.releaseStatus}
        </if>
        <if test="search.brandId != null">
            and opd.brand_id = #{search.brandId}
        </if>
        <if test="productNameLangIds != null">
            and opd.product_name in
            <foreach collection="productNameLangIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="search.productType != null and search.productType != '' ">
            and p.product_type = #{search.productType}
        </if>
        <if test="pIds != null">
            and opd.p_id in
            <foreach collection="pIds" item="pId" index="index" open="(" separator="," close=")">
                #{pId}
            </foreach>
        </if>
        )
        )
        order by p.create_time desc
    </select>

    <select id="getSourceReleaseList" parameterType="com.chervon.technology.api.dto.ProductOperationSearchDto"
            resultType="com.chervon.technology.api.vo.ProductReleaseVo">
        SELECT distinct p.id as id, p.product_name as productName, p.model as model, p.commodity_model as
        commodityModel, p.create_time as createTime,
        p.product_sn_code as productSnCode, p.product_icon as productIcon, p.category_id as categoryId,
        p.brand_id as brandId, p.product_type as productType, p.operation_remark as operationRemark,
        p.release_status as releaseStatus, pr.apply_time as applyTime, pr.apply_by as applyBy,
        pr.approve_by as approveBy, pr.approve_time as approveTime
        FROM product_release pr
        left join product p
        on p.id=pr.p_id
        where p.is_deleted = 0
        <if test="search.categoryId != null">
            and p.category_id=#{search.categoryId}
        </if>
        <if test="search.id != '' and search.id != null">
            and p.id like concat('%', #{search.id}, '%')
        </if>
        <if test="search.productSnCode != null and search.productSnCode != '' ">
            and p.product_sn_code like concat('%', #{search.productSnCode}, '%')
        </if>
        <if test="search.applyStartTime != null and search.applyStartTime != ''">
            and pr.apply_time &gt;= #{search.applyStartTime}
        </if>
        <if test="search.applyEndTime != null and search.applyEndTime != ''">
            and pr.apply_time &lt;= #{search.applyEndTime}
        </if>
        <if test="search.approveStartTime != null and search.approveStartTime != ''">
            and pr.approve_time &gt;= #{search.approveStartTime}
        </if>
        <if test="search.approveEndTime != null and search.approveEndTime != ''">
            and pr.approve_time &lt;= #{search.approveEndTime}
        </if>
        <if test="search.model != null and search.model != ''">
            and p.model like concat('%', #{search.model}, '%')
        </if>
        <if test="search.commodityModel != null and search.commodityModel != ''">
            and p.commodity_model like concat('%', #{search.commodityModel}, '%')
        </if>
        <if test="search.status != null and search.status != ''">
            and p.status = #{search.status}
        </if>
        <if test="search.releaseStatus != null and search.releaseStatus != ''">
            and p.release_status = #{search.releaseStatus}
        </if>
        <if test="search.brandId != null">
            and p.brand_id = #{search.brandId}
        </if>
        <if test="productNameLangIds != null">
            and p.product_name in
            <foreach collection="productNameLangIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

        <if test="search.productType != null and search.productType != '' ">
            and p.product_type = #{search.productType}
        </if>
        <if test="search.applyBy != null and search.applyBy != '' ">
            and pr.apply_by like concat('%', #{search.applyBy}, '%')
        </if>
        <if test="search.approveBy != null and search.approveBy != '' ">
            and pr.approve_by like concat('%', #{search.approveBy}, '%')
        </if>
        order by p.create_time desc
    </select>

    <select id="getOperationProductBase"
            resultType="com.chervon.technology.domain.vo.product.OperationProductMergeBaseVo">
        select p.id as pId,
        p.brand_id as brandId,
        p.commodity_model as commodityModel,
        opd.id as draftId,
        opd.brand_id as draftBrandId,
        opd.commodity_model as draftCommodityModel
        from product p
        left join operation_product_draft opd
        on opd.p_id = p.id
        where p.is_deleted = 0
        <if test="pIds != null">
            and p.id in
            <foreach collection="pIds" item="pId" index="index" open="(" separator="," close=")">
                #{pId}
            </foreach>
        </if>
    </select>

    <select id="getProductTypeListByDeviceIds" resultType="com.chervon.technology.domain.dataobject.Product">
        select p.id as id, p.product_type as productType
        from iot_platform.product p
        join iot_platform.device d on p.id = d.product_id
        where d.device_id in
        <if test="deviceIds != null">
            <foreach collection="deviceIds" item="deviceId" index="index" open="(" separator="," close=")">
                #{deviceId}
            </foreach>
            order by FIELD(d.device_id,
            <foreach collection="deviceIds" item="deviceId" index="index" separator=",">
                #{deviceId}
            </foreach>)
        </if>
    </select>
    <select id="getReleaseProductAppShowMaxOrder" resultType="java.lang.Integer">
        select ifnull(max(app_show_order),0)
        from product
        where release_status='PReleased' and is_deleted = 0
    </select>
</mapper>