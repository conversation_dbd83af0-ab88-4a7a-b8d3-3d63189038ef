<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chervon.technology.mapper.DeviceUserRefMapper">
    <select id="pageOperationDevice" resultType="com.chervon.technology.api.vo.OperationDevicePageVo">
        select
        t1.device_id as deviceId,
        t1.sn as sn,
        t3.id as pid,
        t3.category_id as categoryId,
        t3.brand_id as brandId,
        t3.model as productModel,
        t3.commodity_model as commodityModel,
        t3.product_type as productType,
        t1.phone_num as devicePhoneNum,
        t1.is_online as isOnline,
        t1.status as status,
        t2.current_bind_business_type as currentBindBusinessType,
        t1.usage_status as usageStatus,
        t2.first_bind_business_type as firstBindBusinessType,
        t2.first_bind_ego_user_id as firstBindEgoUserId,
        t2.first_bind_fleet_company_id as firstBindFleetCompanyId,
        t2.first_bind_fleet_user_id as firstBindFleetUserId,
        t1.activation_time as activationTime,
        t2.first_bind_time as firstBindTime,
        t1.last_login_time as lastLoginTime
        from device as t1
        left join device_user_ref as t2 on t1.device_id = t2.device_id and t2.is_deleted = 0
        left join product as t3 on t1.product_id = t3.id  and t3.is_deleted = 0
        where t1.is_deleted = 0
        <if test="search.firstBindStartTime != null and search.firstBindStartTime != ''">
            and t2.first_bind_time &gt;= #{search.firstBindStartTime}
        </if>
        <if test="search.firstBindEndTime != null and search.firstBindEndTime != ''">
            and t2.first_bind_time &lt;= #{search.firstBindEndTime}
        </if>
        <if test="search.lastLoginStartTime != null and search.lastLoginStartTime != ''">
            and t1.last_login_time &gt;= #{search.lastLoginStartTime}
        </if>
        <if test="search.lastLoginEndTime != null and search.lastLoginEndTime != ''">
            and t1.last_login_time &lt;= #{search.lastLoginEndTime}
        </if>
        <if test="search.activationStartTime != null and search.activationStartTime != ''">
            and t1.activation_time &gt;= #{search.activationStartTime}
        </if>
        <if test="search.activationEndTime != null and search.activationEndTime != ''">
            and t1.activation_time &lt;= #{search.activationEndTime}
        </if>
        <if test="search.deviceId != null and search.deviceId != ''">
            and t1.device_id like concat('%',#{search.deviceId},'%')
        </if>
        <if test="search.sn != null and search.sn != ''">
            and t1.sn like concat('%',#{search.sn},'%')
        </if>
        <if test="search.pid != null and search.pid != ''">
            and t3.id like concat('%',#{search.pid},'%')
        </if>
        <if test="search.categoryId != null">
            and t3.category_id = #{search.categoryId}
        </if>
        <if test="search.brandId != null">
            and t3.brand_id = #{search.brandId}
        </if>
        <if test="search.productModel != null and search.productModel != ''">
            and t3.model like concat('%',#{search.productModel},'%')
        </if>
        <if test="search.commodityModel != null and search.commodityModel != ''">
            and t3.commodity_model like concat('%',#{search.commodityModel},'%')
        </if>
        <if test="search.productType != null and search.productType != ''">
            and t3.product_type = #{search.productType}
        </if>
        <if test="search.devicePhoneNum != null and search.devicePhoneNum != ''">
            and t1.phone_num like concat('%',#{search.devicePhoneNum},'%')
        </if>
        <if test="search.isOnline != null">
            and t1.is_online = #{search.isOnline}
        </if>
        <if test="search.status != null">
            and t1.status = #{search.status}
        </if>
        <if test="search.usageStatus != null">
            and t1.usage_status = #{search.usageStatus}
        </if>

        <if test="search.currentBindBusinessTypeStr != null">
            and t2.current_bind_business_type = #{search.currentBindBusinessTypeStr}
        </if>

        <if test="search.firstBindBusinessTypeStr != null">
            and t2.first_bind_business_type = #{search.firstBindBusinessTypeStr}
        </if>

        <if test="search.firstBindEgoUserId != null and search.firstBindEgoUserId != ''">
            and t2.first_bind_ego_user_id like concat('%',#{search.firstBindEgoUserId},'%')
        </if>
        <if test="search.firstBindFleetCompanyId != null and search.firstBindFleetCompanyId != ''">
            and t2.first_bind_fleet_company_id like concat('%',#{search.firstBindFleetCompanyId},'%')
        </if>
        <if test="search.firstBindFleetUserId != null and search.firstBindFleetUserId != ''">
            and t2.first_bind_fleet_user_id like concat('%',#{search.firstBindFleetUserId},'%')
        </if>
        <if test="currentBindEgoDeviceIds != null and currentBindEgoDeviceIds.size() > 0">
            and t2.device_id in
            <foreach collection="currentBindEgoDeviceIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
            and t2.current_bind_business_type like '%1%'
        </if>
        <if test="currentBindFleetDeviceIds != null and currentBindFleetDeviceIds.size() > 0">
            and t2.device_id in
            <foreach collection="currentBindFleetDeviceIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
            and t2.current_bind_business_type like '%2%'
        </if>
        order by t1.create_time desc
    </select>

    <select id="listOperationDevice" resultType="com.chervon.technology.api.vo.OperationDevicePageVo">
        select
        t1.device_id as deviceId,
        t1.sn as sn,
        t3.id as pid,
        t3.category_id as categoryId,
        t3.brand_id as brandId,
        t3.model as productModel,
        t3.commodity_model as commodityModel,
        t3.product_type as productType,
        t1.phone_num as devicePhoneNum,
        t1.is_online as isOnline,
        t1.status as status,
        t2.current_bind_business_type as currentBindBusinessType,
        t1.usage_status as usageStatus,
        t2.first_bind_business_type as firstBindBusinessType,
        t2.first_bind_ego_user_id as firstBindEgoUserId,
        t2.first_bind_fleet_company_id as firstBindFleetCompanyId,
        t2.first_bind_fleet_user_id as firstBindFleetUserId,
        t1.activation_time as activationTime,
        t2.first_bind_time as firstBindTime,
        t1.last_login_time as lastLoginTime
        from device as t1
        left join device_user_ref as t2 on t1.device_id = t2.device_id and t2.is_deleted = 0
        left join product as t3 on t1.product_id = t3.id and t3.is_deleted = 0
        where t1.is_deleted = 0
        <if test="search.firstBindStartTime != null and search.firstBindStartTime != ''">
            and t2.first_bind_time &gt;= #{search.firstBindStartTime}
        </if>
        <if test="search.firstBindEndTime != null and search.firstBindEndTime != ''">
            and t2.first_bind_time &lt;= #{search.firstBindEndTime}
        </if>
        <if test="search.lastLoginStartTime != null and search.lastLoginStartTime != ''">
            and t1.last_login_time &gt;= #{search.lastLoginStartTime}
        </if>
        <if test="search.lastLoginEndTime != null and search.lastLoginEndTime != ''">
            and t1.last_login_time &lt;= #{search.lastLoginEndTime}
        </if>
        <if test="search.activationStartTime != null and search.activationStartTime != ''">
            and t1.activation_time &gt;= #{search.activationStartTime}
        </if>
        <if test="search.activationEndTime != null and search.activationEndTime != ''">
            and t1.activation_time &lt;= #{search.activationEndTime}
        </if>
        <if test="search.deviceId != null and search.deviceId != ''">
            and t1.device_id like concat('%',#{search.deviceId},'%')
        </if>
        <if test="search.sn != null and search.sn != ''">
            and t1.sn like concat('%',#{search.sn},'%')
        </if>
        <if test="search.pid != null and search.pid != ''">
            and t3.id like concat('%',#{search.pid},'%')
        </if>
        <if test="search.categoryId != null">
            and t3.category_id = #{search.categoryId}
        </if>
        <if test="search.brandId != null">
            and t3.brand_id = #{search.brandId}
        </if>
        <if test="search.productModel != null and search.productModel != ''">
            and t3.model like concat('%',#{search.productModel},'%')
        </if>
        <if test="search.commodityModel != null and search.commodityModel != ''">
            and t3.commodity_model like concat('%',#{search.commodityModel},'%')
        </if>
        <if test="search.productType != null and search.productType != ''">
            and t3.product_type = #{search.productType}
        </if>
        <if test="search.devicePhoneNum != null and search.devicePhoneNum != ''">
            and t1.phone_num like concat('%',#{search.devicePhoneNum},'%')
        </if>
        <if test="search.isOnline != null">
            and t1.is_online = #{search.isOnline}
        </if>
        <if test="search.status != null">
            and t1.status = #{search.status}
        </if>
        <if test="search.usageStatus != null">
            and t1.usage_status = #{search.usageStatus}
        </if>

        <if test="search.currentBindBusinessTypeStr != null">
            and t2.current_bind_business_type = #{search.currentBindBusinessTypeStr}
        </if>

        <if test="search.firstBindBusinessTypeStr != null">
            and t2.first_bind_business_type = #{search.firstBindBusinessTypeStr}
        </if>

        <if test="search.firstBindEgoUserId != null and search.firstBindEgoUserId != ''">
            and t2.first_bind_ego_user_id like concat('%',#{search.firstBindEgoUserId},'%')
        </if>
        <if test="search.firstBindFleetCompanyId != null and search.firstBindFleetCompanyId != ''">
            and t2.first_bind_fleet_company_id like concat('%',#{search.firstBindFleetCompanyId},'%')
        </if>
        <if test="search.firstBindFleetUserId != null and search.firstBindFleetUserId != ''">
            and t2.first_bind_fleet_user_id like concat('%',#{search.firstBindFleetUserId},'%')
        </if>
        <if test="currentBindEgoDeviceIds != null and currentBindEgoDeviceIds.size() > 0">
            and t2.device_id in
            <foreach collection="currentBindEgoDeviceIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
            and t2.current_bind_business_type like '%1%'
        </if>
        <if test="currentBindFleetDeviceIds != null and currentBindFleetDeviceIds.size() > 0">
            and t2.device_id in
            <foreach collection="currentBindFleetDeviceIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
            and t2.current_bind_business_type like '%2%'
        </if>
        order by t1.create_time desc
    </select>

</mapper>
