<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chervon.technology.mapper.ChargingMapper">

    <select id="flowChargingPage" resultType="com.chervon.technology.api.vo.charging.FlowChargingVo">
        SELECT distinct
        t1.id as chargingId,
        t3.commodity_model as commodityModel,
        t1.create_time as createTime
        FROM charging as t1
        left join charging_flow as t2 on t1.id = t2.charging_id and t2.is_deleted = 0
        left join product as t3 on t1.product_id = t3.id
        where t1.is_deleted = 0  and t3.is_deleted = 0 and t1.type = 'flow'
        <if test="search.chargingId != null and search.chargingId != ''">
            and t1.id like concat('%',#{search.chargingId},'%')
        </if>
        <if test="search.commodityModel != null and search.commodityModel != ''">
            and t3.commodity_model like concat('%',#{search.commodityModel},'%')
        </if>
        <if test="search.chargingStartTime != null and search.chargingStartTime != ''">
            and t2.handle_time &gt;= #{search.chargingStartTime}
        </if>
        <if test="search.chargingEndTime != null and search.chargingEndTime != ''">
            and t2.handle_time &lt;= #{search.chargingEndTime}
        </if>
        order by t1.create_time desc
    </select>

    <select id="flowChargingList" resultType="com.chervon.technology.api.vo.charging.FlowChargingVo">
        SELECT distinct
        t1.id as chargingId,
        t3.commodity_model as commodityModel,
        t1.create_time as createTime
        FROM charging as t1
        left join charging_flow as t2 on t1.id = t2.charging_id and t2.is_deleted = 0
        left join product as t3 on t1.product_id = t3.id
        where t1.is_deleted = 0 and t3.is_deleted = 0 and t1.type = 'flow'
        <if test="search.chargingId != null and search.chargingId != ''">
            and t1.id like concat('%',#{search.chargingId},'%')
        </if>
        <if test="search.commodityModel != null and search.commodityModel != ''">
            and t3.commodity_model like concat('%',#{search.commodityModel},'%')
        </if>
        <if test="search.chargingStartTime != null and search.chargingStartTime != ''">
            and t2.handle_time &gt;= #{search.chargingStartTime}
        </if>
        <if test="search.chargingEndTime != null and search.chargingEndTime != ''">
            and t2.handle_time &lt;= #{search.chargingEndTime}
        </if>
        order by t1.create_time desc
    </select>


    <select id="smsChargingPage" resultType="com.chervon.technology.api.vo.charging.SmsChargingVo">
        SELECT distinct
        t1.id as chargingId,
        t1.msg_id as msgId,
        t1.msg_title_lang_code as msgTitle,
        t1.msg_type as msgType,
        t3.commodity_model as commodityModel,
        t1.create_time as createTime
        FROM charging as t1
        left join charging_sms as t2 on t1.id = t2.charging_id and t2.is_deleted = 0
        left join product as t3 on t1.product_id = t3.id
        where t1.is_deleted = 0 and t3.is_deleted = 0 and t1.type = 'sms'
        <if test="search.chargingId != null and search.chargingId != ''">
            and t1.id like concat('%',#{search.chargingId},'%')
        </if>
        <if test="search.msgId != null and search.msgId != ''">
            and t1.msg_id like concat('%',#{search.msgId},'%')
        </if>
        <if test="msgTitleLangIds != null and msgTitleLangIds.size() > 0">
            and t1.msg_title_lang_id in
            <foreach collection="msgTitleLangIds" item="msgTitleLangId" open="(" separator="," close=")">
                #{msgTitleLangId}
            </foreach>
        </if>
        <if test="search.msgType != null and search.msgType != ''">
            and t1.msg_type = #{search.msgType}
        </if>
        <if test="search.commodityModel != null and search.commodityModel != ''">
            and t3.commodity_model like concat('%',#{search.commodityModel},'%')
        </if>
        <if test="search.chargingStartTime != null and search.chargingStartTime != ''">
            and t2.push_time &gt;= #{search.chargingStartTime}
        </if>
        <if test="search.chargingEndTime != null and search.chargingEndTime != ''">
            and t2.push_time &lt;= #{search.chargingEndTime}
        </if>
        order by t1.create_time desc
    </select>

    <select id="smsChargingList" resultType="com.chervon.technology.api.vo.charging.SmsChargingVo">
        SELECT distinct
        t1.id as chargingId,
        t1.msg_id as msgId,
        t1.msg_title_lang_code as msgTitle,
        t1.msg_type as msgType,
        t3.commodity_model as commodityModel,
        t1.create_time as createTime
        FROM charging as t1
        left join charging_sms as t2 on t1.id = t2.charging_id and t2.is_deleted = 0
        left join product as t3 on t1.product_id = t3.id
        where t1.is_deleted = 0 and t3.is_deleted = 0 and t1.type = 'sms'
        <if test="search.chargingId != null and search.chargingId != ''">
            and t1.id like concat('%',#{search.chargingId},'%')
        </if>
        <if test="search.msgId != null and search.msgId != ''">
            and t1.msg_id like concat('%',#{search.msgId},'%')
        </if>
        <if test="msgTitleLangIds != null and msgTitleLangIds.size() > 0">
            and t1.msg_title_lang_id in
            <foreach collection="msgTitleLangIds" item="msgTitleLangId" open="(" separator="," close=")">
                #{msgTitleLangId}
            </foreach>
        </if>
        <if test="search.msgType != null and search.msgType != ''">
            and t1.msg_type = #{search.msgType}
        </if>
        <if test="search.commodityModel != null and search.commodityModel != ''">
            and t3.commodity_model like concat('%',#{search.commodityModel},'%')
        </if>
        <if test="search.chargingStartTime != null and search.chargingStartTime != ''">
            and t2.push_time &gt;= #{search.chargingStartTime}
        </if>
        <if test="search.chargingEndTime != null and search.chargingEndTime != ''">
            and t2.push_time &lt;= #{search.chargingEndTime}
        </if>
        order by t1.create_time desc
    </select>
</mapper>
