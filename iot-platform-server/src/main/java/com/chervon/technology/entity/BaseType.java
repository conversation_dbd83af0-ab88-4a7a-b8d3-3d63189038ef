package com.chervon.technology.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/9/9 11:38
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("base_type")
public class BaseType extends BaseDo {

    /**
     * 品类id
     */
    private Long categoryId;

    /**
     * 品类多语言id
     */
    private Long categoryLangId;

    /**
     * 品类多语言code
     */
    private String categoryLangCode;

    /**
     * 品类编码
     */
    private String code;
}
