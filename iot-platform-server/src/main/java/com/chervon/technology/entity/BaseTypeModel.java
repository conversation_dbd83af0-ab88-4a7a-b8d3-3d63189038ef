package com.chervon.technology.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/9/13 9:36
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("base_type_model")
public class BaseTypeModel extends BaseDo {

    /**
     * 基础设备错误码id
     */
    private Long baseTypeId;

    /**
     * 模块名称
     */
    private String name;

    /**
     * 模块多语言id
     */
    private Long nameLangId;

    /**
     * 模块多语言code
     */
    private String nameLangCode;

    /**
     * 模块编码
     */
    private String code;
}
