package com.chervon.technology.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/9/7 13:32
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("device_error_code")
public class DeviceErrorCode extends BaseDo {

    /**
     * 基础品类管理id
     */
    private Long baseTypeId;

    /**
     * 品类Id
     */
    private Long categoryId;

    /**
     * 品类多语言id
     */
    private Long categoryLangId;

    /**
     * 品类多语言code
     */
    private String categoryLangCode;

    /**
     * 品类编码
     */
    private String categoryCode;

    /**
     * 模块id
     */
    private Long modelId;

    /**
     * 模块多语言id
     */
    private Long modelLangId;

    /**
     * 模块多语言code
     */
    private String modelLangCode;

    /**
     * 模块编码
     */
    private String modelCode;

    /**
     * 产品型号
     */
    private String productModel;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 事件类型code
     */
    private String eventCode;

    /**
     * 功能名称多语言code
     */
    private String functionLangCode;

    /**
     * 功能名称多语言id
     */
    private Long functionLangId;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 备注多语言id
     */
    private Long remarkLangId;

    /**
     * 备注多语言code
     */
    private String remarkLangCode;
}
