package com.chervon.technology.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.technology.entity.BaseTypeModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2022/9/13 9:40
 */
@Mapper
public interface BaseTypeModelMapper extends BaseMapper<BaseTypeModel> {

    /**
     * 根据品类编码和模块编码查询
     *
     * @param categoryCode 品类编码
     * @param modelCode    模块编码
     * @return 基础错误码
     */
    BaseTypeModel selectByCode(@Param("categoryCode") String categoryCode, @Param("modelCode") String modelCode);
}
