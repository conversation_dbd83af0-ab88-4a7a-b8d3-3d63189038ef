package com.chervon.technology.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chervon.technology.api.dto.charging.FlowChargingPageDto;
import com.chervon.technology.api.dto.charging.SmsChargingPageDto;
import com.chervon.technology.api.vo.charging.FlowChargingVo;
import com.chervon.technology.api.vo.charging.SmsChargingVo;
import com.chervon.technology.domain.dataobject.Charging;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ChargingMapper extends BaseMapper<Charging> {

    /**
     * 按条件分页查询计费情况
     *
     * @param page   分页
     * @param search 条件
     * @return 分页数据
     */
    IPage<FlowChargingVo> flowChargingPage(IPage<Charging> page, @Param("search") FlowChargingPageDto search);

    /**
     * 按条件列表查询计费情况
     *
     * @param search 条件
     * @return 列表数据
     */
    List<FlowChargingVo> flowChargingList(@Param("search") FlowChargingPageDto search);

    /**
     * 按条件分页查询计费情况
     *
     * @param page   分页
     * @param search 条件
     * @return 分页数据
     */
    IPage<SmsChargingVo> smsChargingPage(IPage<Charging> page,
                                         @Param("search") SmsChargingPageDto search,
                                         @Param("msgTitleLangIds") List<Long> msgTitleLangIds);

    /**
     * 按条件列表查询计费情况
     *
     * @param search 条件
     * @return 列表数据
     */
    List<SmsChargingVo> smsChargingList(@Param("search") SmsChargingPageDto search,
                                        @Param("msgTitleLangIds") List<Long> msgTitleLangIds);



}
