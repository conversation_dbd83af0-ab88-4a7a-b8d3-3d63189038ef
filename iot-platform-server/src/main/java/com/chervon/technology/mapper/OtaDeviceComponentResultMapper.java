package com.chervon.technology.mapper;

import com.chervon.technology.api.dto.DeviceJobResultDto;
import com.chervon.technology.api.vo.ota.DeviceJobResultVo;
import com.chervon.technology.domain.entity.OtaDeviceComponentResult;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-04
 */
public interface OtaDeviceComponentResultMapper extends BaseMapper<OtaDeviceComponentResult> {
//    /**
//     * 获取设备升级结果(废弃)
//     * <AUTHOR>
//     * @date 13:51 2022/8/4
//     * @param deviceJobResultDto:
//     * @return com.chervon.technology.api.vo.ota.DeviceJobResultVo
//     **/
//    DeviceJobResultVo getDeviceJobResult(@Param("deviceJobResultDto") DeviceJobResultDto deviceJobResultDto);
}
