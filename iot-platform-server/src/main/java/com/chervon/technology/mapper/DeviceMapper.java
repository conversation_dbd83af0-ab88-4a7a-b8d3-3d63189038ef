package com.chervon.technology.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.iot.middle.api.vo.device.DeviceTopologyVo;
import com.chervon.technology.api.dto.DeviceProductDto;
import com.chervon.technology.api.vo.DeviceRpcVo;
import com.chervon.technology.domain.dataobject.Device;
import com.chervon.technology.domain.dataobject.DeviceCode;
import com.chervon.technology.domain.dto.DeviceUpdateDto;
import com.chervon.technology.domain.dto.device.SearchDeviceDto;
import com.chervon.technology.domain.vo.device.DeviceDetailVo;
import com.chervon.technology.domain.vo.device.DeviceListVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 设备表
 *
 * <AUTHOR>
 * @date 2022-04-25 19:00:12
 */
@Mapper
public interface DeviceMapper extends BaseMapper<Device> {

    /**
     * 获取设备列表
     *
     * @param page         分页属性
     * @param searchDevice 搜索条件Dto
     * @return 设备列表Vo列表
     */
    List<DeviceListVo> getDevicePage(@Param("page") Page<DeviceListVo> page,
                                     @Param("searchDevice") SearchDeviceDto searchDevice);


    /**
     * 获取设备列表-不分页
     *
     * @param searchDevice 搜索条件Dto
     * @return 设备列表Vo列表
     */
    List<DeviceListVo> getDeviceList(@Param("searchDevice") SearchDeviceDto searchDevice);

    /**
     * 获取设备详情
     *
     * @param deviceId 设备Id
     * @return 设备详情Vo
     */
    DeviceDetailVo getDeviceDetail(@Param("deviceId") String deviceId);

    /**
     * 通过设备Id列表顺序获取DeviceRpcVo列表
     *
     * @param deviceIds 设备Id列表（按照顺序）
     * @return DeviceRpcVo列表
     */
    List<DeviceRpcVo> getDeviceRpcVoList(@Param("deviceIds") List<String> deviceIds);

    /**
     * 通过设备Id列表顺序获取DeviceRpcVo列表
     * 简化方法 只返回部分数据
     *
     * @param deviceIds 设备Id列表
     * @return DeviceRpcVo列表
     */
    List<DeviceRpcVo> getSimpleDeviceRpcVoList(@Param("deviceIds") List<String> deviceIds);


    /**
     * 通过设备ID列表获取设备名以及产品类型（获取拓扑设备接口用）
     *
     * @param deviceIds 设备ID列表
     * @return 拓扑设备Vo
     */
    List<DeviceTopologyVo> getDeviceNameAndProductTypeByDeviceIds(@Param("deviceIds") List<String> deviceIds);

    /**
     * 获取制定字段的map "deviceId" "productModel" "isOnline"
     *
     * @param deviceIds:
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * <AUTHOR>
     * @date 20:40 2022/7/28
     **/
    List<Map<String, Object>> listMapByIds(@Param("deviceIds") List<String> deviceIds);

    /**
     * 根据产品信息查询deviceId集合
     *
     * @param bindDeviceId         bindDeviceId
     * @param bindDeviceSn         已绑定设备SN
     * @param bindDeviceCategoryId 已绑定设备品类id
     * @param bindDeviceBrandId    已绑定设备品牌id
     * @param model                已绑定设备产品型号code
     * @param commodityModel       已绑定设备商品型号/Model #
     * @return deviceId集合
     */
    List<String> selectListDeviceIdByProductInfo(@Param("bindDeviceId") String bindDeviceId,
                                                 @Param("bindDeviceSn") String bindDeviceSn,
                                                 @Param("bindDeviceCategoryId") Long bindDeviceCategoryId,
                                                 @Param("bindDeviceBrandId") Long bindDeviceBrandId,
                                                 @Param("model") String model,
                                                 @Param("commodityModel") String commodityModel);

    /**
     * 根据产品id获得设备id集合
     * @param productId 产品id
     * @return deviceId集合
     */
    List<String> selectListDeviceIdByProductId(@Param("productId") Long productId);

    /**
     * 根据设备id集合查询设备产品信息
     *
     * @param deviceIds 设备id集合
     * @return 设备产品信息集合
     */
    List<DeviceProductDto> selectListDeviceProductByDeviceIds(@Param("deviceIds") List<String> deviceIds);

    /**
     * 从多码表修复设备sn
     */
    void fixMissingDeviceSnFromDeviceCode();
}
