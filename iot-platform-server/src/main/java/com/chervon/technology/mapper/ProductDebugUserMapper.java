package com.chervon.technology.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.technology.domain.dataobject.DebugDeviceUser;
import com.chervon.technology.domain.dto.productdebug.ProductDebugDto;
import com.chervon.technology.domain.vo.productdebug.DebugDeviceVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ProductDebugUserMapper extends BaseMapper<DebugDeviceUser> {
    /**
     * 列表
     * @param page 分页参数
     * @param search 查询参数
     * @param deviceIds 设备ID集合
     * @return 结果集
     */
    Page<DebugDeviceVo> selectByPage(@Param("page") Page page, @Param("search") ProductDebugDto search, @Param("deviceIds") List<String> deviceIds);
}
