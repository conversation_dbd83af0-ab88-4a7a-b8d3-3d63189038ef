package com.chervon.technology.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.technology.api.dto.ota.OtaResultDto;
import com.chervon.technology.api.vo.ota.ComponentResultVo;
import com.chervon.technology.api.vo.ota.OtaHistoryVo;
import com.chervon.technology.api.vo.ota.OtaResultItemVo;
import com.chervon.technology.domain.entity.OtaDeviceResult;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
public interface OtaDeviceResultMapper extends BaseMapper<OtaDeviceResult> {

    /**
     * 获取升级历史列表
     * <AUTHOR>
     * @date 14:56 2022/8/4
     * @param deviceId:
     * @return java.util.List<com.chervon.technology.api.vo.ota.OtaHistoryVo>
     **/
    List<OtaHistoryVo> getOtaHistory(@Param("deviceId") String deviceId, @Param("userId") Long userId);

    /**
     * 分页查询升级结果记录
     * <AUTHOR>
     * @date 15:26 2022/8/17
     * @param page:
     * @return com.chervon.common.core.domain.PageResult<com.chervon.technology.api.vo.ota.OtaResultItemVo>
     **/
    Page<OtaResultItemVo> pageOtaResult(@Param("page") Page<OtaResultItemVo> page, @Param("otaResultDto") OtaResultDto otaResultDto);

    /**
     * 获取总成零件升级结果
     * <AUTHOR>
     * @date 15:40 2022/8/17
     * @param deviceIds:
     * @return java.util.List<com.chervon.technology.api.vo.ota.ComponentResultVo>
     **/
    List<ComponentResultVo> getComponentResults(@Param("deviceIds") List<String> deviceIds, @Param("otaResultDto") OtaResultDto otaResultDto);
}
