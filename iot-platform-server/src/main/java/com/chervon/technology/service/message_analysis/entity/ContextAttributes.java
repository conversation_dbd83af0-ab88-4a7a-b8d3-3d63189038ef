package com.chervon.technology.service.message_analysis.entity;

import java.util.Arrays;

/**
    处理器上下文中属性信息
 */
public enum ContextAttributes {
    FaultMessageAlarmLogAddDto("FaultMessageAlarmLogAddDto","故障告警消息请求对象"),
    FaultMessage("FaultMessage","告警消息对象"),
    EventType("EventType","事件类型(info信息,warn告警,error故障)"),
    DeviceMessage("DeviceMessage","待发送的设备消息"),
    ;
    /**
     * 描述
     */
    private final String code;


    /**
     * 描述
     */
    private final String desc;

    ContextAttributes(String code, String desc) {
        this.code =code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    /**
     * 获取描述
     */
    public String getDesc() {
        return desc;
    }


    /**
     * 获取枚举
     */
    public static ContextAttributes getEnum(String name) {
        return Arrays.stream(values()).filter(x -> x.name().equals(name)).findFirst().orElse(null);
    }
}
