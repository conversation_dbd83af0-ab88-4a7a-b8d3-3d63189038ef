package com.chervon.technology.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.operation.api.RemoteMessageTemplateService;
import com.chervon.operation.api.dto.MessageTemplateDto;
import com.chervon.operation.api.vo.MessageTemplateBo;
import com.chervon.technology.api.enums.PushMethodEnum;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.domain.dataobject.*;
import com.chervon.technology.domain.dto.fault.message.*;
import com.chervon.technology.domain.vo.fault.message.FaultMessageDetailVo;
import com.chervon.technology.domain.vo.fault.message.FaultMessageVo;
import com.chervon.technology.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2022-09-08 10:26
 **/
@Slf4j
@Service
public class FaultMessageDraftServiceImpl
        implements FaultMessageDraftService {

    @Resource
    private FaultMessagePushMethodDraftService faultMessagePushMethodDraftService;
    @Resource
    private FaultMessagePushMethodService faultMessagePushMethodService;
    @DubboReference
    private RemoteMessageTemplateService remoteMessageTemplateService;

    /**
     * 根据根据故障消息Id删除触发器和关系和IOT触发器
     *
     * @param
     */
 /*   public void delFaultMessageRuleTriggerDraft(FaultMessageDraft faultMessageDraft) {
        LambdaQueryWrapper<FaultMessageTriggerDraft> wrapper = new LambdaQueryWrapper<FaultMessageTriggerDraft>()
                .eq(FaultMessageTriggerDraft::getFaultMessageId, faultMessageDraft.getId());
        List<FaultMessageTriggerDraft> list = faultMessageTriggerDraftService.list(wrapper);

        for (FaultMessageTriggerDraft faultMessageTriggerDraft : list) {
            //删除触发器
            triggerDraftService.removeById(faultMessageTriggerDraft.getTriggerId());
        }
        //根据故障消息Id删除所有关联触发器
        faultMessageTriggerDraftService.remove(wrapper);
    }*/




}
