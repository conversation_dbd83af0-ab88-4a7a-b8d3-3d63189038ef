package com.chervon.technology.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.technology.domain.dataobject.FaultMessageTrigger;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-09-08 10:28
 **/
public interface FaultMessageTriggerService extends IService<FaultMessageTrigger> {
    /**
     * 根据故障消息Id查询  故障触发器关系
     * @param faultMessageId
     * @return
     */
    List<FaultMessageTrigger> listByFaultMessageId(long faultMessageId);
}
