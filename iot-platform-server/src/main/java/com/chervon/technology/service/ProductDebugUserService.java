package com.chervon.technology.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.iot.middle.api.vo.device.IotDeviceCertVo;
import com.chervon.technology.domain.dataobject.DebugDeviceUser;
import com.chervon.technology.api.dto.SearchModelDebugDto;
import com.chervon.technology.domain.dto.productdebug.*;
import com.chervon.technology.domain.vo.productdebug.DebugDeviceVo;
import com.chervon.technology.domain.vo.productdebug.ModelVo;

/**
 * <AUTHOR>
 */
public interface ProductDebugUserService extends IService<DebugDeviceUser> {
    /**
     * 列表
     * @param search 参数
     * @return 结果集
     */
    PageResult<DebugDeviceVo> list(ProductDebugDto search);

    /**
     * 创建虚拟设备
     * @param productDebugAddDto 参数
     * @return 结果集
     */
    IotDeviceCertVo addVirtualDevice(ProductDebugAddDto productDebugAddDto);

    /**
     * 绑定
     * @param productDebugUserAddDto 参数
     */
    void add(ProductDebugUserAddDto productDebugUserAddDto);

    /**
     * 解绑
     * @param productDebugUserDeleteDto 参数
     */
    void delete(ProductDebugUserDeleteDto productDebugUserDeleteDto);

    /**
     * 调试列表
     * @param search 参数
     * @return 结果集
     */
    ModelVo model(SearchModelDebugDto search) throws Exception;


    JSONObject getThingModel(SearchModelDebugDto search);

    /**
     *  批量更新设备影子上报与下发
     *
     * @param productDebugIssueDto 参数
     */
    void updateDesired(ProductDebugIssueDto productDebugIssueDto);
}
