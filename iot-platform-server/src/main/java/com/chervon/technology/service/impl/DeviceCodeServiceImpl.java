package com.chervon.technology.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.DateTimeZoneUtil;
import com.chervon.common.core.utils.SnowFlake;
import com.chervon.common.redis.constant.RedisConstant;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.iot.middle.api.service.RemoteDeviceCertificateService;
import com.chervon.iot.middle.api.service.RemoteDeviceService;
import com.chervon.iot.middle.api.vo.device.DeviceCertificateVo;
import com.chervon.technology.api.dto.DeviceCodeAddDto;
import com.chervon.technology.api.dto.EditDeviceCodeStatusDto;
import com.chervon.technology.api.dto.SearchDeviceCodeDto;
import com.chervon.technology.api.enums.DeviceOnlineStatusEnum;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.api.vo.DeviceCodeExcel;
import com.chervon.technology.api.vo.DeviceCodeVo;
import com.chervon.technology.config.ExceptionMessageUtil;
import com.chervon.technology.config.IotPlatformCommon;
import com.chervon.technology.domain.bo.DictBo;
import com.chervon.technology.domain.bo.DictNodeBo;
import com.chervon.technology.domain.dataobject.Device;
import com.chervon.technology.domain.dataobject.DeviceCode;
import com.chervon.technology.domain.dataobject.Product;
import com.chervon.technology.domain.dataobject.ProductNetworkMode;
import com.chervon.technology.domain.dto.DeviceUpdateDto;
import com.chervon.technology.domain.dto.device.DeviceCodeRead;
import com.chervon.technology.domain.enums.ProductTypeEnum;
import com.chervon.technology.mapper.DeviceCodeMapper;
import com.chervon.technology.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.sql.Timestamp;
import java.sql.Wrapper;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chervon.common.core.constant.CommonConstant.TEXT_SIZE;

/**
 * <AUTHOR>
 * @date 20220425
 * 品牌写服务
 */
@Service
@Slf4j
public class DeviceCodeServiceImpl extends ServiceImpl<DeviceCodeMapper, DeviceCode> implements DeviceCodeService {
    public static final String NORM_DATE_PATTERN = "yyyy-MM-dd";
    private static final String MULTI_CODE_STATUS = "multiCodeStatus";
    @Autowired
    private DeviceCodeMapper deviceCodeMapper;
    @Lazy
    @Autowired
    private ProductService productService;
    @Resource
    private DeviceService deviceService;
    @DubboReference
    private RemoteDeviceService remoteDeviceService;
    @Autowired
    private DictService dictService;
    @Autowired
    private ProductNetworkModeService productNetworkModeService;
    @DubboReference
    private RemoteDeviceCertificateService remoteDeviceCertificateService;

    @Override
    public DeviceCode findNormalOneByDeviceId(String deviceId) {
        LambdaQueryWrapper<DeviceCode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceCode::getDeviceId, deviceId);
        queryWrapper.eq(DeviceCode::getStatus, CommonConstant.ONE);
        return deviceCodeMapper.selectOne(queryWrapper);
    }

    @Override
    public DeviceCode getByDeviceId(String deviceId) {
        LambdaQueryWrapper<DeviceCode> wrapper = new LambdaQueryWrapper<DeviceCode>()
                .eq(DeviceCode::getDeviceId, deviceId);
        return this.getOne(wrapper);
    }

    @Override
    public DeviceCode getBySn(String sn) {
        LambdaQueryWrapper<DeviceCode> wrapper = new LambdaQueryWrapper<DeviceCode>()
                .eq(DeviceCode::getSn, sn);
        return this.getOne(wrapper);
    }

    @Override
    public PageResult<DeviceCodeVo> getListDevice(SearchDeviceCodeDto searchDevice) {
        LambdaQueryWrapper<DeviceCode> queryWrapper = new LambdaQueryWrapper<DeviceCode>()
                .eq(DeviceCode::getProductSnCode, searchDevice.getProductSnCode())
                .like(StringUtils.isNotEmpty(searchDevice.getDeviceId()), DeviceCode::getDeviceId, searchDevice.getDeviceId())
                .like(StringUtils.isNotEmpty(searchDevice.getIccid()), DeviceCode::getIccid, searchDevice.getIccid())
                .like(StringUtils.isNotEmpty(searchDevice.getSn()), DeviceCode::getSn, searchDevice.getSn())
                .like(StringUtils.isNotEmpty(searchDevice.getMoCode()), DeviceCode::getMoCode, searchDevice.getMoCode())
                .like(StringUtils.isNotEmpty(searchDevice.getMes()), DeviceCode::getMes, searchDevice.getMes())
                .like(StringUtils.isNotEmpty(searchDevice.getItemCode()), DeviceCode::getItemCode, searchDevice.getItemCode())
                .ge(StringUtils.isNotBlank(searchDevice.getCreateStartTime()), DeviceCode::getCreateTime, searchDevice.getCreateStartTime())
                .le(StringUtils.isNotBlank(searchDevice.getCreateEndTime()), DeviceCode::getCreateTime, searchDevice.getCreateEndTime())
                .ge(StringUtils.isNotBlank(searchDevice.getUpdateStartTime()), DeviceCode::getUpdateTime, searchDevice.getUpdateStartTime())
                .le(StringUtils.isNotBlank(searchDevice.getUpdateEndTime()), DeviceCode::getUpdateTime, searchDevice.getUpdateEndTime())
                .ge(StringUtils.isNotBlank(searchDevice.getProductionStartDate()), DeviceCode::getProductionDate, searchDevice.getProductionStartDate())
                .le(StringUtils.isNotBlank(searchDevice.getProductionEndDate()), DeviceCode::getProductionDate, searchDevice.getProductionEndDate())
                .eq(null != searchDevice.getStatus(), DeviceCode::getStatus, searchDevice.getStatus())
                .orderByDesc(DeviceCode::getCreateTime);
        Page<DeviceCode> page = deviceCodeMapper.selectPage(new Page<>(searchDevice.getPageNum(),
                searchDevice.getPageSize()), queryWrapper);

        PageResult<DeviceCodeVo> res = new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal());

        page.getRecords().forEach(e -> {
            DeviceCodeVo deviceCodeVo = ConvertUtil.convert(e, DeviceCodeVo.class);
            if (null != e.getProductionDate()) {
                Timestamp timestamp = new Timestamp(e.getProductionDate().atStartOfDay(ZoneOffset.ofHours(8)).toInstant()
                        .toEpochMilli());
                deviceCodeVo.setProductionDate(timestamp);
            }
            res.getList().add(deviceCodeVo);
        });

        return res;
    }

    @Override
    public List<String> getDeviceIds(String sncode) {
        return list(Wrappers.<DeviceCode>lambdaQuery().eq(DeviceCode::getProductSnCode, sncode))
                .stream()
                .map(DeviceCode::getDeviceId)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getSns(String sncode) {
        return list(Wrappers.<DeviceCode>lambdaQuery().eq(DeviceCode::getProductSnCode, sncode))
                .stream()
                .map(DeviceCode::getSn)
                .collect(Collectors.toList());
    }

    /**
     * 从sn中截取snCode,兼容IOT设备以及非IOT设备
     *
     * @param sn SN
     * @return SnCode
     */
    private String getSnCodeFromSn(String sn) {
        if (sn.matches(IotPlatformCommon.DEVICE_CODE_CHECK)) {
            // 15位多码
            return sn.substring(1, 5);
        } else if (sn.matches(IotPlatformCommon.NO_IOT_DEVICE_CODE_CHECK)) {
            // 16位，R开头的非IOT设备多码
            return sn.substring(2, 6);
        } else {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_CODE_SN_ERROR, sn);
        }
    }

    @Override
    public void addDeviceCode(DeviceCodeAddDto deviceCodeAdd) {
        // 判断字符长度是否合法
        if (deviceCodeAdd.getDeviceId().length() > TEXT_SIZE) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ID_ERROR);
        }
        if (deviceCodeAdd.getSn().length() > TEXT_SIZE) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_SN_ERROR);
        }
        if (deviceCodeAdd.getMoCode().length() > TEXT_SIZE) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_MOCODE_ERROR);
        }
        if (deviceCodeAdd.getItemCode().length() > TEXT_SIZE) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_ITEM_CODE_ERROR);
        }
        if (deviceCodeAdd.getMes().length() > TEXT_SIZE) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_MES_ERROR);
        }
        if (StringUtils.isEmpty(deviceCodeAdd.getProductSnCode())) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_PRODUCT_SN_CODE_CANT_BE_NULL);
        }
        DeviceCode deviceCode = this.getOne(new LambdaQueryWrapper<DeviceCode>().eq(DeviceCode::getDeviceId, deviceCodeAdd.getDeviceId()));
        if (null != deviceCode) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ID_ALREADY_EXIST, deviceCodeAdd.getDeviceId());
        }
        // 查看设备的SN是否存在
        deviceCode = this.getBySn(deviceCodeAdd.getSn());
        if (null != deviceCode) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_SN_ALREADY_EXIST, deviceCodeAdd.getSn());
        }
        String snCodeFromSn = getSnCodeFromSn(deviceCodeAdd.getSn());
        // 校验Sn的2-5位是否等于SnCode
        if (!Objects.equals(deviceCodeAdd.getProductSnCode(), snCodeFromSn)) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_CODE_SN_OR_SNCODE_ERROR, deviceCodeAdd.getSn(),
                    deviceCodeAdd.getProductSnCode());
        }
        deviceCode = ConvertUtil.convert(deviceCodeAdd, DeviceCode.class);
        deviceCode.setProductionDate(deviceCodeAdd.getProductionDate().toInstant().atZone(ZoneId.systemDefault())
                .toLocalDate());
        deviceCode.setStatus(CommonConstant.ONE);
        this.save(deviceCode);
        //异步申请证书
        asyncApplyDeviceCertificate(Arrays.asList(deviceCode));

        // 更新Device表SN
        Device device = deviceService.getOne(new LambdaQueryWrapper<Device>().eq(Device::getDeviceId, deviceCodeAdd.getDeviceId()));
        if (null != device) {
            deviceService.updateDeviceSnByDeviceId(deviceCodeAdd.getDeviceId(), deviceCodeAdd.getSn());
        }
    }

    /**
     * 异步申请证书
     * @param listDeviceCode
     */
    @Override
    public void asyncApplyDeviceCertificate(List<DeviceCode> listDeviceCode) {
        final List<String> listNeedApplyDevice = getNeedApplyCertificateDeviceId(listDeviceCode);
        if (CollectionUtils.isEmpty(listDeviceCode)){
            return;
        }
        remoteDeviceCertificateService.asyncApplyForCertificate(listNeedApplyDevice);
    }

    /**
     * 同步申请证书并返回证书信息
     * @param listDeviceCode
     * @return
     */
    @Override
    public List<DeviceCertificateVo> applyDeviceCertificate(List<DeviceCode> listDeviceCode) {
        final List<String> listNeedApplyDevice = getNeedApplyCertificateDeviceId(listDeviceCode);
        if (CollectionUtils.isEmpty(listDeviceCode)){
            return new ArrayList<>();
        }
        return remoteDeviceCertificateService.applyForCertificate(listNeedApplyDevice);
    }

    @Override
    public List<String> getNeedApplyCertificateDeviceId(List<DeviceCode> listDeviceCode) {
        final List<String> listSnCode = listDeviceCode.stream().map(a -> a.getProductSnCode()).collect(Collectors.toList());
        final List<Product> listProduct = productService.listBySnCode(listSnCode);
        if(CollectionUtils.isEmpty(listProduct)){
            return null;
        }
        final List<Long> listPid = listProduct.stream().map(Product::getId).collect(Collectors.toList());
        List<ProductNetworkMode> productNetModes = productNetworkModeService.getModeInPIds(listPid);
        if(CollectionUtils.isEmpty(productNetModes)){
            return null;
        }
        //需要申请证书的产品信息
        List<Product> listNeedApplyProduct=new ArrayList<>();
        //将需要申请证书的产品添加到列表
        for(Product product:listProduct){
            final List<ProductNetworkMode> productNetworkModes = productNetModes.stream().filter(p -> p.getProductId().equals(product.getId())).collect(Collectors.toList());
            final boolean match = productNetworkModes.stream().anyMatch(a -> a.getNetworkModeCode().contains("wifi") || a.getNetworkModeCode().contains("4G"));
            if(match){ //wifi/4G联网方式设备才需申请证书
                listNeedApplyProduct.add(product);
            }
        }
        //需要申请证书的产品列表为空，直接返回无需申请
        if(CollectionUtils.isEmpty(listNeedApplyProduct)){
            return null;
        }
        //需要申请证书的设备列表
        List<String> listNeedApplyDevice=new ArrayList<>();
        for(DeviceCode deviceCode: listDeviceCode){
            final boolean match = listNeedApplyProduct.stream().anyMatch(a -> a.getProductSnCode().equals(deviceCode.getProductSnCode()));
            if(match){
                listNeedApplyDevice.add(deviceCode.getDeviceId());
            }
        }
        //没有要申请证书的设备，直接返回
        if(CollectionUtils.isEmpty(listNeedApplyDevice)){
            return null;
        }
        return listNeedApplyDevice;
    }

    @Override
    public boolean isNeedApplyCertificateByPid(Long productId) {
        final List<Long> listPid = Arrays.asList(productId);
        List<ProductNetworkMode> productNetModes = productNetworkModeService.getModeInPIds(listPid);
        if(CollectionUtils.isEmpty(productNetModes)){
            return false;
        }
        final boolean match = productNetModes.stream().anyMatch(a -> a.getNetworkModeCode().contains("wifi") || a.getNetworkModeCode().contains("4G"));
        if(match){ //wifi/4G联网方式设备才需申请证书
            return true;
        }
        return false;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> uploadDeviceCodeFile(byte[] deviceCodeFileArr, Long productId) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<Product>().eq(Product::getId, productId);
        Product product = productService.getOne(wrapper);
        if (null == product.getProductSnCode()) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_CODE_IMPORT_SN_CODE_NOT_EXISTS);
        }
        String productSnCode = product.getProductSnCode();

        List<DeviceCodeRead> importExcel;
        InputStream inputStream;
        //将字节转换为流
        inputStream = new ByteArrayInputStream(deviceCodeFileArr);
        try {
            importExcel = EasyExcel.read(inputStream).head(DeviceCodeRead.class).sheet().doReadSync();
        } catch (Exception e) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_CODE_IMPORT_ERROR);
        }
        if (CollectionUtils.isEmpty(importExcel)) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_CODE_IMPORT_ERROR);
        }
        if (importExcel.size() > IotPlatformCommon.FIVE_THOUSAND) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_CODE_IMPORT_MORE_THAN_5000, importExcel.size());
        }

        // 获取多码表中的所有deviceId列表和SN列表,用于判断是否重复
        Set<String> deviceIdSet = new HashSet<>(this.getDeviceIds(productSnCode));
        Set<String> snSet = new HashSet<>(this.getSns(productSnCode));
        // result用于记录导入中的错误，如果为空则说明导入成功
        List<String> result = new ArrayList<>();

        int index = 2;
        List<DeviceCode> deviceCodes = new ArrayList<>();
        for (DeviceCodeRead deviceCode : importExcel) {
            // 1.判断是否空值
            if (StringUtils.isEmpty(deviceCode.getDeviceId())) {
                result.add("第" + index + "行,【设备ID】不能为空");
            } else if (deviceCode.getDeviceId().length() > TEXT_SIZE) {
                result.add("第" + index + "行,【设备ID】内容超过" + TEXT_SIZE + "个字符");
            }
            if (StringUtils.isEmpty(deviceCode.getSn())) {
                result.add("第" + index + "行,【设备SN】不能为空");
            } else if (deviceCode.getSn().length() > TEXT_SIZE) {
                result.add("第" + index + "行,【设备SN】内容超过" + TEXT_SIZE + "个字符");
            } else {
                // 判断SN是否符合正则校验且2-5(3-6)位能对上数据库
                if (deviceCode.getSn().matches(IotPlatformCommon.DEVICE_CODE_CHECK)) {
                    if (!deviceCode.getSn().substring(1, 5).equals(productSnCode)) {
                        // 15位多码
                        result.add("第" + index + "行,【设备SN】与【ProductSnCode】不符合对应规则");
                    }
                } else if (deviceCode.getSn().matches(IotPlatformCommon.NO_IOT_DEVICE_CODE_CHECK)) {
                    if (!deviceCode.getSn().substring(2, 6).equals(productSnCode)) {
                        // 16位，R开头的非IOT设备多码
                        result.add("第" + index + "行,【设备SN】与【ProductSnCode】不符合对应规则");
                    }
                } else {
                    result.add("第" + index + "行,【设备SN】不符合正则校验");
                }
            }
            if (StringUtils.isEmpty(deviceCode.getMoCode())) {
                result.add("第" + index + "行,【MO code】不能为空");
            } else if (deviceCode.getMoCode().length() > TEXT_SIZE) {
                result.add("第" + index + "行,【MO code】内容超过" + TEXT_SIZE + "个字符");
            }
            if (StringUtils.isEmpty(deviceCode.getMes())) {
                result.add("第" + index + "行,【MES #】不能为空");
            } else if (deviceCode.getMes().length() > TEXT_SIZE) {
                result.add("第" + index + "行,【MES #】内容超过" + TEXT_SIZE + "个字符");
            }
            if (StringUtils.isEmpty(deviceCode.getItemCode())) {
                result.add("第" + index + "行,【ITEM code】不能为空");
            } else if (deviceCode.getItemCode().length() > TEXT_SIZE) {
                result.add("第" + index + "行,【ITEM code】内容超过" + TEXT_SIZE + "个字符");
            }
            if (StringUtils.isEmpty(deviceCode.getProductionDate())) {
                result.add("第" + index + "行,【Production Date】不能为空");
            }
            // 2.判断数据合法性
            if (deviceIdSet.contains(String.valueOf(deviceCode.getDeviceId()))) {
                result.add("第" + index + "行,【设备ID】已存在");
            }
            if (snSet.contains(String.valueOf(deviceCode.getSn()))) {
                result.add("第" + index + "行,【设备SN】已存在");
            }
            LocalDate localDate = null;
            try {
                String productionDate = deviceCode.getProductionDate();
                localDate = LocalDate.parse(productionDate, DateTimeFormatter.ofPattern(NORM_DATE_PATTERN));
            } catch (Exception e) {
                result.add("第" + index + "行,【ProductDate】日期非法");
            }

            index += 1;
            deviceIdSet.add(deviceCode.getDeviceId());
            snSet.add(deviceCode.getSn());

            // 3.添加到DeviceCode列表
            DeviceCode code = new DeviceCode(
                    deviceCode.getDeviceId(),
                    deviceCode.getSn(),
                    productSnCode,
                    deviceCode.getMes(),
                    deviceCode.getMoCode(),
                    deviceCode.getItemCode(),
                    localDate,
                    CommonConstant.ONE);
            deviceCodes.add(code);
        }
        if (result.isEmpty()) {
            this.saveBatch(deviceCodes);
            //反向更新device表中的sn
            List<DeviceUpdateDto> deviceUpdateDtos = deviceCodes.stream().map(x -> DeviceUpdateDto.builder().deviceId(x.getDeviceId()).sn(x.getSn()).build()).collect(Collectors.toList());
            deviceService.batchUpdateDeviceSnByDeviceId(deviceUpdateDtos);
            //异步申请证书
            asyncApplyDeviceCertificate(deviceCodes);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editDeviceCodeStatus(EditDeviceCodeStatusDto editDeviceCodeStatusDto) {
        LambdaQueryWrapper<DeviceCode> wrapper = new LambdaQueryWrapper<DeviceCode>()
                .eq(DeviceCode::getDeviceId, editDeviceCodeStatusDto.getDeviceId());
        DeviceCode deviceCode = this.getOne(wrapper);
        if (null == deviceCode) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_CODE_NOT_EXIST, editDeviceCodeStatusDto.getDeviceId());
        }
        deviceCode.setStatus(editDeviceCodeStatusDto.getStatus());
        Set<String> disCards = RedisUtils.getCacheSet(RedisConstant.DEVICE_CODE_DISCARD);
        if (CommonConstant.ZERO.equals(editDeviceCodeStatusDto.getStatus())) {
            if (CollectionUtils.isEmpty(disCards)) {
                disCards = new HashSet<>();
            }
            disCards.add(editDeviceCodeStatusDto.getDeviceId());
            RedisUtils.setCacheSet(RedisConstant.DEVICE_CODE_DISCARD, disCards);
            // 废弃了，直接把设备的在线状态置为离线
            deviceService.update(new Device(), new LambdaUpdateWrapper<Device>()
                    .set(Device::getIsOnline, DeviceOnlineStatusEnum.OFFLINE.getValue())
                    .eq(Device::getDeviceId, editDeviceCodeStatusDto.getDeviceId()));
        } else {
            if (!CollectionUtils.isEmpty(disCards)) {
                disCards.remove(editDeviceCodeStatusDto.getDeviceId());
                RedisUtils.setCacheSet(RedisConstant.DEVICE_CODE_DISCARD, disCards);
            }

        }
        this.updateById(deviceCode);
        //如果设备已经被注册过则调用中台禁用对应设备证书
        Device device = deviceService.getOne(new LambdaQueryWrapper<Device>().eq(Device::getDeviceId, deviceCode.getDeviceId()).select(Device::getId, Device::getProductId));
        if (null != device) {
            Product product = productService.getOne(new LambdaQueryWrapper<Product>().eq(Product::getId, device.getProductId()).select(Product::getId, Product::getProductType));
            if (product != null && !Objects.equals(product.getProductType(), ProductTypeEnum.NOT_IOT_DEVICE.getValue())) {
                remoteDeviceService.updateCertStatus(editDeviceCodeStatusDto.getDeviceId(),
                        editDeviceCodeStatusDto.getStatus().equals(CommonConstant.ONE));
            }
        }
    }

    @Override
    public List<DeviceCodeExcel> export(SearchDeviceCodeDto searchDevice) {
        LambdaQueryWrapper<DeviceCode> queryWrapper = new LambdaQueryWrapper<DeviceCode>()
                .eq(DeviceCode::getProductSnCode, searchDevice.getProductSnCode())
                .like(StringUtils.isNotEmpty(searchDevice.getDeviceId()), DeviceCode::getDeviceId, searchDevice.getDeviceId())
                .like(StringUtils.isNotEmpty(searchDevice.getSn()), DeviceCode::getSn, searchDevice.getSn())
                .like(StringUtils.isNotEmpty(searchDevice.getMoCode()), DeviceCode::getMoCode, searchDevice.getMoCode())
                .like(StringUtils.isNotEmpty(searchDevice.getMes()), DeviceCode::getMes, searchDevice.getMes())
                .like(StringUtils.isNotEmpty(searchDevice.getItemCode()), DeviceCode::getItemCode, searchDevice.getItemCode())
                .ge(StringUtils.isNotBlank(searchDevice.getCreateStartTime()), DeviceCode::getCreateTime, searchDevice.getCreateStartTime())
                .le(StringUtils.isNotBlank(searchDevice.getCreateEndTime()), DeviceCode::getCreateTime, searchDevice.getCreateEndTime())
                .ge(StringUtils.isNotBlank(searchDevice.getUpdateStartTime()), DeviceCode::getUpdateTime, searchDevice.getUpdateStartTime())
                .le(StringUtils.isNotBlank(searchDevice.getUpdateEndTime()), DeviceCode::getUpdateTime, searchDevice.getUpdateEndTime())
                .ge(StringUtils.isNotBlank(searchDevice.getProductionStartDate()), DeviceCode::getProductionDate, searchDevice.getProductionStartDate())
                .le(StringUtils.isNotBlank(searchDevice.getProductionEndDate()), DeviceCode::getProductionDate, searchDevice.getProductionEndDate())
                .eq(null != searchDevice.getStatus(), DeviceCode::getStatus, searchDevice.getStatus())
                .orderByDesc(DeviceCode::getCreateTime);
        List<DeviceCode> list = deviceCodeMapper.selectList(queryWrapper);

        // 字典
        List<DictBo> dictList = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(), Collections.singletonList(MULTI_CODE_STATUS));
        Map<String, DictBo> collect = dictList.stream().collect(Collectors.toMap(DictBo::getDictName, Function.identity()));
        List<DeviceCodeExcel> data = list.stream().map(e -> {
            DeviceCodeExcel excel = new DeviceCodeExcel();
            BeanUtils.copyProperties(e, excel);
            if (e.getProductionDate() != null) {
                excel.setProductionDate(e.getProductionDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            }
            // 设置状态
            excel.setStatus(collect.get(MULTI_CODE_STATUS).getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), e.getStatus() + ""))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());

            excel.setCreateTime(DateTimeZoneUtil.format(e.getCreateTime(), searchDevice.getZone()));
            excel.setUpdateTime(DateTimeZoneUtil.format(e.getUpdateTime(), searchDevice.getZone()));
            return excel;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(data)) {
            data.add(new DeviceCodeExcel());
        }
        return data;
    }

    @Override
    public String createDeviceCodeBySn(String sn, String snCode) {
        DeviceCode deviceCode = new DeviceCode();
        deviceCode.setSn(sn);
        deviceCode.setProductSnCode(snCode);
        deviceCode.setDeviceId(String.valueOf(SnowFlake.nextId()));
        this.save(deviceCode);
        return deviceCode.getDeviceId();
    }

    @Override
    public void initDeviceCodeDiscard() {
        LambdaQueryWrapper<DeviceCode> wrapper = new LambdaQueryWrapper<DeviceCode>().
                eq(DeviceCode::getStatus, CommonConstant.ZERO)
                .select(DeviceCode::getDeviceId);
        List<Object> objects = this.listObjs(wrapper);
        if (CollectionUtils.isEmpty(objects)) {
            return;
        }
        List<String> deviceIds = (List<String>) (List) objects;
        RedisUtils.setCacheSet(RedisConstant.DEVICE_CODE_DISCARD, new HashSet<>(deviceIds));
    }

}
