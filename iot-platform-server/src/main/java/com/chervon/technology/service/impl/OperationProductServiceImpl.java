package com.chervon.technology.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.MultiLanguageVo;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.enums.ApplicationEnum;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.RsaUtils;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.common.oss.uitl.UrlUtil;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.iot.app.api.enums.BusinessTypeEnum;
import com.chervon.operation.api.RemoteProductReleaseService;
import com.chervon.technology.api.dto.*;
import com.chervon.technology.api.enums.ProductCreateTypeEnum;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.api.vo.*;
import com.chervon.technology.config.ExceptionMessageUtil;
import com.chervon.technology.config.IotPlatformCommon;
import com.chervon.technology.config.MultiLanguageUtil;
import com.chervon.technology.domain.dataobject.*;
import com.chervon.technology.domain.vo.product.OperationProductMergeBaseVo;
import com.chervon.technology.mapper.ProductMapper;
import com.chervon.technology.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import static com.chervon.technology.api.dto.ProductReleaseEnum.*;

/**
 * <AUTHOR>
 * @date 2022-07-26
 */
@EnableConfigurationProperties(AwsProperties.class)
@Service
@Slf4j
public class OperationProductServiceImpl
        implements OperationProductService {

    @Autowired
    private ProductService productService;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private ProductNetworkModeService networkModeService;

    @DubboReference
    private RemoteProductReleaseService remoteProductReleaseService;

    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;

    @Autowired
    private AwsProperties awsProperties;

    @Override
    public void deleteIndependentProduct(Long pId) {
        // 根据产品Id获取产品信息
        Product product = productService.checkExistAndGetProductById(pId);
        //运营平台不能删除IoT产品
        if (ProductCreateTypeEnum.IOT_PLATFORM.getType().equals(product.getCreateType())) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_PRODUCT_DELETE_INDEPENDENT, pId);
        }
        if(RELEASED.getValue().equals(product.getReleaseStatus())||OFF_RELEASED.getValue().equals(product.getReleaseStatus())){
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_PRODUCT_ALREADY_RELEASE_CAN_NOT_DELETE, pId);
        }
        if (StringUtils.isNotEmpty(product.getProductName())) {
            // 清理多语言
            remoteMultiLanguageService.deleteByLangIds(Arrays.asList(Long.valueOf(product.getProductName())));
        }
        productService.removeById(pId);
    }

    private void updateMultiLanguage(OperationEditProductDto editProductDto, boolean ifFirstDraft) {
        // 创建或者更新多语言
        Map<String, String> createMap = new HashMap<>();
        Map<Long, String> updateMap = new HashMap<>();
        if (ifFirstDraft) {
            createMap.put(String.valueOf(CommonConstant.ONE), editProductDto.getProductName().getMessage());
        } else {
            if (null != editProductDto.getProductName() && editProductDto.getProductName().getLangId() == null) {
                createMap.put(String.valueOf(CommonConstant.ONE), editProductDto.getProductName().getMessage());
            } else if (null != editProductDto.getProductName() && editProductDto.getProductName().getLangId() != null) {
                updateMap.put(editProductDto.getProductName().getLangId(),
                        editProductDto.getProductName().getMessage());
            }
        }
        if (!CollectionUtils.isEmpty(createMap)) {
            Map<String, MultiLanguageBo> creates = remoteMultiLanguageService.simpleCreateMultiLanguages(IotPlatformCommon.APPLICATION_NAME,
                    createMap, LocaleContextHolder.getLocale().getLanguage());
            if (null != creates.get(String.valueOf(CommonConstant.ONE))) {
                MultiLanguageVo vo = new MultiLanguageVo(creates.get(String.valueOf(CommonConstant.ONE)).getLangId(),
                        editProductDto.getProductName().getMessage());
                editProductDto.setProductName(vo);
            }
        }
        if (!CollectionUtils.isEmpty(updateMap)) {
            remoteMultiLanguageService.simpleUpdateMultiLanguages(IotPlatformCommon.APPLICATION_NAME,
                    updateMap, LocaleContextHolder.getLocale().getLanguage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(OperationEditProductDto editProductDto) {
        Product product = productService.checkExistAndGetProductById(editProductDto.getPId());
        // 校验商品型号
        productService.checkProductExistedByCommodityModelAndPId(editProductDto.getCommodityModel(),editProductDto.getPId());
        //TODO flynn ??
        updateMultiLanguage(editProductDto, false);
        Product edit = ConvertUtil.convert(editProductDto, Product.class);
        // 判断变化的SnCode是否已存在
        if (!StringUtils.equals(product.getProductSnCode(), editProductDto.getProductSnCode())&&productService.countBySnCode(edit.getProductSnCode()) > 0) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_PRODUCT_SNCODE_DUPLICATED, editProductDto.getPId());
        }
        edit.setProductName(String.valueOf(editProductDto.getProductName().getLangId()));
        if (!CollectionUtils.isEmpty(editProductDto.getQuestionTemplate())) {
            edit.setQuestionTemplate(String.join(",", editProductDto.getQuestionTemplate()));
        }
        edit.setId(editProductDto.getPId());
        productService.updateById(edit);
    }




    @Override
    public PageResult<OperationProductVo> getOperationProductPageList(ProductOperationSearchDto search) {
        // 获取所有的Id
        List<Long> nameLangIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(search.getProductName())) {
            nameLangIds = getProductNameLangIds(search.getProductName());
            if (CollectionUtils.isEmpty(nameLangIds)) {
                return new PageResult<>(search.getPageNum(), search.getPageSize());
            }
        }
        //分页查询运营产品接口
        Page<Product> page =productService.page(new Page<>(search.getPageNum()
                        , search.getPageSize()),
                Wrappers.<Product>lambdaQuery()
                        .ge(StringUtils.isNotBlank(search.getCreateStartTime()), Product::getCreateTime, search.getCreateStartTime())
                        .le(StringUtils.isNotBlank(search.getCreateEndTime()), Product::getCreateTime, search.getCreateEndTime())
                        .ge(StringUtils.isNotBlank(search.getUpdateStartTime()), Product::getUpdateTime, search.getUpdateStartTime())
                        .le(StringUtils.isNotBlank(search.getUpdateEndTime()), Product::getUpdateTime, search.getUpdateEndTime())
                        .eq(Objects.nonNull(search.getCategoryId()),Product::getCategoryId,search.getCategoryId())
                        .eq(Objects.nonNull(search.getBrandId()),Product::getBrandId,search.getBrandId())
                        .eq(StringUtils.isNotBlank(search.getProductType()),Product::getProductType,search.getProductType())
                        .eq(StringUtils.isNotBlank(search.getReleaseStatus()),Product::getReleaseStatus,search.getReleaseStatus())
                        .like(StringUtils.isNotBlank(search.getId()),Product::getId,search.getId())
                        .like(StringUtils.isNotBlank(search.getModel()),Product::getModel,search.getModel())
                        .like(StringUtils.isNotBlank(search.getCommodityModel()),Product::getCommodityModel,search.getCommodityModel())
                        .like(StringUtils.isNotBlank(search.getProductSnCode()),Product::getProductSnCode,search.getProductSnCode())
                        .in(!CollectionUtils.isEmpty(nameLangIds),Product::getProductName,nameLangIds)
                        .orderByDesc(Product::getUpdateTime)
                        .orderByAsc(Product::getAppShowOrder)
        );

        PageResult<OperationProductVo> res = new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal());
        if (page.getTotal() < CommonConstant.ONE) {
            return res;
        }
        //TODO 多语言优化项-查询产品多语言
        List<String> langIds = page.getRecords().stream().map(Product::getProductName).collect(Collectors.toList());
        //转化为Map
        Map<Long, String> langMaps = remoteMultiLanguageService.listByIds(langIds).stream().collect(Collectors.toMap(MultiLanguageBo::getLangId,MultiLanguageBo::getLangCode));

        page.getRecords().forEach(product -> {
            OperationProductVo productVo = ConvertUtil.convert(product, OperationProductVo.class);
            String productName = product.getProductName();
            productVo.setQuestionTemplate(Arrays.asList(product.getQuestionTemplate().split(",")));

            if (!StringUtils.isEmpty(productName)) {
                String langCode = langMaps.get(Long.valueOf(productName));
                if (StringUtils.isNotEmpty(langCode)) {
                    productVo.setProductNameLanguage(com.chervon.technology.config.MultiLanguageUtil.getByLangCode(langCode, LocaleContextHolder.getLocale().getLanguage()));
                }
            }
            productVo.setProductIcon(getFileUrl(productVo.getProductIcon()));
            res.getList().add(productVo);
        });
        return res;
    }


    private List<Long> getProductNameLangIds(String name) {
        // 获取所有的Id
        List<Long> nameLangIds = new ArrayList<>();
        Map<String, List<Long>> query = new HashMap<>();
        //TODO 多语言优化项-分页里套全表查
        List<Long> langIds = productService.list().stream().map(Product::getProductName).filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList());
        query.put(name, langIds);
        Map<String, List<MultiLanguageBo>> names = remoteMultiLanguageService.listByTextLike(query,
                LocaleContextHolder.getLocale().getLanguage());
        if (CollectionUtils.isEmpty(names) || CollectionUtils.isEmpty(names.get(name))) {
            return null;
        }
        // 获取所有的Id
        List<MultiLanguageBo> languageBoList = names.get(name);
        languageBoList.forEach(multiLanguageBo -> nameLangIds.add(multiLanguageBo.getLangId()));
        return nameLangIds;
    }



    private String getFileUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return null;
        }
        return UrlUtil.completeUrl(awsProperties.getPictureBucket().getCdnHost(), url);
    }

    @Override
    public OperationProductVo getDetailByPid(Long pId) {
        //检查并获取产品
        Product operationVo = productService.checkExistAndGetProductById(pId);
        OperationProductVo product = ConvertUtil.convert(operationVo, OperationProductVo.class);
        if (StringUtils.isNotEmpty(operationVo.getQuestionTemplate())) {
            product.setQuestionTemplate(Arrays.asList(operationVo.getQuestionTemplate().split(",")));
        }
        List<String> langIds = new ArrayList<>();
        if (!StringUtils.isEmpty(product.getProductName())) {
            langIds.add(product.getProductName());
        }
        if (!CollectionUtils.isEmpty(langIds)) {
            List<MultiLanguageBo> langCodes = remoteMultiLanguageService.listByIds(langIds);
            // 转化为Map
            Map<Long, String> langMaps = new HashMap<>(langCodes.size());
            langCodes.forEach(code -> langMaps.put(code.getLangId(), code.getLangCode()));
            if (!StringUtils.isEmpty(product.getProductName())) {
                String langCode = langMaps.get(Long.valueOf(product.getProductName()));
                if (StringUtils.isNotEmpty(langCode)) {
                    product.setProductNameLanguage(MultiLanguageUtil.getByLangCode(langCode, LocaleContextHolder.getLocale().getLanguage()));
                }
            }
        }
        product.setProductIcon(getFileUrl(product.getProductIcon()));
        List<String> modes = networkModeService.getNetworkCodeByPid(product.getId());
        product.setNetworkModes(modes);
        return product;
    }

    @Override
    public List<OperationProductBaseVo> getByPIds(List<Long> pIds) {
        List<OperationProductMergeBaseVo> mergeBaseVoList = productMapper.getOperationProductBase(pIds);
        if (CollectionUtils.isEmpty(mergeBaseVoList)) {
            return null;
        }
        // 合并数据
        List<OperationProductBaseVo> list = new ArrayList<>(mergeBaseVoList.size());
        mergeBaseVoList.forEach(mergeBase -> {
            OperationProductBaseVo vo;
            if (null != mergeBase.getDraftId()) {
                vo = new OperationProductBaseVo(mergeBase.getPId(), mergeBase.getDraftBrandId(),
                        mergeBase.getDraftCommodityModel());
            } else {
                vo = new OperationProductBaseVo(mergeBase.getPId(), mergeBase.getBrandId(), mergeBase.getCommodityModel());
            }
            list.add(vo);
        });
        return list;
    }

    @Override
    public Boolean ifBrandUsed(Long brandId) {
        // 需要判断产品表和运营产品草稿表数据
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getBrandId, brandId);
        Long count = productMapper.selectCount(wrapper);
        if (count > 0) {
            return true;
        }
        return false;
    }

    @Override
    public Boolean ifCategoryUsed(Long categoryId) {
        // 需要同事判断产品表
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getCategoryId, categoryId);
        Long count = productMapper.selectCount(wrapper);
        if (count > CommonConstant.ZERO_L) {
            return true;
        }
        return false;
    }

    @Override
    public void createIndependent(OperationCreateDto operationCreateDto) {
        Product product = ConvertUtil.convert(operationCreateDto, Product.class);
        product.setCreateType(ProductCreateTypeEnum.OPERATION_PLATFORM.getType());
        // 设置使用app类别
        product.setBusinessType(BusinessTypeEnum.EGO_CONNECT.getType()+","+BusinessTypeEnum.FLEET.getType());
        try {
            Map<String, String> keyPair = RsaUtils.genKeyPair();
            product.setPublicKey(keyPair.get(CommonConstant.PUBLIC_KEY));
            product.setSecretKey(keyPair.get(CommonConstant.PRIVATE_KEY));
            product.setReleaseStatus(ProductReleaseEnum.TO_BE_RELEASE.getValue());
            productService.save(product);
        } catch (Exception exception) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_PRODUCT_CREATE_ERROR);
        }
        ProductNetworkMode pMode = new ProductNetworkMode(product.getId(), operationCreateDto.getNetworkMode());
        networkModeService.save(pMode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> batchCreateIndependent(List<ProductIndependenceRead> data) {
        List<String> res = new ArrayList<>();
        if (CollectionUtils.isEmpty(data)) {
            return res;
        }
        // 查出所有产品原表
        List<Product> products = productService.list();
        // 校验产品id是否存在
        List<Long> dataProductIds = data.stream().filter(e -> StringUtils.isNotBlank(e.getProductId())).map(e -> Long.parseLong(e.getProductId())).distinct().collect(Collectors.toList());
        if (!dataProductIds.isEmpty()) {
            List<Long> productIds = products.stream().map(BaseDo::getId).collect(Collectors.toList());
            for (int i = 2, j = data.size() + 2; i < j; i++) {
                ProductIndependenceRead read = data.get(i - 2);
                if (StringUtils.isNotBlank(read.getProductId()) && !productIds.contains(Long.parseLong(read.getProductId()))) {
                    res.add("第" + i + "行，【PID】不存在");
                }
            }
        }
        if (!res.isEmpty()) {
            return res;
        }
        // 校验：sn code 和商品型号是否唯一；设备类型，只能编辑运营平台创建的非iot设备和老iot设备
        List<String> deviceTypes = Arrays.asList("notIotDevice", "oldIotDevice");
        List<String> dbCommodityModels = new ArrayList<>();
        dbCommodityModels.addAll(products.stream().map(Product::getCommodityModel).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));

        List<String> productSnCodes = new ArrayList<>();
        productSnCodes.addAll(products.stream().map(Product::getProductSnCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));
        // 产品和运营草稿对应的map
        Map<Long, Product> dbProductMap = products.stream().collect(Collectors.toMap(BaseDo::getId, Function.identity()));
       for (int i = 2, j = data.size() + 2; i < j; i++) {
            ProductIndependenceRead read = data.get(i - 2);
            if (StringUtils.isBlank(read.getProductId())) {
                // 新增
                if (StringUtils.isNotBlank(read.getCommodityModel()) && dbCommodityModels.contains(read.getCommodityModel())) {
                    res.add("第" + i + "行，【商品型号/Model #】已存在");
                }
                if (StringUtils.isNotBlank(read.getProductSnCode()) && productSnCodes.contains(read.getProductSnCode())) {
                    res.add("第" + i + "行，【SN code】已存在");
                }
                // 判断是否是非Iot设备或老iot设备
                if (!deviceTypes.contains(read.getProductType())) {
                    res.add("第" + i + "行，【设备类型】不是非Iot设备或老iot设备");
                }
            } else {
                // 编辑
                Product p = null;
                if (StringUtils.isNotBlank(read.getCommodityModel())) {
                    p = dbProductMap.getOrDefault(Long.parseLong(read.getProductId()), new Product());
                    if (!StringUtils.equals(p.getCommodityModel(), read.getCommodityModel())  && dbCommodityModels.contains(read.getCommodityModel())) {
                        res.add("第" + i + "行，【商品型号/Model #】已存在");
                    }
                }
                if (StringUtils.isNotBlank(read.getProductSnCode())) {
                    if (p == null) {
                        p = dbProductMap.getOrDefault(Long.parseLong(read.getProductId()), new Product());
                    }

                    if (!StringUtils.equals(p.getProductSnCode(), read.getProductSnCode()) && productSnCodes.contains(read.getProductSnCode())) {
                        res.add("第" + i + "行，【SN code】已存在");
                    }
                    if (p.getCreateType() == 0) {
                        // 技术平台创建
                        if (!StringUtils.equals(p.getProductType(), read.getProductType()) ) {
                            res.add("第" + i + "行，【PID】和【设备类型】不匹配");
                        }
                    }
                }
            }
        }
        if (!res.isEmpty()) {
            return res;
        }

        // 获取更新数据，产品id不为空
        // 判断是否有编辑权限
        List<String> updateProductIds = data.stream().map(ProductIndependenceRead::getProductId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (!updateProductIds.isEmpty()) {
            for (int i = 2, j = data.size() + 2; i < j; i++) {
                ProductIndependenceRead read = data.get(i - 2);
                if (StringUtils.isNotBlank(read.getProductId())) {

                    if (!StringUtils.equals(read.getCategoryName(), dbProductMap.get(Long.parseLong(read.getProductId())).getCategoryId() + "")) {
                        res.add("第" + i + "行，【PID】和【品类名称】不匹配");
                    }
                    if (!StringUtils.equals(read.getProductType(), dbProductMap.get(Long.parseLong(read.getProductId())).getProductType())) {
                        res.add("第" + i + "行，【PID】和【设备类型】不匹配");
                    }
                    if (StringUtils.isBlank(read.getBrandName())) {
                        res.add("第" + i + "行，【PID】存在，【品牌名称】为空");
                    }
                    if (StringUtils.isBlank(read.getProductName())) {
                        res.add("第" + i + "行，【PID】存在，【产品名称】为空");
                    }
                    if (StringUtils.isBlank(read.getCommodityModel())) {
                        res.add("第" + i + "行，【PID】存在，【商品型号/Model #】为空");
                    }
                    if (StringUtils.isBlank(read.getProductSnCode())) {
                        res.add("第" + i + "行，【PID】存在，【SN code】为空");
                    }
                    if (StringUtils.isBlank(read.getProductIcon())) {
                        res.add("第" + i + "行，【PID】存在，【产品图片】为空");
                    }
                    if (StringUtils.isBlank(read.getQuestionTemplate())) {
                        res.add("第" + i + "行，【PID】存在，【问卷模板】为空");
                    }
                }
            }
            if (!res.isEmpty()) {
                return res;
            }
        }
        // 新增或编辑
        List<Product> create = new ArrayList<>();
        List<Product> updateProduct = new ArrayList<>();

        List<Map<String, String>> extra = new ArrayList<>();

        int flag = 1;
        for (ProductIndependenceRead read : data) {
            String productId = read.getProductId();

            Map<String, String> m = new HashMap<>();
            extra.add(m);
            m.put("short", read.getShortDescription());
            m.put("long", read.getLongDescription());
            m.put("tech", read.getTechnicalSpecification());
            m.put("flag", flag + "");

            if (StringUtils.isBlank(productId)) {
                Product p = new Product();
                p.setFlag(flag + "");
                try {
                    Map<String, String> keyPair = RsaUtils.genKeyPair();
                    p.setPublicKey(keyPair.get(CommonConstant.PUBLIC_KEY));
                    p.setSecretKey(keyPair.get(CommonConstant.PRIVATE_KEY));
                } catch (Exception ex) {
                    throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_CREATE_KEY_PAIR_ERROR);
                }
                p.setReleaseStatus(ProductReleaseEnum.TO_BE_RELEASE.getValue());
                p.setCategoryId(Long.parseLong(read.getCategoryName()));
                p.setModel(read.getModel());
                p.setProductType(read.getProductType());
                p.setCreateType(CommonConstant.ONE);
                if (StringUtils.isNotBlank(read.getBrandName())) {
                    p.setBrandId(Long.parseLong(read.getBrandName()));
                }
                if (StringUtils.isNotBlank(read.getProductName())) {
                    MultiLanguageBo bo = remoteMultiLanguageService.simpleCreateMultiLanguage(ApplicationEnum.IOT_PLATFORM.getName(), read.getProductName(), LocaleContextHolder.getLocale().getLanguage());
                    p.setProductName(bo.getLangId() + "");
                }
                if (StringUtils.isNotBlank(read.getCommodityModel())) {
                    p.setCommodityModel(read.getCommodityModel());
                }
                if (StringUtils.isNotBlank(read.getProductSnCode())) {
                    p.setProductSnCode(read.getProductSnCode());
                }
                if (StringUtils.isNotBlank(read.getProductIcon())) {
                    p.setIconType(1);
                    p.setProductIcon(read.getProductIcon());
                }
                if (StringUtils.isNotBlank(read.getQuestionTemplate())) {
                    p.setQuestionTemplate(read.getQuestionTemplate());
                }
                if (StringUtils.isNotBlank(read.getOperationRemark())) {
                    p.setOperationRemark(read.getOperationRemark());
                }
                create.add(p);
            } else {
                m.put("productId", productId);
                Product p = new Product();
                p.setId(Long.parseLong(productId));
                p.setBrandId(Long.parseLong(read.getBrandName()));
                if (p.getProductName() != null) {
                    remoteMultiLanguageService.simpleUpdateMultiLanguage(
                            ApplicationEnum.IOT_PLATFORM.getName(),
                            Long.parseLong(p.getProductName()),
                            Optional.ofNullable(read.getProductName()).orElse(""),
                            LocaleContextHolder.getLocale().getLanguage()
                    );
                } else {
                    MultiLanguageBo bo = remoteMultiLanguageService.simpleCreateMultiLanguage(ApplicationEnum.IOT_PLATFORM.getName(), read.getProductName(), LocaleContextHolder.getLocale().getLanguage());
                    p.setProductName(bo.getLangId() + "");
                }
                p.setCommodityModel(read.getCommodityModel());
                p.setProductSnCode(read.getProductSnCode());
                p.setIconType(1);
                p.setProductIcon(read.getProductIcon());
                p.setQuestionTemplate(read.getQuestionTemplate());
                if (StringUtils.isNotBlank(read.getOperationRemark())) {
                    p.setOperationRemark(read.getOperationRemark());
                }
                updateProduct.add(p);
            }
            flag++;
        }

        if (!create.isEmpty()) {
            create.forEach(e->e.setBusinessType("1,2"));
            productService.saveBatch(create);
            Map<String, Long> collect = create.stream().collect(Collectors.toMap(Product::getFlag, BaseDo::getId));
            extra.forEach(e -> {
                if (e.get("productId") == null) {
                    e.put("productId", collect.get(e.get("flag")) + "");
                }
            });
        }
        if (!updateProduct.isEmpty()) {
            productService.updateBatchById(updateProduct);
        }
        List<ProductNetworkMode> networkModes = new ArrayList<>();
        create.forEach(product -> {
            ProductNetworkMode pMode = new ProductNetworkMode(product.getId(), "notNetworked");
            networkModes.add(pMode);
        });
        networkModeService.saveBatch(networkModes);
        remoteProductReleaseService.importPostSale(extra, LocaleContextHolder.getLocale().getLanguage());
        return res;
    }

    @Override
    public List<OperationProductVo> getExportData(ProductOperationSearchDto search) {

        // 获取所有的Id
        List<Long> nameLangIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(search.getProductName())) {
            nameLangIds = getProductNameLangIds(search.getProductName());
            if (CollectionUtils.isEmpty(nameLangIds)) {
                return null;
            }
        }

        //获取产品
        List<Product> products= productService.list(
                Wrappers.<Product>lambdaQuery()
                        .ge(StringUtils.isNotBlank(search.getCreateStartTime()), Product::getCreateTime, search.getCreateStartTime())
                        .le(StringUtils.isNotBlank(search.getCreateEndTime()), Product::getCreateTime, search.getCreateEndTime())
                        .ge(StringUtils.isNotBlank(search.getUpdateStartTime()), Product::getUpdateTime, search.getUpdateStartTime())
                        .le(StringUtils.isNotBlank(search.getUpdateEndTime()), Product::getUpdateTime, search.getUpdateEndTime())
                        .eq(Objects.nonNull(search.getCategoryId()),Product::getCategoryId,search.getCategoryId())
                        .eq(Objects.nonNull(search.getBrandId()),Product::getBrandId,search.getBrandId())
                        .eq(StringUtils.isNotBlank(search.getProductType()),Product::getProductType,search.getProductType())
                        .eq(StringUtils.isNotBlank(search.getReleaseStatus()),Product::getReleaseStatus,search.getReleaseStatus())
                        .like(StringUtils.isNotBlank(search.getId()),Product::getId,search.getId())
                        .like(StringUtils.isNotBlank(search.getModel()),Product::getModel,search.getModel())
                        .like(StringUtils.isNotBlank(search.getCommodityModel()),Product::getCommodityModel,search.getCommodityModel())
                        .like(StringUtils.isNotBlank(search.getProductSnCode()),Product::getProductSnCode,search.getProductSnCode())
                        .in(!CollectionUtils.isEmpty(nameLangIds),Product::getProductName,nameLangIds)
                        .orderByDesc(Product::getUpdateTime)
                        .orderByAsc(Product::getAppShowOrder)
        );
        List<OperationProductVo> res = new ArrayList<>();
        products.forEach(product -> {
            OperationProductVo productVo = ConvertUtil.convert(product, OperationProductVo.class);
            productVo.setProductIcon(getFileUrl(productVo.getProductIcon()));
            productVo.setQuestionTemplate(Arrays.asList(product.getQuestionTemplate().split(",")));
            res.add(productVo);
        });
        return res;
    }


}
