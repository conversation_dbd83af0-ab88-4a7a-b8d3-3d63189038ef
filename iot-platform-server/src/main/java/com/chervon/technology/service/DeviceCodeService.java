package com.chervon.technology.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.iot.middle.api.vo.device.DeviceCertificateVo;
import com.chervon.technology.api.dto.DeviceCodeAddDto;
import com.chervon.technology.api.dto.EditDeviceCodeStatusDto;
import com.chervon.technology.api.dto.SearchDeviceCodeDto;
import com.chervon.technology.api.vo.DeviceCodeExcel;
import com.chervon.technology.api.vo.DeviceCodeVo;
import com.chervon.technology.domain.dataobject.DeviceCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-07-03
 */
public interface DeviceCodeService extends IService<DeviceCode> {
    /**
     * 根据DeviceId获取status=1的多码
     *
     * @param deviceId 设备多码deviceId
     * @return 设备多码信息
     */
    DeviceCode findNormalOneByDeviceId(String deviceId);
    /**
     * 根据设备ID获取设备多码
     *
     * @param deviceId 设备ID
     * @return 设备多码
     */
    DeviceCode getByDeviceId(String deviceId);
    /**
     * 根据设备多码Sn获取设备多码
     *
     * @param sn 设备SN
     * @return 设备多码
     */
    DeviceCode getBySn(String sn);

    /**
     * 获取所有多码表中的deviceId
     *
     * @return deviceId列表
     */
    List<String> getDeviceIds(String sncode);

    /**
     * 获取多码表中的sn值
     *
     * @return SN列表
     */
    List<String> getSns(String sncode);

    /**
     * 分页获取设备多码详情
     *
     * @param searchDeviceCodeDto 设备多码搜索Dto
     * @return 设备多码分页结果
     */
    PageResult<DeviceCodeVo> getListDevice(SearchDeviceCodeDto searchDeviceCodeDto);

    /**
     * 设备多码添加
     *
     * @param deviceAdd 设备多码添加信息
     */
    void addDeviceCode(DeviceCodeAddDto deviceAdd);

    /**
     * 根据产品判断是否需要申请证书
     * @param productId
     * @return
     */
    boolean isNeedApplyCertificateByPid(Long productId);

    /**
     * 批量验证是否需要申请证书
     * @param listDeviceCode
     * @return
     */
    List<String> getNeedApplyCertificateDeviceId(List<DeviceCode> listDeviceCode);

    /**
     * 异步申请设备证书
     * @param listDeviceCode
     */
    void asyncApplyDeviceCertificate(List<DeviceCode> listDeviceCode);

    List<DeviceCertificateVo> applyDeviceCertificate(List<DeviceCode> listDeviceCode);
    /**
     * 上传多码文文件
     *
     * @param deviceCodeFileArr 多码文件二进制数组
     * @param productId      产品Id
     * @return 上传错误列表
     */
    List<String> uploadDeviceCodeFile(byte[] deviceCodeFileArr, Long productId);

    /**
     * 编辑设备多码状态
     *
     * @param editDeviceCodeStatusDto 编辑设备多码状态Dto
     */
    void editDeviceCodeStatus(EditDeviceCodeStatusDto editDeviceCodeStatusDto);

    /**
     * 导出CSV表格
     *
     * @param searchDeviceCode 查询条件
     */
    List<DeviceCodeExcel> export(SearchDeviceCodeDto searchDeviceCode);

    /**
     * 根据sn,snCode创建一条多码数据,deviceId随机生成
     * @param sn 设备sn
     * @param snCode 产品snCOde
     * @return 设备ID
     */
    String createDeviceCodeBySn(String sn, String snCode);

    /**
     * 废弃的多码信息转移到redis中
     */
    void initDeviceCodeDiscard();
}
