package com.chervon.technology.service;

import com.chervon.common.core.domain.PageResult;
import com.chervon.technology.api.dto.*;
import com.chervon.technology.api.vo.OperationProductBaseVo;
import com.chervon.technology.api.vo.OperationProductVo;
import com.chervon.technology.api.vo.ProductReleaseVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-07-26
 */
public interface OperationProductService  {

    /**
     * 编辑产品产品
     *
     * @param editProductDto
     */
    void edit(OperationEditProductDto editProductDto);

    /**
     * 根据产品Pid删除独立产品
     *
     * @param pId 产品信息
     */
    void deleteIndependentProduct(Long pId);

    /**
     * 产品搜索
     *
     * @param search 搜索条件
     * @return 搜索结果
     */
    PageResult<OperationProductVo> getOperationProductPageList(ProductOperationSearchDto search);

    /**
     * 根据Pid获取产品信息
     *
     * @param pId 产品Pid
     * @return 产品详情
     */
    OperationProductVo getDetailByPid(Long pId);

    /**
     * 根据产品Pid获取产品信息
     *
     * @param pIds 产品Pid
     * @return 产品信息
     */
    List<OperationProductBaseVo> getByPIds(List<Long> pIds);

    /**
     * 品牌是否被使用
     *
     * @param brandId 品牌Id
     * @return 是否被使用
     */
    Boolean ifBrandUsed(Long brandId);

    /**
     * 品类是否被使用
     *
     * @param categoryId 品类Id
     * @return 是否被使用
     */
    Boolean ifCategoryUsed(Long categoryId);

    /**
     * 创建独立产品
     *
     * @param operationCreateDto 运营产品信息
     */
    void createIndependent(OperationCreateDto operationCreateDto);

    /**
     * 批量创建独立产品
     *
     * @param operationCreates 运营产品信息
     */
    List<String> batchCreateIndependent(List<ProductIndependenceRead> operationCreates);

    /**
     * 获取产品导出数据
     *
     * @param search 搜索数据
     * @return 产品信息
     */
    List<OperationProductVo> getExportData(ProductOperationSearchDto search);


}
