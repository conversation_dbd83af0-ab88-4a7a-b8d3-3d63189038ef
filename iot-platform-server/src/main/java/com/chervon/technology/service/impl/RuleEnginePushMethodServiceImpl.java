package com.chervon.technology.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.technology.api.enums.PushMethodEnum;
import com.chervon.technology.domain.dataobject.RuleEnginePushMethod;
import com.chervon.technology.domain.dto.rule.engine.RuleEngineEditDto;
import com.chervon.technology.mapper.RuleEnginePushMethodMapper;
import com.chervon.technology.service.RuleEnginePushMethodService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-09-08 10:25
 **/
@Service
public class RuleEnginePushMethodServiceImpl extends ServiceImpl<RuleEnginePushMethodMapper, RuleEnginePushMethod>
    implements RuleEnginePushMethodService {
    @Override
    public boolean updateByRuleEngineEditDto(RuleEngineEditDto ruleEngineEditDto) {
        LambdaQueryWrapper<RuleEnginePushMethod> pushMethodWrapper = new LambdaQueryWrapper<RuleEnginePushMethod>()
            .eq(RuleEnginePushMethod::getRuleEngineId, ruleEngineEditDto.getId());
        List<RuleEnginePushMethod> ruleEnginePushMethods = this.list(pushMethodWrapper);
        if (!ruleEngineEditDto.isSameAsRuleEnginePushMethod(ruleEnginePushMethods)) {
            List<RuleEnginePushMethod> ruleEnginePushMethodList = new ArrayList<>();
            for (PushMethodEnum pushMethod : ruleEngineEditDto.getPushMethodList()) {
                RuleEnginePushMethod ruleEnginePushMethod = new RuleEnginePushMethod();
                ruleEnginePushMethod.setRuleEngineId(ruleEngineEditDto.getId());
                ruleEnginePushMethod.setPushMethod(pushMethod);
                ruleEnginePushMethodList.add(ruleEnginePushMethod);
            }
            this.removeBatchByIds(ruleEnginePushMethods);
            this.saveBatch(ruleEnginePushMethodList);
            return true;
        }
        return false;
    }
}
