package com.chervon.technology.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.technology.api.enums.OtaJobReleaseOperation;
import com.chervon.technology.domain.dataobject.ReleaseRecord;
/**
 * 操作记录
 * <AUTHOR>
 * @date 2022-07-29
 */
public interface ReleaseRecordService extends IService<ReleaseRecord> {

    /**
     * 获取最新的发布记录
     * <AUTHOR>
     * @date 19:26 2022/8/11
     * @param id: 发布的记录Id，如产品Id，固件Id，RN包管理ID
     * @param releaseType: 审批类型：0产品发布，1固件发布，2RN包发布
     * @param operationType: 操作：0申请发布，1确认发布，2拒绝发布，3取消不发，4申请下架，5确认下架，6拒绝下架，
     * 7取消下架，8申请更新，9确认更新，10拒绝更新，11取消更新，12确认测试通过
     * @return com.chervon.technology.domain.dataobject.ReleaseRecord
     **/
    ReleaseRecord getLatestRecord(Long id, Integer releaseType, String operationType);

    /**
     * 获取拒绝原因
     * <AUTHOR>
     * @date 19:51 2022/9/1
     * @param jobId:
     * @param status:
     * @return java.lang.String
     **/
    String getRefuseReason(Long jobId, OtaJobReleaseOperation status);
}
