package com.chervon.technology.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.technology.api.dto.LastRnPackageDto;
import com.chervon.technology.api.vo.rn.RnItemVo;
import com.chervon.technology.entity.ProductRn;
import com.chervon.technology.req.OriginRnPageDto;
import com.chervon.technology.req.ProductRnAddOrUpdateDto;
import com.chervon.technology.req.ProductRnPageDto;
import com.chervon.technology.resp.ProductRnPageVo;
import com.chervon.technology.resp.ProductRnVo;

/**
 * <AUTHOR>
 */
public interface ProductRnService extends IService<ProductRn> {

    /**
     * 原始rn分页查询
     *
     * @param req 查询条件
     * @return 分页数据
     */
    PageResult<RnItemVo> originRnPage(OriginRnPageDto req);

    /**
     * 产品下添加或修改RN信息
     *
     * @param req 添加或编辑对象
     */
    void addOrUpdate(ProductRnAddOrUpdateDto req);

    /**
     * 删除产品下的RN
     *
     * @param productRnId productRnId
     */
    void delete(Long productRnId);

    /**
     * 获取产品下RN包的url
     *
     * @param productRnId productRnId
     * @return url
     */
    String getRnUrl(Long productRnId);

    /**
     * 产品下rn分页查询
     *
     * @param req 查询条件
     * @return 分页数据
     */
    PageResult<ProductRnPageVo> page(ProductRnPageDto req);


    /**
     * app 获取用户最大rn版本
     *
     * @param req 请求数据
     * @return 最大rn版本
     */
    ProductRn getMaxRn(LastRnPackageDto req);

    /**
     * 把rnName设置到redis
     *
     * @param rnName     rn 名称
     * @param userId     用户id
     * @param productId  产品id
     * @param appType    app 类型 android ios
     * @param appVersion app 版本
     */
    void rnNameToRedis(String rnName, Long userId, Long productId, String appType, String appVersion);

    /**
     * 获取最大版本 rn name
     *
     * @param req    请求参数
     * @param userId 用户id
     * @return 最大版本 rn name
     */
    String getLatestBundleName(LastRnPackageDto req, Long userId);

    /**
     * RN确认发布
     *
     * @param productRnId
     */
    void releaseRn(Long productRnId);

    /**
     * RN下架
     *
     * @param productRnId
     */
    void withdrawReleaseRn(Long productRnId);

    /**
     * 检查产品RN是否存在
     * @param productRnId
     */
    ProductRn checkAndGetProductRn(long productRnId);
}
