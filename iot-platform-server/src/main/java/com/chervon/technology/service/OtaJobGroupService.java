package com.chervon.technology.service;

import com.chervon.technology.domain.entity.OtaJobGroup;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.technology.domain.vo.ota.JobFirmwareVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 升级任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
public interface OtaJobGroupService extends IService<OtaJobGroup> {

    /**
     * 获取测试分组名称列表
     * <AUTHOR>
     * @date 15:52 2022/8/10
     * @param jobId:
     * @return java.util.List<java.lang.String>
     **/
    List<String> getTestGroupName(Long jobId);

    /**
     * 根据jobId删除
     * <AUTHOR>
     * @date 19:00 2022/8/10
     * @param jobId:
     * @return void
     **/
    void removeByJobId(Long jobId);

    /**
     * 获取生产名称列表
     * <AUTHOR>
     * @date 15:52 2022/8/10
     * @param jobId:
     * @return java.util.List<java.lang.String>
     **/
    List<String> getProductGroupName(Long jobId);

    /**
     * 根据分组名称列表获取已发布或者测试分组测试中的job列表
     * <AUTHOR>
     * @date 19:04 2022/8/24
     * @param groupNames: 分组名称列表
     * @param deviceId:
     * @return java.util.List<java.lang.String>
     **/
    List<Long> getReadyJobIds(List<String> groupNames, String deviceId);

    /**
     * 获取可升级的任务固件列表
     * @param groupNames
     * @param deviceId
     * @param componentNo
     * @return
     */
    List<JobFirmwareVo> getReadyJobFirmwareList(List<String> groupNames,String deviceId,String componentNo,String currentVersion);
    /**
     * 判断是否有目标分组
     * <AUTHOR>
     * @date 10:13 2022/9/14
     * @param jobId:
     * @return java.lang.Boolean
     **/
    Boolean hasTargetGroup(Long jobId);

    /**
     * 根据分组名称获取在使用的任务id列表
     * <AUTHOR>
     * @date 14:59 2022/9/27
     * @param groupName: 分组名称
     * @return java.util.List<java.lang.Long>
     **/
    List<Long> getJobIdsByGroupName(String groupName);

    /**
     * 根据设备分组名称列表及最新的job，获取最新job命中设备的分组名称
     * <AUTHOR>
     * @date 15:09 2022/10/3
     * @param groupNames:
     * @param jobId:
     * @return java.lang.String
     **/
    String getGroupNameByJobId(List<String> groupNames, Long jobId);
}
