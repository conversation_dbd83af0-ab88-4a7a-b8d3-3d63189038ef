package com.chervon.technology.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.technology.domain.dataobject.RuleTrigger;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022-09-08 10:24
 **/
public interface TriggerService extends IService<RuleTrigger> {

    /**
     * 根据groupId获取所有的触发器
     *
     * @param groupId
     * @return
     */
    List<RuleTrigger> listByGroupId(String groupId,String propertyId,String parameter,String value);
}
