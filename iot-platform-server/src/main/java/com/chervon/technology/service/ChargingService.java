package com.chervon.technology.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.technology.domain.bo.SmsChargingStoreBo;
import com.chervon.technology.domain.dataobject.Charging;

import javax.validation.constraints.NotNull;

public interface ChargingService extends IService<Charging> {

    /**
     * 存储短信计费数据
     *
     * @param req 请求数据
     */
    void storeSmsCharging(@NotNull SmsChargingStoreBo req);

    /**
     * 根据消息id，删除短信计费数据
     *
     * @param msgId 消息id
     */
    void removeSmsCharging(@NotNull Long msgId);
}
