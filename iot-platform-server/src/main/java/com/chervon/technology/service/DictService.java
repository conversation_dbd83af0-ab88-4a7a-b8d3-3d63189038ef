package com.chervon.technology.service;

import com.chervon.technology.domain.bo.DictBo;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/17 11:35
 * @desc 描述
 */
public interface DictService {

    /**
     * 根据字典名称获取字典详情集合
     *
     * @param dictNames 字典名称集合
     * @param lang      语言
     * @return 详情集合
     */
    List<DictBo> listByDictName(String lang, @NotEmpty List<String> dictNames);
}
