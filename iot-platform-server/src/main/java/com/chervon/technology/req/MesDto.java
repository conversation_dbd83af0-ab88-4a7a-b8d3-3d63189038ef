package com.chervon.technology.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/8 15:13
 */
@Data
@ApiModel(description = "mes请求参数对象")
public class MesDto {

    @ApiModelProperty(value = "工单号")
    private String moCode;

    @ApiModelProperty(value = "mes#")
    private String mes;

    @ApiModelProperty(value = "设备序列号")
    private String sn;

    @ApiModelProperty(value = "设备Id")
    private String deviceId;

    @ApiModelProperty(value = "物料码")
    private String itemCode;

    @ApiModelProperty(value = "生产日期，格式yyyy-MM-dd 如2022-10-01")
    private String productionDate;

    @ApiModelProperty(value = "iccid")
    private String iccid;

    private Long synchronizationTime;

    private Long createTime;

}
