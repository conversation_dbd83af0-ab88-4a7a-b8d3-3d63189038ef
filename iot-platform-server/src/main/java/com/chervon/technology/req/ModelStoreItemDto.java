package com.chervon.technology.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2022/9/13 11:21
 */
@Data
@ApiModel(description = "模块项对象")
public class ModelStoreItemDto {

    @ApiModelProperty(value = "模块id，传入则编辑，不传入则新增")
    private Long modelId;

    @ApiModelProperty(value = "模块名称")
    @NotBlank
    private String modelName;

    @ApiModelProperty(value = "模块多语言id，有就传，没有就不传")
    private String modelLangId;

    @ApiModelProperty(value = "模块编码")
    @NotBlank
    private String modelCode;
}
