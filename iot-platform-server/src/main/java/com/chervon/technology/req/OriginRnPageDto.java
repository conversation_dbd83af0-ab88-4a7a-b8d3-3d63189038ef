package com.chervon.technology.req;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/7/19 11:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "原始rn分页查询")
public class OriginRnPageDto extends PageRequest {

    @ApiModelProperty(value = "rn包名称，模糊搜索")
    private String rnName;

    @ApiModelProperty(value = "rn包版本号")
    private String rnVersion;

    @ApiModelProperty(value = "适用app类型 1 ego 2 fleet，默认全部")
    private int appName;
}
