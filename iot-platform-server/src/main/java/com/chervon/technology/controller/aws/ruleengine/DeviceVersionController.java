package com.chervon.technology.controller.aws.ruleengine;

import cn.hutool.core.collection.CollectionUtil;
import com.chervon.common.core.utils.JsonUtils;
import com.chervon.technology.service.DeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Api(tags = "AWS规则引擎接收-设备版本上报")
@RestController
@RequestMapping("/rule")
@Slf4j
public class DeviceVersionController {
    @Autowired
    private DeviceService deviceService;

    /**
     * 上报更新总成版本号
     * 对应的topic：$aws/things/+/shadow/name/componentVersion/update/accepted
     * 规则引擎：*_update_component_version
     *
     * @param deviceId:
     * <AUTHOR>
     * @date 16:34 2022/5/16
     * @return: void
     **/
    @PostMapping("/job/package/version")
    @ApiOperation("上报更新总成版本号")
    public void updatePackageVersion(@RequestHeader String deviceId,
                                     @RequestBody Map<String, String> versionMap) {
        //缓存上报的固件版本(提前做减少更新缓存的间隔)
        if (CollectionUtil.isEmpty(versionMap)) {
            log.error("updatePackageVersion请求 device:{},updateFirmwareVersion versionMap is empty", deviceId);
            return;
        }
        log.info("updatePackageVersion请求刷新缓存->device:{}, versionMap:{},timestamp：{}", deviceId, JsonUtils.toJsonString(versionMap),System.currentTimeMillis());
        deviceService.updateFirmwareVersion(deviceId, versionMap);
    }
}
