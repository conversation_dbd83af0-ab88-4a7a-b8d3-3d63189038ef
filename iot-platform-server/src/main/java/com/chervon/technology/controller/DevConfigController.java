package com.chervon.technology.controller;

import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.technology.api.vo.PsysUserVo;
import com.chervon.usercenter.api.dto.SysUserPageDto;
import com.chervon.usercenter.api.service.RemoteSysUserService;
import com.chervon.usercenter.api.vo.SysUserVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/12 11:44
 */
@Api(tags = "技术平台-产品、rn、固件的编辑审批设置")
@RestController
@RequestMapping("/dev")
@Slf4j
public class DevConfigController {

    @DubboReference
    private RemoteSysUserService remoteSysUserService;

    @ApiOperation("筛选用户")
    @PostMapping("sysUser/page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<PsysUserVo> sysUserPage(@RequestBody SysUserPageDto req) {
        PageResult<SysUserVo> data = remoteSysUserService.sysUserPage(req);
        PageResult<PsysUserVo> res = new PageResult<>(data.getPageNum(), data.getPageSize(), data.getTotal());
        res.setList(data.getList().stream().map(e -> {
            PsysUserVo vo = new PsysUserVo();
            vo.setSysUserId(e.getId());
            vo.setSysUserGuid(e.getLdapUserGuid());
            vo.setSysUserEmployeeNumber(e.getEmployeeNumber());
            vo.setSysUsername(e.getUserName());
            return vo;
        }).collect(Collectors.toList()));
        return res;
    }



}
