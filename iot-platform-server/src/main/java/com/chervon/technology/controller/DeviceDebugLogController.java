package com.chervon.technology.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.technology.api.dto.DeviceDebugLogDto;
import com.chervon.technology.api.vo.DeviceDebugLogPreVo;
import com.chervon.technology.api.vo.DeviceDebugLogVo;
import com.chervon.technology.domain.dataobject.DeviceDebugLog;
import com.chervon.technology.domain.dataobject.DeviceDebugLogWebVo;
import com.chervon.technology.domain.dto.productdebug.IotDeviceShadowByDebugDto;
import com.chervon.technology.domain.dto.productdebug.ProductDebugIssueDto;
import com.chervon.technology.domain.vo.device.DeviceDebugLogDesiredVo;
import com.chervon.technology.domain.vo.device.DeviceDebugLogSearchVo;
import com.chervon.technology.service.DeviceDebugLogService;
import com.chervon.technology.service.ProductDebugUserService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2022年12月29日
 */

@Api(tags = "设备调试日志文件相关接口")
@RestController
@RequestMapping("/device/debug/log")
public class DeviceDebugLogController {

	@Autowired
	private DeviceDebugLogService debugLogService;

	@Resource
	private ProductDebugUserService productDebugUserService;


	/**
	 * 发起调试
	 *
	 * @param
	 * @return AWS平台地址
	 */
	@ApiOperation(value = "发起调试")
	@PostMapping("/desired")
	public R<?> desired(@RequestBody @Validated DeviceDebugLogDesiredVo deviceDebugLogDesiredVo) {
		ProductDebugIssueDto productDebugIssueDto = new ProductDebugIssueDto();
		List<IotDeviceShadowByDebugDto> debugDtos = Lists.newArrayList();
		IotDeviceShadowByDebugDto debugDto = new IotDeviceShadowByDebugDto();
		JSONObject jsonObject = new JSONObject();
		String debugParam = deviceDebugLogDesiredVo.getDebugParam();
		jsonObject.set("1", HexUtil.encodeHexStr(debugParam.getBytes()));
		debugDto.setIdentifier("66");
		debugDto.setValue(jsonObject);
		debugDtos.add(debugDto);
		productDebugIssueDto.setList(debugDtos);
		productDebugIssueDto.setDeviceId(deviceDebugLogDesiredVo.getDeviceId());
		productDebugIssueDto.setActionType("desired");
		productDebugUserService.updateDesired(productDebugIssueDto);
		return R.ok();
	}

	/**
	 * 获取列表接口
	 *
	 * @param dto 设备Id
	 * @return AWS平台地址
	 */
	@PostMapping("/record/list")
	@ApiOperation(value = "获取列表接口")
	@Log(businessType = BusinessType.VIEW)
	public PageResult<DeviceDebugLogWebVo> pageList(@RequestBody @Validated DeviceDebugLogSearchVo dto) {
		Page<DeviceDebugLog> deviceDebugLogPage = debugLogService.pageList(dto);
		List<DeviceDebugLog> records = deviceDebugLogPage.getRecords();
		List<DeviceDebugLogWebVo> result = Lists.newArrayList();
		if (CollectionUtil.isNotEmpty(records)) {
			records.forEach(e -> {
				DeviceDebugLogWebVo convert = ConvertUtil.convert(e, DeviceDebugLogWebVo.class);
				if (convert.getSuccessPartNum() != null && convert.getTotalPartNum() != null) {
					double progressDouble = (double) convert.getSuccessPartNum() / convert.getTotalPartNum();
					convert.setProgress(NumberUtil.decimalFormat("#%", progressDouble));
				} else {
					convert.setProgress("0%");
				}
				result.add(convert);
			});
		}
		PageResult<DeviceDebugLogWebVo> pageResult = new PageResult<>(deviceDebugLogPage.getCurrent(), deviceDebugLogPage.getSize(), deviceDebugLogPage.getTotal());
		pageResult.setPages(deviceDebugLogPage.getPages());
		pageResult.setList(result);
		return pageResult;
	}


	/**
	 * 下载接口  生成预下载url
	 *
	 * @param /ota/downloadUrl
	 * @return AWS平台地址
	 */
	@PostMapping("/get/url")
	@ApiOperation(value = "下载接口  生成预下载url")
	public R<String> getDownloadUrl(@RequestBody @Validated SingleInfoReq<String> key) {
		String downloadUrl = debugLogService.getDownloadUrl(key.getReq());
		return R.ok(downloadUrl);
	}

	/**
	 * 删除接口筛查
	 */
	@PostMapping("/record/delete")
	@ApiOperation(value = "删除接口筛查")
	@Log(businessType = BusinessType.DELETE)
	public R<Boolean> recordDelete(@RequestBody @Validated SingleInfoReq<Long> key) {
		LambdaQueryWrapper<DeviceDebugLog> wrapper = new LambdaQueryWrapper<DeviceDebugLog>()
				.eq(DeviceDebugLog::getId, key.getReq());
		Boolean remove = debugLogService.remove(wrapper);
		return R.ok(remove);
	}

	/**
	 * 分段预上传
	 *
	 * @return 注册结果
	 */
	@ApiOperation("分段预上传")
	@PostMapping("/part/preSignedUrl/get")
	public DeviceDebugLogDto getPartPreSignedUrl(@RequestBody @Validated DeviceDebugLogVo appDeviceDebugLogVo) {
		return debugLogService.getPartPreSignedUrl(appDeviceDebugLogVo);
	}


	@ApiOperation("获取下一分段url")
	@PostMapping("/part/preSignedUrl/Next")
	public DeviceDebugLogDto getNextPartPreSignedUrl(@RequestBody @Validated DeviceDebugLogPreVo appDeviceDebugLogVo) {
		return debugLogService.getNextPartPreSignedUrl(appDeviceDebugLogVo);

	}


}
