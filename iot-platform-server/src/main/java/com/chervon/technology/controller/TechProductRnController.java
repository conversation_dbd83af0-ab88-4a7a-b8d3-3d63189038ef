package com.chervon.technology.controller;

import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.domain.SingleInfoResp;
import com.chervon.technology.req.*;
import com.chervon.technology.resp.ProductRnPageVo;
import com.chervon.technology.resp.ProductRnVo;
import com.chervon.technology.api.vo.rn.RnItemVo;
import com.chervon.technology.service.ProductRnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022-07-11
 */
@Api(tags = "产品管理-Rn包管理")
@RestController
@RequestMapping("/product/rn")
@AllArgsConstructor
@Validated
public class TechProductRnController {

    private final ProductRnService productRnService;

    @ApiOperation("加载选择RN包分页列表")
    @PostMapping("origin/page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<RnItemVo> originRnPage(@RequestBody OriginRnPageDto req) {
        return productRnService.originRnPage(req);
    }

    @ApiOperation("产品下添加RN包或者修改RN包")
    @PostMapping("addOrUpdate")
    @Log(businessType = BusinessType.EDIT)
    public void addOrUpdate(@RequestBody ProductRnAddOrUpdateDto req) {
        productRnService.addOrUpdate(req);
    }

    @ApiOperation("删除产品下RN包")
    @PostMapping("delete")
    @Log(businessType = BusinessType.DELETE)
    public void delete(@RequestBody SingleInfoReq<Long> req) {
        productRnService.delete(req.getReq());
    }

    @ApiOperation("下载产品下的Rn包文件")
    @PostMapping(value = "download")
    @Log(businessType = BusinessType.VIEW)
    public SingleInfoResp<String> downLoad(@RequestBody SingleInfoReq<Long> req) {
        return new SingleInfoResp<>(productRnService.getRnUrl(req.getReq()));
    }

    @ApiOperation("分页获取产品下RN列表")
    @PostMapping("page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<ProductRnPageVo> page(@RequestBody ProductRnPageDto req) {
        return productRnService.page(req);
    }


    /**
     * RN确认发布
     *
     * @param productRnId
     */
    @ApiOperation("RN-发布")
    @PostMapping("release")
    @Log(businessType = BusinessType.EDIT)
    public void confirmRelease(@RequestBody SingleInfoReq<Long> productRnId) {
        productRnService.releaseRn(productRnId.getReq());
    }

    /**
     * RN确认下架
     *
     * @param productRnId
     */
    @ApiOperation("RN-下架")
    @PostMapping("withdraw")
    @Log(businessType = BusinessType.EDIT)
    public void withdraw(@RequestBody SingleInfoReq<Long> productRnId) {
        productRnService.withdrawReleaseRn(productRnId.getReq());
    }

}
