package com.chervon.technology.config;

import com.chervon.common.redis.constant.RedisConstant;

/**
 * <AUTHOR>
 * @date 2022-07-19
 */
public interface IotPlatformCommon {

    /**
     * 异步调用
     */
    String ASYNC = "async";

    /**
     * 同步调用
     */
    String SYNC = "sync";

    String APPLICATION_NAME = "iot-platform";

    /**
     * 产品编辑资源URL
     */
    String PRODUCT_EDIT_PATH = "/product/edit";

    /**
     * 运营平台产品的编辑权限
     */
    String OPERATION_PRODUCT_EDIT_PATH = "/product/edit/operation";

    /**
     * 国内iccid前缀
     */
    String CN_ICCID_PREFIX = "8986";

    /**
     * 国际iccid前缀
     */
    String IN_ICCID_PREFIX = "893301";


    String WIFI = "wifi";

    String MODE_4G = "4G";

    String BLE = "BLE";

    String DT = "DT";

    String LAN = "LAN";

    String NO_NETWORKED = "noNetworked";

    String BLE_WIFI_4G="BLE+wifi+4G";

    /**
     * 多码导入
     */
    String DEVICE_ID = "设备ID";

    String SN = "设备SN";

    String PRODUCT_SN_CODE = "ProductSnCode";

    String MES = "MES #";

    String MO_CODE = "MO code";

    String ITEM_CODE = "ITEM code";

    String PRODUCTION_DATE = "Production Date";

    /**
     * 15位多码正则校验
     * 保持一致:IotAppCommonConstant#DEVICE_CODE_CHECK
     */
    String DEVICE_CODE_CHECK = "^[N,A,E,Z][A-Z,0-9]{4}[0-9]{9}[A-Z,0-9]";
    /**
     * 16位非IOT设备多码校验
     * 保持一致:IotAppCommonConstant#NOT_IOT_DEVICE_CODE_CHECK
     */
    String NO_IOT_DEVICE_CODE_CHECK = "^[R][N,A,E,Z][A-Z,0-9]{4}[0-9]{9}[A-Z,0-9]";

    String UNDERLINE = "_";

    String AUTHORIZATION = "Authorization";

    String RULE_ID = "ruleId";

    String GROUP_ID = "groupId";

    Integer FIVE_THOUSAND = 20000;

    Integer NEGATIVE_ONE = 1;
    /**
     * 添加物模型服务
     */
    String NAME="name";


    String OUTPUT_DATA="outputData";


    String INPUT_DATA="inputData";

    /**
     * RN bundle name redis key
     * 变量：userId,PID,appType,appversion
     *
     */
    String RN_BUNDLE_NAME_KEY= RedisConstant.USER_DEVICE_BUNDLE_NAME + "%s:%s:%s:%s";
}
