package com.chervon.technology.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * C设备充电完成提醒配置
 *
 * <AUTHOR>
 * @since 2023-04-18 14:31
 **/
@Data
@Component
@ConfigurationProperties(prefix = "charger.reminder")
@RefreshScope
public class ChargerReminderConfig {
    /**
     * 产品sn code
     */
    private String productSnCode;
    /**
     * 多少秒内
     */
    private Integer triggerDuration;
    /**
     * 触发多少次
     */
    private Integer triggerTimes;
    /**
     * 推送方式列表
     */
    private List<Integer> pushMethods;
    /**
     * 消息模板ID
     */
    private Long messageTemplateId;
}
