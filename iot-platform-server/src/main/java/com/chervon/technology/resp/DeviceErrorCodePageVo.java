package com.chervon.technology.resp;

import com.chervon.technology.api.core.BasePageResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/9/7 13:55
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "设备错误码分页数据")
public class DeviceErrorCodePageVo extends BasePageResp {

    @ApiModelProperty(value = "设备错误码id")
    private Long deviceErrorCodeId;

    @ApiModelProperty(value = "品类Id")
    private Long categoryId;

    @ApiModelProperty(value = "品类名称")
    private String categoryName;

    @ApiModelProperty(value = "品类code")
    private String categoryCode;

    @ApiModelProperty(value = "模块名称")
    private String modelName;

    @ApiModelProperty(value = "模块编码")
    private String modelCode;

    @ApiModelProperty(value = "产品型号")
    private String productModel;

    @ApiModelProperty(value = "事件类型code")
    private String eventCode;

    @ApiModelProperty(value = "功能名称")
    private String function;

    @ApiModelProperty(value = "错误代码")
    private String errorCode;

    @ApiModelProperty(value = "备注")
    private String remark;
}
