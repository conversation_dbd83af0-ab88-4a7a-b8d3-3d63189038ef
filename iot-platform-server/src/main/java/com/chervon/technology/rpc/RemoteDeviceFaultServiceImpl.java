package com.chervon.technology.rpc;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.sso.CurrentLoginUtil;
import com.chervon.iot.app.api.RemoteAppDeviceService;
import com.chervon.iot.app.api.dto.AppUserDeviceDTO;
import com.chervon.operation.api.RemoteSuggestionService;
import com.chervon.operation.api.vo.SuggestionVo;
import com.chervon.technology.api.RemoteDeviceFaultService;
import com.chervon.technology.api.vo.DeviceFaultVo;
import com.chervon.technology.domain.dataobject.Device;
import com.chervon.technology.domain.dataobject.DeviceFault;
import com.chervon.technology.domain.dataobject.FaultDictionary;
import com.chervon.technology.service.DeviceFaultService;
import com.chervon.technology.service.DeviceService;
import com.chervon.technology.service.FaultDictionaryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-03-19 11:15
 **/
@DubboService
@Service
@Slf4j
public class RemoteDeviceFaultServiceImpl implements RemoteDeviceFaultService {
    @Autowired
    private DeviceFaultService deviceFaultService;
    @Autowired
    private FaultDictionaryService faultDictionaryService;
    @Autowired
    private DeviceService deviceService;
    @DubboReference
    private RemoteSuggestionService remoteSuggestionService;
    @DubboReference
    private RemoteAppDeviceService remoteAppDeviceService;

    /**
     * 获取设备故障列表（包含处理建议）
     * @param deviceId 设备id
     * @return
     */
    @Override
    public List<DeviceFaultVo> getDeviceFaultList(String deviceId) {
        final List<DeviceFault> deviceFaultList = deviceFaultService.getDeviceFaultList(deviceId,null);
        if (CollectionUtils.isEmpty(deviceFaultList)) {
            return new ArrayList<>();
        }
        //获取设备昵称
        String nickName = getDeviceName(deviceId);

        final Map<String, FaultDictionary> faultDictionaryMap = faultDictionaryService.batchGetFaultCodeMap(deviceFaultList);
        final List<Long> listSuggestionId = faultDictionaryMap.values().stream().map(FaultDictionary::getSuggestionId).distinct().collect(Collectors.toList());
        Map<Long, SuggestionVo> mapSuggestions = new HashMap<>();
        if(!CollectionUtils.isEmpty(listSuggestionId)) {
            final List<SuggestionVo> listSuggestionVos = remoteSuggestionService.getSuggestionListByIds(listSuggestionId);
            mapSuggestions = listSuggestionVos.stream().collect(Collectors.toMap(SuggestionVo::getSuggestionId, Function.identity(), (k1, k2) -> k2));
        }
        List<DeviceFaultVo> deviceFaultVos = BeanCopyUtils.copyList(deviceFaultList, DeviceFaultVo.class);
        for (DeviceFaultVo deviceFaultVo : deviceFaultVos) {
            FaultDictionary dicFault = faultDictionaryMap.get(deviceFaultVo.getFaultCode()+"-"+deviceFaultVo.getProductId());
            if (dicFault != null) {
                deviceFaultVo.setFaultTitle(nickName+":"+dicFault.getFaultTitle());
                deviceFaultVo.setFaultDesc(dicFault.getFaultDesc());
                final SuggestionVo suggestionVo = mapSuggestions.get(dicFault.getSuggestionId());
                if (suggestionVo != null) {
                    deviceFaultVo.setFaultSuggestion(suggestionVo.getContent());
                }
            }
        }
        return deviceFaultVos;
    }

    private String getDeviceName(String deviceId){
        Long userId = CurrentLoginUtil.getCurrentId();
        final List<AppUserDeviceDTO> appUserDeviceInfo = remoteAppDeviceService.getAppUserDeviceInfoByDeviceId(deviceId, userId);
        if(!CollectionUtils.isEmpty(appUserDeviceInfo) && !StringUtils.isEmpty(appUserDeviceInfo.get(0).getDeviceNickName())){
            return appUserDeviceInfo.get(0).getDeviceNickName();
        }
        final Device deviceInfo = deviceService.getOne(new LambdaQueryWrapper<Device>().eq(Device::getDeviceId, deviceId));
        if(!Objects.isNull(deviceInfo)){
            return deviceInfo.getDeviceName();
        }
        return "";
    }

    /**
     * 获取设备故障消息标题（不包含处理建议和内容）
     * @param deviceId 设备id
     * @return
     */
    @Override
    public List<DeviceFaultVo> getDeviceFaultMessage(String deviceId) {
        Long userId = CurrentLoginUtil.getCurrentId();
        final List<DeviceFault> deviceFaultList = deviceFaultService.getDeviceFaultList(deviceId,userId);
        if (CollectionUtils.isEmpty(deviceFaultList)) {
            return new ArrayList<>();
        }
        final Map<String, FaultDictionary> faultDictionaryMap = faultDictionaryService.batchGetFaultCodeMap(deviceFaultList);
        //获取设备昵称
        String nickName = getDeviceName(deviceId);
        List<DeviceFaultVo> deviceFaultVos = new ArrayList<>();
        for (DeviceFault deviceFault : deviceFaultList) {
            DeviceFaultVo deviceFaultVo = BeanCopyUtils.copy(deviceFault, DeviceFaultVo.class);
            deviceFaultVo.setCreateTime(deviceFault.getUpdateTime().getTime());
            FaultDictionary dicFault = faultDictionaryMap.get(deviceFaultVo.getFaultCode()+"-"+deviceFaultVo.getProductId());
            if (dicFault != null) {
                deviceFaultVo.setFaultTitle(nickName+":"+dicFault.getFaultTitle());
            }
            deviceFaultVos.add(deviceFaultVo);
        }
        return deviceFaultVos;
    }

    @Override
    public boolean updateDeviceFaultStatus(String deviceId, String faultCode, Integer status) {
        return deviceFaultService.updateDeviceFaultStatus(deviceId, faultCode, status);
    }
}
