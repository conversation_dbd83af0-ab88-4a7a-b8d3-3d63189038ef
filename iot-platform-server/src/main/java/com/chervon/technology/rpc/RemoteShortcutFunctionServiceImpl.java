package com.chervon.technology.rpc;

import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.technology.api.RemoteShortcutFunctionService;
import com.chervon.technology.api.vo.ShortcutFunctionVo;
import com.chervon.technology.service.ShortcutFunctionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 2022/11/10 19:53
 */
@DubboService
@Slf4j
public class RemoteShortcutFunctionServiceImpl implements RemoteShortcutFunctionService {
    @Resource
    private ShortcutFunctionService shortcutFunctionService;
    @Override
    public List<ShortcutFunctionVo> list(String deviceId) {
        List<ShortcutFunctionVo> shortcutFunctionVoList=new ArrayList<>();
        List<com.chervon.technology.domain.vo.shortcutFunction.ShortcutFunctionVo> listByDeviceId=shortcutFunctionService.listByDeviceId(deviceId);
        if(listByDeviceId!=null&&!listByDeviceId.isEmpty()){
            for(com.chervon.technology.domain.vo.shortcutFunction.ShortcutFunctionVo shortcutFunctionVo:listByDeviceId){
                ShortcutFunctionVo shortcutFunction= ConvertUtil.convert(shortcutFunctionVo, ShortcutFunctionVo.class);
                shortcutFunctionVoList.add(shortcutFunction);
            }
        }
        return shortcutFunctionVoList;
    }
}
