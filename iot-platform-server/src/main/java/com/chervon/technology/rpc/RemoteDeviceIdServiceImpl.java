package com.chervon.technology.rpc;

import com.chervon.technology.api.RemoteDeviceIdService;
import com.chervon.technology.api.dto.DeviceProductDto;
import com.chervon.technology.mapper.DeviceMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/31 16:09
 */
@DubboService
@Service
@Slf4j
public class RemoteDeviceIdServiceImpl implements RemoteDeviceIdService {

    @Resource
    private DeviceMapper deviceMapper;

    @Override
    public List<String> listDeviceIdByProductInfo(String bindDeviceId, String bindDeviceSn, Long bindDeviceCategoryId, Long bindDeviceBrandId, String model, String commodityModel) {
        List<String> res = deviceMapper.selectListDeviceIdByProductInfo(bindDeviceId, bindDeviceSn, bindDeviceCategoryId, bindDeviceBrandId, model, commodityModel);
        if (res == null) {
            res = new ArrayList<>();
        }
        return res.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<String> selectListDeviceIdByProductId(Long productId) {
        List<String> res=deviceMapper.selectListDeviceIdByProductId(productId);
        if (res == null) {
            res = new ArrayList<>();
        }
        return res.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<DeviceProductDto> listDeviceProductByDeviceIds(List<String> deviceIds) {
        if(CollectionUtils.isEmpty(deviceIds)){
            return Collections.emptyList();
        }
        List<DeviceProductDto> res = deviceMapper.selectListDeviceProductByDeviceIds(deviceIds);
        if(CollectionUtils.isEmpty(res)){
            return Collections.emptyList();
        }
        return res;
    }
}
