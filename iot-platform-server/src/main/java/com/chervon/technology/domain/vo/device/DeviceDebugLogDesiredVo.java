package com.chervon.technology.domain.vo.device;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022年12月29日
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeviceDebugLogDesiredVo implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty("设备Id")
	private String deviceId;
	@ApiModelProperty("调试参数")
	private String debugParam;

}
