package com.chervon.technology.domain.vo.product;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-07-06
 */
@Data
@NoArgsConstructor
public class ModelEventVo implements Serializable {
    /**
     * 物模型项目唯一标识符
     **/
    @ApiModelProperty("物模型项目唯一标识符")
    private String identifier;

    /**
     * 物模型项目名称
     **/
    @ApiModelProperty("物模型项目标识符")//多语言转字符串
    private String name;

    /**
     * 事件类型(info,error,warn)
     */
    @ApiModelProperty("事件类型(info,error,warn)")
    private String type;

    /**
     * 事件描述
     **/
    @ApiModelProperty("事件描述")
    private String desc;

    /**
     * 参数列表
     */
    @ApiModelProperty("参数列表")
    private List<ParamDataVo> outputData;

    @ApiModelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("更新者")
    private String updateBy;

    public ModelEventVo(String identifier, String type, String desc) {
        this.identifier = identifier;
        this.type = type;
        this.desc = desc;
    }
}
