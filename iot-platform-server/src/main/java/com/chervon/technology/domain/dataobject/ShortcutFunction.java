package com.chervon.technology.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date Created in 2022/11/8 16:56
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("shortcut_function")
@Slf4j
public class ShortcutFunction extends BaseDo {
    @ApiModelProperty("产品Id")
    private Long productId;
    @ApiModelProperty("触发条件类型：0属性 1事件")
    private String conditionType;
    @ApiModelProperty("物模型功能Id")
    private String propertyId;
    @ApiModelProperty("功能名称多语言id")
    private String multiLanguageId;
}
