//package com.chervon.technology.domain.enums;
//
///**
// * 日志状态
// * <AUTHOR>
// * @date 16:38 2022/7/29
// **/
//public enum LogStatus {
//    SUCCESS(1, "成功"),
//    FAIL(0, "失败");
//
//    private int value;
//
//    private String typeStr;
//
//    LogStatus(int value, String typeStr) {
//        this.value = value;
//    }
//
//    public int getValue() {
//        return value;
//    }
//
//    public String getTypeStr() {
//        return typeStr;
//    }
//
//    public static LogStatus getLogStatus(int value) {
//        if (value == 1) {
//            return LogStatus.SUCCESS;
//        } else {
//            return LogStatus.FAIL;
//        }
//    }
//}
