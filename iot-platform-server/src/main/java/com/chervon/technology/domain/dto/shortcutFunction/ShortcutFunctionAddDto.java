package com.chervon.technology.domain.dto.shortcutFunction;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 2022/11/8 17:23
 */
@Data
public class ShortcutFunctionAddDto implements Serializable {
    @ApiModelProperty("产品Id")
    private Long productId;
    @ApiModelProperty("触发条件类型：0属性 1事件")
    private String conditionType;
    @ApiModelProperty("物模型功能Id")
    private String propertyId;
    @ApiModelProperty("功能名称多语言id")
    private String multiLanguageId;
}
