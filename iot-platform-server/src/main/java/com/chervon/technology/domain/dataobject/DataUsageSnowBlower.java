package com.chervon.technology.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * 扫雪机上次设备使用情况记录
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_data_usage_snowblower")
@ApiModel(value = "设备使用数据", description = "扫雪机上次设备使用情况记录")
public class DataUsageSnowBlower extends BaseDo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("1032:设备开始工作时间戳")
    private Long workingTimeStart;

    @ApiModelProperty("1033：设备停止工作时间戳")
    private Long workingTimeEnd;

    @ApiModelProperty("工作持续时间，单位：秒")
    private Long workingTimeDuration;

    @ApiModelProperty("1034：当前工作的总耗电量")
    private Integer powerConsumed;

    @ApiModelProperty("历次平均耗电量")
    private Integer averagePowerConsumed;
    /**
     * 计算公式：workingTimeDuration * 380 / 1000
     */
    @ApiModelProperty("二氧化碳减排量跟实际使用时长相关，为380mg/s，22.8g/min，1.4kg/h")
    private Integer carbonDioxideEmissions;
    /**
     * 原始数据：{"4004":[{"ts_ms":1700637480000,"load":80},{"ts_ms":1700637480200,"load":70},{"ts_ms":1700637480400,"load":60}]}
     * 返回数组数据：[56,73,87,34,87,24,78,97,24,75,86,98,23,54,56,67,23,76,82,37,86,23,65,78,87,24,34,2,8,58]
     */
    @ApiModelProperty("4004：工作期间的负载数据(取工作时间段内50个采样平均值)")
    private String workLoad;

    @ApiModelProperty("上报的最新工作时间戳")
    private Long reportTime;

    @ApiModelProperty("数据状态：1.读取成功  2.处理中   3.数据已就绪")
    private Integer status;

    public void setWorkingTimeDuration(){
        workingTimeDuration=(workingTimeEnd-workingTimeStart)/1000;
    }
}
