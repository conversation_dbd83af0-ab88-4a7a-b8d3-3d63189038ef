package com.chervon.technology.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/2/25 15:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("charging")
public class Charging extends BaseDo implements Serializable {

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 类型  flow 流量  sms 短信
     */
    private String type;

    /**
     * 消息id
     */
    private Long msgId;

    /**
     * 规则引擎id，如果消息是通过规则引擎生产的，则记录规则引擎id
     */
    private Long ruleEngineId;

    /**
     * 消息标题多语言id
     */
    private Long msgTitleLangId;

    /**
     * 消息标题多语言code
     */
    private String msgTitleLangCode;

    /**
     * 消息类型
     */
    private String msgType;

}
