package com.chervon.technology.domain.dto;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-07-19 16:33
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class DeviceComponentListDto extends PageRequest implements Serializable {

    /**
     * 设备Id
     */
    @ApiModelProperty("设备Id")
    @NotEmpty
    private String deviceId;
}
