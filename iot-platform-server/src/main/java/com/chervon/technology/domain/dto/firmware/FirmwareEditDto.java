package com.chervon.technology.domain.dto.firmware;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @date 2022-07-11
 * <AUTHOR>
 */
@Data
public class FirmwareEditDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("固件Id")
    @NotNull
    private Long id;

    @ApiModelProperty("产品PID")
    @NotNull
    private Long productId;

    @ApiModelProperty("总成零件号")
    @NotNull
    private String componentNo;

    @ApiModelProperty("固件名称")
    @NotEmpty
    private String packageName;

    @ApiModelProperty("固件版本号")
    @NotEmpty
    private String packageVersion;

    @ApiModelProperty("最低兼容的版本")
    private String minimumVersion;

    @ApiModelProperty("固件类型, fullPackage:全包, deltaPackage:差分包")
    @NotEmpty
    private Integer packageType;

    @ApiModelProperty("升级方式 notForceUpgrade：非强制升级，forceUpgrade：强制升级，silenceUpgrade：静默升级")
    private String upgradeMethod;

    @ApiModelProperty("升级描述")
    private String description;

    @ApiModelProperty("更新内容")
    @NotEmpty
    private String packageContent;

    @ApiModelProperty("固件包存放在s3中的key")
    @NotEmpty
    private String url;

}
