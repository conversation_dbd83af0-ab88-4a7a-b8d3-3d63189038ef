package com.chervon.technology.domain.dto.product.model;

import com.chervon.technology.domain.dto.product.ParamDataDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ServiceAddDto implements Serializable {

    /**
     * 所属产品id
     */
    @ApiModelProperty("物模型所属产品id")
    @NotNull
    private Long productId;

    /**
     * 物模型项目唯一标识符
     **/
    @ApiModelProperty("物模型唯一标识符")
    @NotEmpty
    private String identifier;

    /**
     * 物模型项目名称
     **/
    @NotEmpty
    @ApiModelProperty("物模型名称")
    private String name;

    /**
     * 服务描述
     **/
    @ApiModelProperty("服务描述")
    private String desc;

    /**
     * 服务类型 async（异步调用）或sync（同步调用）
     **/
    @NotNull
    @ApiModelProperty("是否数据同步")
    private Boolean ifCallType;

    /**
     * 入参列表
     **/
    @ApiModelProperty("入参列表")
    private List<ParamDataDto> inputData;

    /**
     * 出参列表
     **/
    @ApiModelProperty("出参列表")
    private List<ParamDataDto> outputData;
}
