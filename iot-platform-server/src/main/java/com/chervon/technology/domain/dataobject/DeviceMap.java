package com.chervon.technology.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/8/8 11:15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("device_map")
public class DeviceMap extends BaseDo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("上传地图文件的设备id")
    private String deviceId;

    @ApiModelProperty("文件原始名称")
    private String originalFilename;

    @ApiModelProperty("下载文件key")
    private String fileKey;

    @ApiModelProperty("文件大小")
    private Long fileSize;

    @ApiModelProperty("文件类型")
    private String fileType;
}
