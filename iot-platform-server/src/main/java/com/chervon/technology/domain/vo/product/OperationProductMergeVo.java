package com.chervon.technology.domain.vo.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-08-03
 * 运营平台产品列表
 */
@Data
public class OperationProductMergeVo implements Serializable {

    private Long id;
    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品型号
     */
    private String model;

    /**
     * 商品型号Model#
     */
    @ApiModelProperty("商品型号Model#")
    private String commodityModel;

    @ApiModelProperty("产品SNCode")
    private String productSnCode;
    /**
     * 产品图标
     */
    private String productIcon;

    /**
     * 品类Id
     */
    private Long categoryId;
    /**
     * 品牌Id
     */
    private Long brandId;
    /**
     * 设备类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，gatewaySubDevice：网关子设备，
     * notIotDevice：非Iot设备，oldIotDevice：老iot设备
     */
    private String productType;

    /**
     * 产品发布状态
     * 无状态 -
     * 待发布 to_be_release
     * 发布审核 release_approve
     * 已发布 released
     * 发布被驳回 release_refuse
     * 更新审核中 update_release_approve
     * 更新被驳回 update_release_refuse
     * 下架审核中 off_released_approve
     * 下架被驳回 off_released_refuse
     * 已下架 off_released
     */
    private String releaseStatus;

    /**
     * 运营平台的备注
     */
    private String operationRemark;

    private String createBy;

    private String updateBy;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    @ApiModelProperty("创建类型:0 技术平台创建，1运营平台创建")
    private Integer createType;

    @ApiModelProperty("问卷模板：commonTemplate， extendedWarrantyTemplate，用逗号间隔")
    private String questionTemplate;


    @ApiModelProperty("图片类型：0图片上传，1图片链接")
    private Integer iconType;

    private Long draftId;

    private Integer draftDeleted;
    /**
     * 产品名称
     */
    private String draftProductName;

    /**
     * 商品型号Model#
     */
    @ApiModelProperty("商品型号Model#")
    private String draftCommodityModel;

    @ApiModelProperty("产品SNCode")
    private String draftProductSnCode;
    /**
     * 产品图标
     */
    private String draftProductIcon;

    /**
     * 品牌Id
     */
    private Long draftBrandId;

    /**
     * 产品发布状态
     * 无状态 -
     * 待发布 to_be_release
     * 发布审核 release_approve
     * 已发布 released
     * 发布被驳回 release_refuse
     * 更新审核中 update_release_approve
     * 更新被驳回 update_release_refuse
     * 下架审核中 off_released_approve
     * 下架被驳回 off_released_refuse
     * 已下架 off_released
     */
    private String draftReleaseStatus;

    /**
     * 运营平台的备注
     */
    private String draftOperationRemark;

    private String draftUpdateBy;

    private LocalDateTime draftUpdateTime;

    @ApiModelProperty("问卷模板：commonTemplate， extendedWarrantyTemplate，用逗号间隔")
    private String draftQuestionTemplate;

    @ApiModelProperty("图片类型：0图片上传，1图片链接")
    private Integer draftIconType;

    @ApiModelProperty("是否支持分享, false:不支持, true:支持")
    private Boolean isSharingSupported;
}
