package com.chervon.technology.domain.vo.ota;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 设备升级记录详情vo
 * <AUTHOR>
 * @className DeviceOtaDetailVo
 * @description
 * @date 2022/7/13 18:17
 */
@ApiModel("设备升级记录详情vo")
@Data
public class DeviceOtaDetailVo {
    /**
     * 总成零件号
     **/
    @ApiModelProperty("总成零件号")
    private String componentNo;

    /**
     * 总成零件名称
     **/
    @ApiModelProperty("总成零件名称")
    private String componentName;

    /**
     * 原始固件版本
     **/
    @ApiModelProperty("原始固件版本")
    private String oldVersion;

    /**
     * 目标固件版本
     **/
    @ApiModelProperty("目标固件版本")
    private String newVersion;

    /**
     * 总成固件升级状态  0: 等待升级 初始状态  1:下载中  2:升级中  3:升级成功  4:升级失败
     **/
    @ApiModelProperty("总成固件升级状态  0: 等待升级 初始状态  1:下载中  2:升级中  3:升级成功  4:升级失败")
    private Integer status;

    /**
     * status 的中文含义
     **/
    @ApiModelProperty("status 的中文含义")
    private String statusLabel;
    /**
     * 升级状态详情 status为1时表示下载进度  status为2时表示升级安装进度  status为4时表示升级失败原因 status为其他时无意义
     **/
    @ApiModelProperty("升级状态详情 status为1时表示下载进度  status为2时表示升级安装进度  status为4时表示升级失败原因 status为其他时无意义")
    private String detail;

    /**
     * 升级时间
     **/
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime upgradeTime;
}
