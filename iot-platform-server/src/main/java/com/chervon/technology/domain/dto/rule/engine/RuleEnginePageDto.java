package com.chervon.technology.domain.dto.rule.engine;

import com.chervon.common.core.domain.PageRequest;
import com.chervon.technology.api.enums.PushMethodEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-09-09 16:03
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class RuleEnginePageDto extends PageRequest implements Serializable {
    /**
     * 规则Id
     */
    @ApiModelProperty("规则Id")
    private String id;
    /**
     * 规则名称
     */
    @ApiModelProperty("规则名称")
    private String name;
    /**
     * 模板类型: 0 设备消息
     */
    @ApiModelProperty("模板类型: 0 设备消息")
    private Integer type;
    /**
     * 模板标题
     */
    @ApiModelProperty("模板标题")
    private String title;
    /**
     * 推送方式
     */
    @ApiModelProperty("推送方式")
    private PushMethodEnum pushMethod;

    @ApiModelProperty("适用app类型 1 ego 2 fleet")
    private List<Integer> businessType = new ArrayList<>();

    @ApiModelProperty(hidden = true)
    private String businessTypeStr;

    public String getBusinessTypeStr() {
        if (!businessType.isEmpty()) {
            return businessType.stream().sorted(Integer::compareTo).map(String::valueOf).collect(Collectors.joining(","));
        }
        return null;
    }
}
