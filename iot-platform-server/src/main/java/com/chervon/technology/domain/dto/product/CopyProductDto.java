package com.chervon.technology.domain.dto.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-07-11
 */
@Data
public class CopyProductDto implements Serializable {

    private static final long serialVersionUID = 6294666424397329496L;
    /**
     * 被拷贝的产品id
     */
    @NotNull
    @ApiModelProperty("被复制的原始产品Id")
    private Long sourcePid;

    /**
     * 产品型号
     */
    @NotEmpty
    @ApiModelProperty("产品型号")
    private String model;

    /**
     * 商业型号
     */
    @NotEmpty
    @ApiModelProperty("产品商业型号")
    private String commodityModel;

    /**
     * 通讯方式，wifiAndBle：Wifi+BLE，4GAndBle：4G+BLE，ble：BLE，DOrT：D/T，wifi：WIFI，4G：4G，lan：LAN，
     * notNetworked：不联网
     */
    @NotNull
    @ApiModelProperty("通讯方式，wifiAndBle：Wifi+BLE，4GAndBle：4G+BLE，ble：BLE，DOrT：D/T，wifi：WIFI，4G：4G，lan：LAN，notNetworked：不联网")
    private List<String> networkModes;

    /**
     * 设备类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，gatewaySubDevice：网关子设备，notIotDevice：非Iot设备，
     * oldIotDevice：老iot设备
     */
    @NotNull
    @ApiModelProperty("设备类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，gatewaySubDevice：网关子设备，notIotDevice：非Iot设备，oldIotDevice：老iot设备")
    private String productType;

    /**
     * 产品描述
     */
    @ApiModelProperty("产品描述")
    private String description;

    @ApiModelProperty("适用app类型 1 ego 2 fleet")
    private List<Integer> businessType = new ArrayList<>();

    @ApiModelProperty(hidden = true)
    private String businessTypeStr;

    @ApiModelProperty("是否支持分享, false:不支持, true:支持")
    private Boolean isSharingSupported;

    @ApiModelProperty("分享权限,  1:账户权限最高，2：设备权限最高")
    private Integer shareAuthorityType;

    public String getBusinessTypeStr() {
        if (!businessType.isEmpty()) {
            return businessType.stream()
                    .sorted(Integer::compareTo)
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
        }
        return null;
    }
}
