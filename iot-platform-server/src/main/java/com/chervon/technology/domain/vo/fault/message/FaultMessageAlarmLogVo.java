package com.chervon.technology.domain.vo.fault.message;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.chervon.common.core.domain.MultiLanguageVo;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date Created in 2022/9/20 9:56
 */
@Data
public class FaultMessageAlarmLogVo implements Serializable {
    @ApiModelProperty("设备ID")
    private String deviceId;
    @ApiModelProperty("设备昵称")
    private String nickName;
    @ApiModelProperty("物模型功能id")
    private String propertyId;
    @ApiModelProperty("告警名称")
    private MultiLanguageVo faultName;
    @ApiModelProperty("功能名称多语言id")
    private String multiLanguageId;
    @ApiModelProperty("告警时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime faultTime;
    @ApiModelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;
    @ApiModelProperty("修改时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;
    @ApiModelProperty("创建人")
    private String createBy;
    @ApiModelProperty("修改人")
    private String updateBy;
}
