package com.chervon.technology.domain.enums;

import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.config.ExceptionMessageUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022-07-22
 * 总成零件类型
 */
public enum ComponentTypeEnum {
    /**
     * mcu	MCU
     * subDevice	子设备
     * bleModule	蓝牙模组
     * 4gModule	4G
     */

    MCU("mcu", "MCU"),
    SUB_DEVICE("subDevice", "子设备"),
    BLE_MODULE("bleModule", "蓝牙模组"),
    MODULE_4G("4gModule", "4G");


    private String value;

    private String label;

    ComponentTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getLabel() {
        return label;
    }

    public String getValue() {
        return value;
    }

    /**
     * 根据匹配value的值获取Label
     *
     * @param value 枚举类型
     * @return 审批状态
     */
    public static String getLabelByValue(String value) {
        if (StringUtils.isBlank(value)) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_COMPONENT_TYPE_ERROR);
        }
        for (ComponentTypeEnum s : ComponentTypeEnum.values()) {
            if (value.equals(s.getValue())) {
                return s.getLabel();
            }
        }
        throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_COMPONENT_TYPE_ERROR);
    }

    /**
     * 获取StatusEnum
     *
     * @param value 枚举类型
     * @return 审批状态
     */
    public static ComponentTypeEnum getStatusEnum(String value) {
        for (ComponentTypeEnum s : ComponentTypeEnum.values()) {
            if (value.equals(s.getValue())) {
                return s;
            }
        }
        throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_COMPONENT_TYPE_ERROR);
    }
}
