package com.chervon.technology.domain.enums;

import org.apache.commons.lang3.StringUtils;

public enum EventEnum {
    /**
     * 告警
     */
    WARN("warn", "告警"),

    /**
     * 信息
     */
    INFO("info", "信息"),

    /**
     * 故障
     */
    ERROR("error", "故障");


    private String value;

    private String label;

    EventEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getLabel() {
        return label;
    }

    public String getValue() {
        return value;
    }

    /**
     * 根据匹配value的值获取Label
     *
     * @param value
     * @return
     */
    public static String getLabelByValue(String value) {
        if (StringUtils.isBlank(value)) {
            return "";
        }
        for (EventEnum s : EventEnum.values()) {
            if (value.equals(s.getValue())) {
                return s.getLabel();
            }
        }
        return "";
    }
}
