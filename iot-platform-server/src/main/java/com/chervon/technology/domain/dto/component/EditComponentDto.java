package com.chervon.technology.domain.dto.component;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022-07-11
 */
@Data
public class EditComponentDto implements Serializable {

    @ApiModelProperty("总成零件号Id")
    @NotNull
    private Long id;

    @ApiModelProperty("总成零件号")
    private String componentNo;

    @ApiModelProperty("总成零件名称")
    private String componentName;

    @ApiModelProperty("总成零件类型，mcu：MCU，subDevice：子设备，bleModule：蓝牙模组，4gModule：4G")
    private String componentType;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("软件编码")
    private String softwareCoding;
}
