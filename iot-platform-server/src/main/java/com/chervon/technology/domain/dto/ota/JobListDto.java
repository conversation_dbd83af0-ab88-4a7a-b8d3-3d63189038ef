package com.chervon.technology.domain.dto.ota;

import com.chervon.common.core.domain.PageRequest;
import com.chervon.technology.api.enums.OtaJobDevelopStatus;
import com.chervon.technology.domain.enums.UpgradeMode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @className PackageListDto
 * @description 任务列表搜索条件
 * @date 2022/7/13 11:06
 */
@ApiModel("任务列表搜索条件")
@Data
public class JobListDto extends PageRequest {

    /**
     * 产品pid
     **/
    @ApiModelProperty("产品pid")
    private Long productId;

    /**
     * 任务id
     **/
    @ApiModelProperty("任务id")
    private String jobId;

    /**
     * 技术固件版本号
     **/
    @ApiModelProperty("技术固件版本号")
    private String technicalVersion;

    /**
     * 总成零件号
     **/
    @ApiModelProperty("总成零件号")
    private String componentNo;

    /**
     * 升级方式 NOT_FORCE非强制升级 FORCE强制升级 SILENCE静默升级
     **/
    @ApiModelProperty("升级方式 NOT_FORCE非强制升级 FORCE强制升级 SILENCE静默升级")
    private UpgradeMode upgradeMode;

    /**
     * 升级任务的开发状态：DEVELOPING开发中 CLOSING封板审核中 CLOSE_REFUSED封板被驳回 CLOSED已封板
     **/
    @ApiModelProperty("升级任务的开发状态：DEVELOPING开发中 CLOSING封板审核中 CLOSE_REFUSED封板被驳回 CLOSED已封板")
    private OtaJobDevelopStatus developStatus;
}
