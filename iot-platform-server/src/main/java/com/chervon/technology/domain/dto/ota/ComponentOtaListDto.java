package com.chervon.technology.domain.dto.ota;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 查看总成固件升级记录
 * <AUTHOR>
 * @since 2022-07-29
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "查看总成固件升级记录", description = "查看总成固件升级记录")
public class ComponentOtaListDto extends PageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备id
     **/
    @NotNull
    @ApiModelProperty("设备id")
    private String deviceId;

    /**
     * 总成零件id
     **/
    @NotNull
    @ApiModelProperty("总成零件号")
    private String componentNo;

    /**
     * 升级时间范围开始
     **/
    @ApiModelProperty("升级时间范围开始")
    private Date startTime;

    /**
     * 升级时间范围结束
     **/
    @ApiModelProperty("升级时间范围结束")
    private Date endTime;

    /**
     * 原始固件版本
     **/
    @ApiModelProperty("原始固件版本")
    private String oldVersion;

    /**
     * 目标固件版本
     **/
    @ApiModelProperty("目标固件版本")
    private String newVersion;

    /**
     * 升级结果 0: 等待升级 初始状态  1:下载中  2:升级中  3:升级成功  4:升级失败
     **/
    @ApiModelProperty("0: 等待升级 初始状态  1:下载中  2:升级中  3:升级成功  4:升级失败")
    private Integer status;

    /**
     * 操作用户id
     **/
    @ApiModelProperty("操作用户id")
    private String userId;

}
