package com.chervon.technology.domain.enums;

import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.config.ExceptionMessageUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * 数值类型
 *
 * <AUTHOR>
 * @date 2022-07-22
 */
public enum DataTypeEnum {
    /**
     * bool	布尔
     * enum	枚举
     * number	数值
     * string	字符串
     */

    BOOL("bool", "布尔"),
    ENUM("enum", "枚举"),
    INT("int", "整形"),
    FLOAT("float", "浮点型"),
    STRING("string", "字符串");

    private String value;

    private String label;

    DataTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getLabel() {
        return label;
    }

    public String getValue() {
        return value;
    }

    /**
     * 根据匹配value的值获取Label
     *
     * @param value 枚举类型
     * @return 审批状态
     */
    public static String getLabelByValue(String value) {
        if (StringUtils.isBlank(value)) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_GET_DATA_TYPE_ERROR2);
        }
        for (DataTypeEnum s : DataTypeEnum.values()) {
            if (value.equals(s.getValue())) {
                return s.getLabel();
            }
        }
        throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_GET_DATA_TYPE_ERROR);
    }

    /**
     * 获取StatusEnum
     *
     * @param value 枚举类型
     * @return 审批状态
     */
    public static DataTypeEnum getStatusEnum(String value) {
        for (DataTypeEnum s : DataTypeEnum.values()) {
            if (value.equals(s.getValue())) {
                return s;
            }
        }
        throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_GET_DATA_TYPE_ERROR);
    }
}
