package com.chervon.technology.domain.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.chervon.common.mybatis.config.BaseDo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 总成零件
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-07
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("component")
@ApiModel(value = "component", description = "总成零件")
public class Component extends BaseDo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("总成零件号")
    private String componentNo;

    @ApiModelProperty("总成零件名称")
    private String componentName;

    @ApiModelProperty("总成零件类型，mcu：MCU，subDevice：子设备，bleModule：蓝牙模组，4gModule：4G")
    private String componentType;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("软件编码")
    @Deprecated
    private String softwareCoding;

}
