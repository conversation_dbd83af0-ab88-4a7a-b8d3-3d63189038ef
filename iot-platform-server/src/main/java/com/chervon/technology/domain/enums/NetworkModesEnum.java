package com.chervon.technology.domain.enums;

import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.config.ExceptionMessageUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022-07-22
 */
public enum NetworkModesEnum {

    /**
     * wifiAndBle	Wifi+BLE
     * 4GAndBle	4G+BLE
     * bleWifi4G BLE+WIFI+4G
     * ble	BLE
     * DOrT	D/T
     * wifi	WIFI
     * 4G	4G
     * lan	LAN
     * notNetworked	不联网
     */

    WIFI_AND_BLE("wifiAndBle", "Wifi+BLE"),
    BLE_AND_4G("4GAndBle", "4G+BLE"),
    BLE_WIFI_4G("bleWifi4G", "BLE+WIFI+4G"),
    BLE("ble", "BLE"),
    D_OR_T("DOrT", "D/T"),
    WIFI("wifi", "WIFI"),
    MODE_4G("4G", "4G"),
    LAN("lan", "LAN"),
    NOT_NETWORKED("notNetworked", "不联网");

    private String value;

    private String label;

    NetworkModesEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getLabel() {
        return label;
    }

    public String getValue() {
        return value;
    }

    /**
     * 根据匹配value的值获取Label
     *
     * @param value 枚举类型
     * @return 审批状态
     */
    public static String getLabelByValue(String value) {
        if (StringUtils.isBlank(value)) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_NETWORK_MODE_ERROR2);
        }
        for (NetworkModesEnum s : NetworkModesEnum.values()) {
            if (value.equals(s.getValue())) {
                return s.getLabel();
            }
        }
        throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_NETWORK_MODE_ERROR);
    }

    /**
     * 获取StatusEnum
     *
     * @param value 枚举类型
     * @return 审批状态
     */
    public static NetworkModesEnum getStatusEnum(String value) {
        for (NetworkModesEnum s : NetworkModesEnum.values()) {
            if (value.equals(s.getValue())) {
                return s;
            }
        }
        throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_NETWORK_MODE_ERROR);
    }
}
