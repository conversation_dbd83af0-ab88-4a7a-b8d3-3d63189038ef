package com.chervon.technology.domain.dto.device;

import com.chervon.common.core.domain.PageRequest;
import com.chervon.technology.api.enums.DeviceStatusEnum;
import com.chervon.technology.api.enums.DeviceUsageStatusEnum;
import com.chervon.technology.api.enums.OnlineStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 20220505
 * 设备搜索相关
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SearchDeviceDto extends PageRequest implements Serializable {

    @ApiModelProperty("激活时间筛选起始时间")
    private String activationStartTime;

    public String getActivationStartTime() {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(this.activationStartTime)) {
            LocalDateTime time = LocalDateTime.parse(this.activationStartTime + " 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return time.minusHours(this.getZone()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return null;
    }

    @ApiModelProperty("激活时间筛选结束时间")
    private String activationEndTime;

    public String getActivationEndTime() {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(this.activationEndTime)) {
            LocalDateTime time = LocalDateTime.parse(this.activationEndTime + " 23:59:59", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return time.minusHours(this.getZone()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return null;
    }

    @ApiModelProperty("最后登录时间筛选起始时间")
    private String lastLoginStartTime;

    public String getLastLoginStartTime() {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(this.lastLoginStartTime)) {
            LocalDateTime time = LocalDateTime.parse(this.lastLoginStartTime + " 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return time.minusHours(this.getZone()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return null;
    }

    @ApiModelProperty("最后登录时间筛选结束时间")
    private String lastLoginEndTime;

    public String getLastLoginEndTime() {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(this.lastLoginEndTime)) {
            LocalDateTime time = LocalDateTime.parse(this.lastLoginEndTime + " 23:59:59", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return time.minusHours(this.getZone()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return null;
    }

    @ApiModelProperty("设备Id")
    private String deviceId;

    @ApiModelProperty("SN码")
    private String sn;

    @ApiModelProperty("Pid")
    private String pid;

    @ApiModelProperty("品类id")
    private Long categoryId;

    @ApiModelProperty("品牌id")
    private Long brandId;

    @ApiModelProperty("产品型号")
    private String productModel;

    @ApiModelProperty("商品型号/Model #")
    private String commodityModel;

    @ApiModelProperty("设备类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，gatewaySubDevice：网关子设备，" +
            "notIotDevice：非Iot设备，oldIotDevice：老iot设备")
    private String productType;

    @ApiModelProperty("在线状态")
    private OnlineStatusEnum isOnline;

    @ApiModelProperty("设备状态：DISABLE 停用 NORMAL 正常")
    private DeviceStatusEnum status;

    @ApiModelProperty("使用状态")
    private DeviceUsageStatusEnum usageStatus;

}
