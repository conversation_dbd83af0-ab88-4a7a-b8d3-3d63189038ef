package com.chervon.technology.domain.vo.product;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.chervon.common.core.domain.MultiLanguageVo;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-07-06
 */
@Data
@NoArgsConstructor
public class ModelServiceVo implements Serializable {
    /**
     * 物模型项目唯一标识符
     **/
    @ApiModelProperty("物模型项目唯一标识符")
    private String identifier;

    /**
     * 物模型项目名称
     **/
    @ApiModelProperty("物模型项目名称")//多语言转String
    private String name;


    /**
     * 服务描述
     **/
    @ApiModelProperty("服务描述")
    private String desc;

    /**
     * 服务类型 async（异步调用）或sync（同步调用）
     **/
    @ApiModelProperty("是否数据同步")
    private Boolean ifCallType;

    /**
     * 服务对应的方法名称(根据identifier生成)
     **/
    @ApiModelProperty("服务对应的方法名称")
    private String method;

    /**
     * 入参列表
     **/
    @ApiModelProperty("入参列表")
    private List<ParamDataVo> inputData;

    /**
     * 出参列表
     **/
    @ApiModelProperty("出参列表")
    private List<ParamDataVo> outputData;

    @ApiModelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("更新者")
    private String updateBy;


    public ModelServiceVo(String identifier, String desc, Boolean callType) {
        this.identifier = identifier;
        this.desc = desc;
        this.ifCallType = callType;
    }
}
