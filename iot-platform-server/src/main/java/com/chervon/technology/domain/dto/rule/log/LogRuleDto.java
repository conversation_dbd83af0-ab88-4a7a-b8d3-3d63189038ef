package com.chervon.technology.domain.dto.rule.log;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 日志规则配置添加
 * <AUTHOR>
 * @since 2024-11-04 14:26
 **/
@Data
public class LogRuleDto implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private Long id;
    /**
     * 日志模板Id
     */
    @ApiModelProperty("日志模板Id")
    private Long logTemplateId;
    /**
     * 是否启用 1启用 0禁用
     */
    @ApiModelProperty("是否启用 1启用 0禁用")
    private Integer enabled;
    /**
     * 产品Id
     */
    @ApiModelProperty("产品Id")
    private Long productId;
    /**
     * 行匹配规则条件列表
     */
    @ApiModelProperty("行匹配规则条件列表")
    private List<LogRuleConditionDto> listRowCondition;

    /**
     * 是否启用防抖：0不启用， 1启用
     */
    @ApiModelProperty("是否启用防抖：0不启用， 1启用")
    private Integer shakeEnable;

    /**
     * 累计触发多少秒内(多少秒内触发多少次，只执行第一次)
     */
    @ApiModelProperty("防抖一个周期时间秒(多少秒内触发多少次，只执行第一次)")
    private Integer shakeTime;
    /**
     * 防抖触发次数(多少秒内触发多少次，只执行第一次)
     */
    @ApiModelProperty("防抖触发次数")
    private Integer shakeCount;
}
