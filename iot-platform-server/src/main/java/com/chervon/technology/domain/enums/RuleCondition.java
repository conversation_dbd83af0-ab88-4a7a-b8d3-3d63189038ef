package com.chervon.technology.domain.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 规则引擎判断规则
 *
 * <AUTHOR>
 * @date 12:11 2022/7/26
 **/
public enum RuleCondition {

    /**
     * 等于
     **/
    EQUALS("="),

    /**
     * 包含
     **/
    INCLUDE("IN"),

    /**
     * 大于
     **/
    GRATER_THAN(">"),

    /**
     * 小于
     **/
    LESS_THAN("<");

    private final String rule;

    RuleCondition(String rule) {
        this.rule = rule;
    }

    public String getRule() {
        return rule;
    }

    public static boolean checkExist(String name) {
        if (StringUtils.isBlank(name)) {
            return false;
        }
        for (RuleCondition s : RuleCondition.values()) {
            if (name.equals(s.name())) {
                return true;
            }
        }
        return false;
    }

}
