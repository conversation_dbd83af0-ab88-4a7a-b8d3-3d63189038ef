package com.chervon.technology.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.technology.api.enums.PushMethodEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 规则引擎 1-N 推送方式表
 *
 * <AUTHOR>
 * @since 2022-09-08 09:43
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("rule_engine_push_method")
public class RuleEnginePushMethod extends BaseDo {
    /**
     * 规则引擎Id
     */
    private Long ruleEngineId;
    /**
     * 推送方式
     */
    private PushMethodEnum pushMethod;
}
