package com.chervon.technology.domain.entity;

import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.technology.api.enums.PackageTypeEnum;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 固件管理
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-29
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "Firmware对象", description = "固件管理")
public class Firmware extends BaseDo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("产品PID")
    private Long productId;

    @ApiModelProperty("总成零件号")
    private String componentNo;

    @ApiModelProperty("固件名称")
    private String packageName;

    @ApiModelProperty("固件版本号")
    private String packageVersion;

    /**
     * 去除显示版本号
     */
    @Deprecated
    @ApiModelProperty("显示版本号")
    private String displayVersion;

    @ApiModelProperty("文件大小")
    private Long size;

    @ApiModelProperty("最低兼容的版本")
    private String minimumVersion;

    @ApiModelProperty("固件类型, FULL_PACKAGE:全包, DELTA_PACKAGE:差分包")
    private PackageTypeEnum packageType;

    @ApiModelProperty("固件包存放在s3中的key")
    private String packageKey;

    @ApiModelProperty("升级包的hash值")
    private String hash;

    @ApiModelProperty("升级任务id")
    private Long jobId;

    /**
     * 排序编号
     **/
    @ApiModelProperty("排序编号")
    private Integer orderNum;


    public static final String ID = "id";

    public static final String PRODUCT_ID = "product_id";

    public static final String COMPONENT_NO = "component_no";

    public static final String PACKAGE_NAME = "package_name";

    public static final String PACKAGE_VERSION = "package_version";

    public static final String DISPLAY_VERSION = "display_version";

    public static final String SIZE = "size";

    public static final String MINIMUM_VERSION = "minimum_version";

    public static final String PACKAGE_TYPE = "package_type";

    public static final String PACKAGE_KEY = "package_key";

    public static final String HASH = "hash";

    public static final String JOB_ID = "job_id";

    public static final String IS_DELETED = "is_deleted";

    public static final String CREATE_BY = "create_by";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_BY = "update_by";

    public static final String UPDATE_TIME = "update_time";

    public static final String ORDER_NUM = "order_num";

}
