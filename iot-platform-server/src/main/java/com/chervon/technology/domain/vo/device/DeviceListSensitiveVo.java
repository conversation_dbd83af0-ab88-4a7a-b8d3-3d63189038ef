package com.chervon.technology.domain.vo.device;

import cn.hutool.core.annotation.Alias;
import com.chervon.common.core.annotation.Sensitive;
import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.chervon.common.core.enums.SensitiveStrategy;
import com.chervon.technology.api.enums.DeviceOnlineStatusEnum;
import com.chervon.technology.api.enums.DeviceStatusEnum;
import com.chervon.technology.api.enums.DeviceUsageStatusEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sargeraswang.util.ExcelUtil.ExcelCell;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 20220505
 * 设备搜索相关
 */
@Data
public class DeviceListSensitiveVo implements Serializable {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty("记录ID")
	@Alias("记录ID")
	private String id;
	/**
	 * 设备ID
	 */
	@ApiModelProperty("设备ID")
	@Alias("设备ID")
	private String deviceId;
	/**
	 * SN
	 */
	@ApiModelProperty("SN")
	@Alias("SN")
	private String sn;
	/**
	 * PID
	 */
	@ApiModelProperty("PID")
	@Alias("PID")
	private Long pid;
	/**
	 * 品类名称
	 */
	@ApiModelProperty("品类名称")
	@Alias("品类名称")
	private String categoryName;
	/**
	 * 品牌名称
	 */
	@ApiModelProperty("品牌名称")
	@Alias("品牌名称")
	private String brandName;
	/**
	 * 产品型号
	 */
	@ApiModelProperty("产品型号")
	@Alias("产品型号")
	private String productModel;
	/**
	 * 商品型号/Model #
	 */
	@ApiModelProperty("商品型号/Model #")
	@Alias("商品型号/Model #")
	private String commodityModel;
	/**
	 * 产品类型
	 *
	 * @link com.chervon.technology.domain.enums.ProductTypeEnum
	 */
	@ApiModelProperty("产品类型")
	@Alias("产品类型")
	private String productType;
	/**
	 * 在线状态
	 */
	@ApiModelProperty("在线状态")
	@Alias("在线状态")
	private DeviceOnlineStatusEnum isOnline;
	/**
	 * 设备状态：DISABLE 停用 NORMAL 正常
	 */
	@ApiModelProperty("设备状态：DISABLE 停用 NORMAL 正常")
	@Alias("设备状态")
	private DeviceStatusEnum status;
	/**
	 * 使用状态
	 */
	@ApiModelProperty("使用状态")
	@Alias("使用状态")
	private DeviceUsageStatusEnum usageStatus;
	/**
	 * 设备激活时间，设备注册的时候设置设备为激活状态
	 */
	@ApiModelProperty("设备激活时间，设备注册的时候设置设备为激活状态")
	@Alias("激活时间")
	@JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
	private LocalDateTime activationTime;
	/**
	 * 设备最后一次上线时间
	 */
	@ApiModelProperty("设备最后一次上线时间")
	@Alias("最后上线时间")
	@JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
	private LocalDateTime lastLoginTime;
}
