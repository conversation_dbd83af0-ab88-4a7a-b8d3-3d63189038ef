package com.chervon.message.api.exception;

import com.chervon.common.core.exception.ErrorCodeI;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 系统：消息中心 108
 *
 * <AUTHOR>
 * @date 2022/8/23 14:50
 */
@Getter
@AllArgsConstructor
public enum MessageErrorCode implements ErrorCodeI {

    /**
     * 消息推送  001
     */
    MESSAGE_PUSH_UNKNOWN_DEVICE_TYPE("1080012001", "unknown device type", "unknown device type, deviceType: [%s] "),
    MESSAGE_PUSH_FCM_CONFIG_FILE_ERROR("1080012002", "FCM push config error", "FCM push file is not configured"),
    MESSAGE_PUSH_PARAM_ERROR("1080012003", "param error", "invalid message type, type: [%s]"),
    MESSAGE_NOT_EXIST("1080012004", "message does not exist", "message does not exist, messageDetail: %s"),
    MESSAGE_PUSH_ERROR("1080012005", "push custom message fail", "push custom message fail"),
    MESSAGE_PUSH_PARAM_ERROR2("1080012006", "param error", "param incomplete! productId or deviceId is null"),

    SEND_MAIL_ERROR("1080022001", "failed to send email", "failed to send email, please check email config: %s, mailBody: %s"),
    ;

    private final String code;

    private final String defaultMessage;

    private final String errorMessage;
}
