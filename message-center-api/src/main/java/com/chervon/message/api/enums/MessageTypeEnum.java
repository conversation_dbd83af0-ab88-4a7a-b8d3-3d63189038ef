package com.chervon.message.api.enums;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023年1月3日
 **/
@Getter
@AllArgsConstructor
public enum MessageTypeEnum {


	SYS_MSG(0, "系统消息"),
	MARKETING_MSG(1, "营销消息"),
	DEVICE_MSG(2, "设备消息"),
	FEEDBACK_MSG(3, "反馈消息"),
	SHARE_MSG(4, "分享消息");

	private static final Map<Integer, String> map = Maps.newHashMap();

	static {
		for (MessageTypeEnum value : MessageTypeEnum.values()) {
			map.put(value.value, value.label);
		}
	}

	public static String valueOfStatus(Integer status) {
		return Optional.ofNullable(map.get(status)).orElse("unknown");
	}

	private Integer value;
	private String label;


}
