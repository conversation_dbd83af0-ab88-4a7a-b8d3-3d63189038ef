package com.chervon.message.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024-09-03 10:16
 **/
@Getter
@AllArgsConstructor
public enum PushTypeHandlerEnum {
    /**
     * 墓碑
     */
    TOMBSTONE("墓碑", 0,2),
    /**
     * APP 弹窗
     */
    POPUP("APP弹框", 1,4),
    /**
     * APP BANNER
     */
    BANNER("APP BANNER", 2,3),
    /**
     * 短信
     */
    MESSAGE("短信", 4,5),
    /**
     * 邮箱
     */
    MAIL("邮箱", 5,7),
    /**
     * 电话语音
     */
    PHONE_VOICE("PHONE_VOICE",8,6),
    /**
     * 消息清除
     */
    MESSAGE_REMOVE("MESSAGE_REMOVE", 9,1)
    ;

    private String value;
    /**
     * 消息推送方式：0墓碑消息，1APP弹窗，2APP banner, 3消息管理
     * 用于给消息中心，推送消息方法调用
     * 其中站内消息是1,2
     */
    private Integer pushTypes;
    /**
     * 推送顺序优先级
     */
    private Integer priority;

    public static PushTypeHandlerEnum getByPushType(Integer value) {
        for (PushTypeHandlerEnum enumItem : PushTypeHandlerEnum.values()) {
            if (enumItem.getPushTypes().equals(value)) {
                return enumItem;
            }
        }
        return null;
    }

}
