package com.chervon.operation.domain.vo.group;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @className GroupVo
 * @description
 * @date 2022/7/19 15:51
 */
@Data
@ApiModel("获取分组设备vo")
public class GroupDeviceListVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备列表
     **/
    @ApiModelProperty("设备列表")
    private List<GroupDeviceVo> devices;

    /**
     * 下一页请求token
     **/
    @ApiModelProperty("下一页请求token")
    private String nextToken;
}
