package com.chervon.operation.domain.vo.faq.common;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.chervon.operation.domain.vo.faq.FaqVo;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/10/28 10:26
 */
@Data
@ApiModel(description = "通用faq管理分页数据")
public class CommonFaqManagePageVo {

    @ApiModelProperty(value = "通用faq id")
    private Long commonFaqId;

    @ApiModelProperty(value = "faq")
    private FaqVo faq;

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("更新者")
    private String updateBy;

    @ApiModelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;
}
