package com.chervon.operation.domain.dto.app;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/20 19:31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "app系统消息发布管理分页查询条件")
public class SysMsgReleasePageDto extends PageRequest implements Serializable {

    @ApiModelProperty(value = "推送方式，可以多个")
    private List<String> pushTypeCode;

    @ApiModelProperty(value = "发布状态code")
    private String statusCode;

    @ApiModelProperty(hidden = true)
    private String pushTypeCodes;

    @ApiModelProperty(value = "消息标题")
    private String title;

    @ApiModelProperty(value = "消息id")
    private String msgId;

    @ApiModelProperty(value = "推送开始最小时间")
    private String pushStartMinTime;

    @ApiModelProperty(value = "推送开始最大时间")
    private String pushStartMaxTime;

    @ApiModelProperty(value = "推送结束最小时间")
    private String pushEndMinTime;

    @ApiModelProperty(value = "推送结束最大时间")
    private String pushEndMaxTime;

    @ApiModelProperty(value = "申请人")
    private String applyBy;

    @ApiModelProperty(value = "审批人")
    private String approveBy;

    public String getPushStartMinTime() {
        if (StringUtils.isNotBlank(this.pushStartMinTime)) {
            LocalDateTime time = LocalDateTime.parse(this.pushStartMinTime + " 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return time.minusHours(this.getZone()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return null;
    }

    public String getPushEndMinTime() {
        if (StringUtils.isNotBlank(this.pushEndMinTime)) {
            LocalDateTime time = LocalDateTime.parse(this.pushEndMinTime + " 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return time.minusHours(this.getZone()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return null;
    }

    public String getPushStartMaxTime() {
        if (StringUtils.isNotBlank(this.pushStartMaxTime)) {
            LocalDateTime time = LocalDateTime.parse(this.pushStartMaxTime + " 23:59:59", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return time.minusHours(this.getZone()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return null;
    }

    public String getPushEndMaxTime() {
        if (StringUtils.isNotBlank(this.pushEndMaxTime)) {
            LocalDateTime time = LocalDateTime.parse(this.pushEndMaxTime + " 23:59:59", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return time.minusHours(this.getZone()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return null;
    }

}
