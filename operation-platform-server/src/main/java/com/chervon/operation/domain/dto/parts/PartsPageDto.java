package com.chervon.operation.domain.dto.parts;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/11/22 14:06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "配件管理分页请求对象")
public class PartsPageDto extends PageRequest {

    @ApiModelProperty("配件id")
    private String partsId;

    @ApiModelProperty("商品型号")
    private String commodityModel;

    @ApiModelProperty("配件名称")
    private String name;

    @ApiModelProperty("配件类型1")
    private String type1;

    @ApiModelProperty("配件类型2")
    private String type2;
}
