package com.chervon.operation.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.common.mybatis.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/8/20 18:41
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_msg")
public class SysMsg extends BaseDo {

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息标题多语言id
     */
    private Long titleLangId;

    /**
     * 消息标题多语言code
     */
    private String titleLangCode;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息内容多语言id
     */
    private Long contentLangId;

    /**
     * 消息内容多语言code
     */
    private String contentLangCode;

    /**
     * 路由地址
     */
    private String rutePath;

    /**
     * 生产分组code，英文逗号分割
     */
    private String prdGroup;

    /**
     * 测试分组code，英文逗号分割
     */
    private String testGroup;

    /**
     * 推送类型code，英文逗号分割
     */
    private String pushTypeCode;

    /**
     * 推送频率code
     */
    private String pushRateCode;

    /**
     * 开始时间时区
     */
    private String startZone;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 真实开始时间(UTC时区)
     */
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime realStartTime;

    /**
     * 结束时间时区
     */
    private String endZone;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 真实结束时间(UTC时区)
     */
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime realEndTime;

    /**
     * 父消息id
     */
    private Long fromId;

    /**
     * 发布状态code
     */
    private String statusCode;

    /**
     * 申请人
     */
    private String applyBy;

    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime applyTime;

    /**
     * 审核人
     */
    private String approvedBy;

    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime approvedTime;

    /**
     * 测试是否被驳回过 0 没有 1 有
     */
    private Integer testRefused;

    /**
     * 发布是否被驳回过 0 没有 1 有
     */
    private Integer releaseRefused;

    /**
     * 停止发布状态后，重新发布前，如果有编辑就不算停止发布后
     */
    private Integer stopRelease;

    /**
     * 推送成功条数
     */

    private Integer pushSuccessNum;

    /**
     * 推送失败条数
     */
    private Integer pushFailNum;

}
