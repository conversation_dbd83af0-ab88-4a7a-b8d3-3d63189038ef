package com.chervon.operation.domain.vo.operationguidance.product;

import com.chervon.operation.domain.vo.operationguidance.OperationGuidanceVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/10/29 14:57
 */
@Data
@ApiModel(description = "产品操作指导数据")
public class ProductOperationGuidanceVo {

    @ApiModelProperty(value = "操作指导")
    private OperationGuidanceVo operationGuidance;

    @ApiModelProperty(value = "是否是引用通用")
    private boolean ifCommon;

    @ApiModelProperty(value = "记录id-用于后续接口入参")
    private Long productOperationGuidanceId;

}
