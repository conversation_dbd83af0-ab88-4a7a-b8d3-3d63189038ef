package com.chervon.operation.domain.vo.link.parts;

import com.chervon.operation.domain.vo.link.LinkVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/22 15:49
 */
@Data
@ApiModel(description = "配件链接数据")
public class PartsLinkVo {

    @ApiModelProperty(value = "链接")
    private LinkVo link;

    @ApiModelProperty(value = "记录id-用于后续接口入参")
    private Long partsLinkId;
}
