package com.chervon.operation.domain.dto.parts;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chervon.common.core.utils.CsvUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022-08-18 10:03
 **/
@Data
public class PartsRead {

    @ExcelProperty("配件ID")
    private String partsId;

    public String getPartsId() {
        return CsvUtil.unFormat(this.partsId);
    }

    @ExcelProperty("商品型号Model #")
    private String commodityModel;

    public String getCommodityModel() {
        return CsvUtil.unFormat(this.commodityModel);
    }

    @ExcelProperty("配件名称")
    private String name;

    public String getName() {
        return CsvUtil.unFormat(this.name);
    }

    @ExcelProperty("配件图片链接")
    private String iconUrl;

    public String getIconUrl() {
        return CsvUtil.unFormat(this.iconUrl);
    }

    @ExcelProperty("配件类型1")
    private String type1;

    public String getType1() {
        return CsvUtil.unFormat(this.type1);
    }

    @ExcelProperty("配件类型2")
    private String type2;

    public String getType2() {
        return CsvUtil.unFormat(this.type2);
    }

    @ExcelProperty("备注")
    private String remark;

    public String getRemark() {
        return CsvUtil.unFormat(this.remark);
    }

    @ExcelProperty("产品短描述")
    private String shortDescription;

    @ExcelProperty("产品长描述")
    private String longDescription;

    @ExcelProperty("技术规格")
    private String technicalSpecification;

    @ExcelProperty("购买链接")
    private String link;

    public String getShortDescription() {
        return CsvUtil.unFormat(this.shortDescription);
    }

    public String getLongDescription() {
        return CsvUtil.unFormat(this.longDescription);
    }

    public String getTechnicalSpecification() {
        return CsvUtil.unFormat(this.technicalSpecification);
    }

    public String getLink() {
        return CsvUtil.unFormat(this.link);
    }
}
