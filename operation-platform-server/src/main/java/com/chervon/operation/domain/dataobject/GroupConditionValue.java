package com.chervon.operation.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.operation.api.enums.GroupConditionRule;
import com.chervon.operation.api.enums.GroupConditionTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 分组条件值表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
@Data
@Accessors(chain = true)
@TableName("group_condition_value")
@ApiModel(value = "GroupConditionValue对象", description = "分组条件值表")
public class GroupConditionValue extends BaseDo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("分组条件id")
    private Long groupConditionId;

    @ApiModelProperty("条件值")
    private String conditionValue;

}
