package com.chervon.operation.domain.dto.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022-08-01
 * 产品的发布操作
 */
@Data
@ApiModel(description = "产品下发布拒绝")
@NoArgsConstructor
public class ProductRefuseDto {

    @ApiModelProperty(value = "产品id")
    private Long pId;

    @ApiModelProperty(value = "原因")
    private String remark;

    public ProductRefuseDto(Long pId, String remark) {
        this.pId = pId;
        this.remark = remark;
    }
}