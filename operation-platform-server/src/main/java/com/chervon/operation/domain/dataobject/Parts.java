package com.chervon.operation.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 配件表
 *
 * <AUTHOR>
 * @date 2022-08-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("parts")
public class Parts extends BaseDo {

    /**
     * 商品型号Model#
     */
    private String commodityModel;

    /**
     * 配件名称
     */
    private String name;

    /**
     * 配件名称多语言id
     */
    private Long nameLangId;

    /**
     * 配件名称多语言code
     */
    private String nameLangCode;

    /**
     * 配件类型1
     */
    private String type1;

    /**
     * 配件类型
     */
    private String type2;

    /**
     * 备注
     */
    private String remark;

    /**
     * 备注多语言id
     */
    private Long remarkLangId;

    /**
     * 备注多语言code
     */
    private String remarkLangCode;

    /**
     * 产品短描述
     */
    private String shortDescription;

    /**
     * 产品短描述多语言id
     */
    private Long shortDescriptionLangId;

    /**
     * 产品短描述多语言code
     */
    private String shortDescriptionLangCode;

    /**
     * 产品长描述
     */
    private String longDescription;

    /**
     * 产品长描述多语言id
     */
    private Long longDescriptionLangId;

    /**
     * 产品长描述多语言code
     */
    private String longDescriptionLangCode;

    /**
     * 产品技术规格
     */
    private String technicalSpecification;

    /**
     * 产品技术规格多语言id
     */
    private Long technicalSpecificationLangId;

    /**
     * 产品技术规格多语言code
     */
    private String technicalSpecificationLangCode;

    /**
     * 图片类型
     */
    private String iconType;

    /**
     * 图片地址
     */
    private String iconUrl;

    /**
     * 上传的图片名
     */
    private String uploadIconName;

    @TableField(exist = false)
    private String link;
}
