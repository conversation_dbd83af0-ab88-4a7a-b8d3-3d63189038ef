package com.chervon.operation.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.common.mybatis.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 通用帮助中心和商品关系表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("help_faq_model")
public class HelpFaqModel extends BaseDo {
    /**
     * faq id
     */
    private Long helpFaqId;
    /**
     * 商品型号
     */
    private String commodityModel;
}
