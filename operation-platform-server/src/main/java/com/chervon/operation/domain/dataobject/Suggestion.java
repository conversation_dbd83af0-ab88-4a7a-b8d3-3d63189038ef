package com.chervon.operation.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/11/30 14:13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("suggestion")
public class Suggestion extends BaseDo {

    /**
     * 标题
     */
    private String title;

    /**
     * 标题多语言id
     */
    private Long titleLangId;

    /**
     * 标题多语言code
     */
    private String titleLangCode;

    /**
     * 内容
     */
    private String content;

    /**
     * 内容多语言id
     */
    private Long contentLangId;

    /**
     * 内容多语言code
     */
    private String contentLangCode;

    /**
     * 附加内容
     */
    private String extra;

}
