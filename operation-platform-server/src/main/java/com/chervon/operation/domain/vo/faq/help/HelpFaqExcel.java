package com.chervon.operation.domain.vo.faq.help;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/17 12:27
 */

@Data
public class HelpFaqExcel {

    @Alias("帮助ID")
    private Long helpFaqId;

    @<PERSON>as("标题")
    private String title;

    @<PERSON>as("商品型号")
    private String model;

    @Alias("数据来源")
    private String source;

    @Alias("阅读量")
    private Integer readCount;

    @Alias("点赞量")
    private Integer praiseCount;

    @Alias("app显示状态")
    private String appShow;

    @Alias("同步时间")
    private String syncTime;

    @<PERSON>as("创建人")
    private String createBy;

    @Alias("创建时间")
    private String createTime;

    @Alias("修改人")
    private String updateBy;

    @Alias("修改时间")
    private String updateTime;

}
