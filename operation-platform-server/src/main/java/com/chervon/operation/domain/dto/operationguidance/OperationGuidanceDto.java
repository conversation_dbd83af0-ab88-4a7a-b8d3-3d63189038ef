package com.chervon.operation.domain.dto.operationguidance;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/10/27 16:04
 */
@Data
@ApiModel(description = "操作指导对象")
public class OperationGuidanceDto {

    @ApiModelProperty(value = "操作指导id，新增不必填", hidden = true)
    private Long operationGuidanceId;

    @ApiModelProperty(value = "文件类型code")
    private String typeCode;

    @ApiModelProperty(value = "文件名称")
    private String name;

    @ApiModelProperty(value = "文件名称多语言id，新增不必填")
    private Long nameLangId;

    @ApiModelProperty(value = "文件描述")
    private String description;

    @ApiModelProperty(value = "文件描述多语言id，新增不必填")
    private Long descriptionLangId;

    @ApiModelProperty(value = "文件格式")
    private String format;

    @ApiModelProperty(value = "文件大小，字节")
    private Long size;

    @ApiModelProperty(value = "文件地址")
    private String url;

    @ApiModelProperty(value = "上传的文件名")
    private String uploadFileName;

}
