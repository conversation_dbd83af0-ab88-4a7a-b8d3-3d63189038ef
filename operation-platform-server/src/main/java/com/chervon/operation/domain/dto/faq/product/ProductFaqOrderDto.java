package com.chervon.operation.domain.dto.faq.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/29 15:27
 */
@Data
@ApiModel(description = "faq排序参数")
public class ProductFaqOrderDto {

    @ApiModelProperty(value = "产品id")
    private Long productId;

    @ApiModelProperty(value = "排序后的产品faq id集合")
    private List<Long> productFaqIds;
}
