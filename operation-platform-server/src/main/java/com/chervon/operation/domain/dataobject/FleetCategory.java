package com.chervon.operation.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2023-08-10 19:07
 **/
@Data
@TableName("t_fleet_category")
@EqualsAndHashCode(callSuper = true)
public class FleetCategory extends BaseDo {

    /**
     * 品类编码，唯一
     */
    private String code;
    /**
     * 品类名称
     */
    private String categoryName;
    /**
     * 语言
     */
    private String language;
    /**
     * 父级品类编码
     */
    private String parentCode;
    /**
     * 品类级别：一级二级
     */
    private Integer level;
    /**
     * 品类描述
     */
    private String description;
    /**
     * 业务定义品类分类 1 tool 2 charger 3 battery
     */
    private Integer customType;
    /**
     * iot设备标记：0非iot设备  1是iot设备
     */
    private Integer iotFlag;


}
