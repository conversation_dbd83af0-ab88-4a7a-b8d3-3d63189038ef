package com.chervon.operation.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/11/10 17:39
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("android_version")
public class AndroidVersion extends BaseDo {

    /**
     * 版本号
     */
    private String version;

    /**
     * 版本名称
     */
    private String name;

    /**
     * 更新内容
     */
    private String updateContent;

    /**
     * 更新内容多语言id
     */
    private Long updateContentLangId;

    /**
     * 更新内容多语言code
     */
    private String updateContentLangCode;

    /**
     * 适用app类型 1 ego 2 fleet
     */
    private Integer businessType;

}
