package com.chervon.operation.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 配件清单
 *
 * <AUTHOR>
 * @date 2022-08-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_parts")
public class ProductParts extends BaseDo {

    /**
     * 产品Id
     */
    private Long productId;

    /**
     * 配件Id
     */
    private Long partsId;

    /**
     * 维保类型 1 日期 2 工时
     */
    private Integer maintenanceType;

    /**
     * 维保周期
     */
    private Integer maintenancePeriod;

    /**
     * 维保提醒
     */
    private Integer maintenanceRemind;

    /**
     * 排序号
     */
    private Integer orderNum;


    /**
     * 实体id
     */
    private Long instanceId;
}
