package com.chervon.operation.domain.vo.faq.help;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/28 10:26
 */
@Data
@ApiModel(description = "帮助中心faq分页数据")
public class HelpFaqPageVo {

    @ApiModelProperty("帮助ID")
    private Long helpFaqId;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("商品型号")
    private List<String> model = new ArrayList<>();

    @ApiModelProperty("数据来源code")
    private String sourceCode;

    @ApiModelProperty("阅读量")
    private Integer readCount;

    @ApiModelProperty("点赞量")
    private Integer praiseCount;

    @ApiModelProperty("app显示状态")
    private String appShowCode;

    @ApiModelProperty("同步时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime syncTime;

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("更新者")
    private String updateBy;

    @ApiModelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

}
