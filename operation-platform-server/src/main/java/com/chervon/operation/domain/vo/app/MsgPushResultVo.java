package com.chervon.operation.domain.vo.app;

import com.chervon.common.core.annotation.Sensitive;
import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.chervon.common.core.enums.SensitiveStrategy;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/11/25 10:48
 */
@Data
@ApiModel(description = "app消息推送结果")
public class MsgPushResultVo {

    @ApiModelProperty(value = "推送方式")
    private String pushType;

    @ApiModelProperty("推送时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime pushTime;

    @ApiModelProperty(value = "用户id")
    @Sensitive(strategy = SensitiveStrategy.USER_ID)
    private String userId;

    @ApiModelProperty(value = "推送结果")
    private Boolean pushResult;
}
