package com.chervon.operation.domain.dto.template;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 日志模板添加Dto
 *
 * <AUTHOR>
 * @since 2024-11-04 11:07
 **/
@Data
public class LogTemplateQueryDto extends PageRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 模板Id
     */
    @ApiModelProperty("模板Id")
    private Long id;
    /**
     * 模板名称
     */
    @ApiModelProperty("模板名称")
    private String name;
    /**
     * 模板标题
     */
    @ApiModelProperty("模板标题")
    private String title;

}
