package com.chervon.operation.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2022-08-25 16:20
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("device_agreement_group")
public class DeviceAgreementGroup extends BaseDo {
    /**
     * 协议表主键Id(不是协议Id)
     */
    @ApiModelProperty("协议表主键Id(不是协议Id)")
    private Long agreementId;
    /**
     * 分组名称
     */
    @ApiModelProperty("分组名称")
    private String groupName;
}
