package com.chervon.operation.domain.dto.androidversion;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/11/10 16:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "安卓版本分页请求对象")
public class AndroidVersionPageDto extends PageRequest {

    @ApiModelProperty(value = "安卓版本id")
    private String androidVersionId;

    @ApiModelProperty(value = "版本号")
    private String version;

    @ApiModelProperty(value = "版本名称")
    private String name;

    @ApiModelProperty("适用app类型 1 ego 2 fleet，默认查全部")
    private Integer businessType;

}
