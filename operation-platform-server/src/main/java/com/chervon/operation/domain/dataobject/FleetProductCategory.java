package com.chervon.operation.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2023-08-10 19:07
 **/
@Data
@TableName("t_fleet_product_category")
@EqualsAndHashCode(callSuper = true)
public class FleetProductCategory extends BaseDo {

    /**
     * 产品商品型号
     */
    private String productModel;
    /**
     * 一级品类编码
     */
    private String firstCategoryCode;
    /**
     * 二级品类编码
     */
    private String secondCategoryCode;
}
