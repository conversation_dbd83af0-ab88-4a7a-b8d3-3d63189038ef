package com.chervon.operation.domain.dto.device.agreement;

import com.chervon.operation.config.OperationCommon;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-08-25 15:47
 **/
@Data
public class DeviceAgreementVersionAddDto implements Serializable {
    /**
     * 协议主键Id
     */
    @ApiModelProperty("协议主键Id")
    @NotNull
    private Long id;
    /**
     * 产品Id
     */
    @ApiModelProperty("产品Id")
    @NotNull
    private Long productId;
    /**
     * 协议版本号
     */
    @ApiModelProperty("协议版本号")
    @NotEmpty
    @Pattern(regexp = OperationCommon.DEVICE_AGREEMENT_VERSION_REGEX)
    private String version;
    /**
     * 判断条件
     */
    @ApiModelProperty("判断条件")
    private List<String> groupNameList;
    /**
     * 协议内容
     */
    @ApiModelProperty("协议内容")
    private String content;
}
