package com.chervon.operation.domain.dto.faq.common;

import com.chervon.operation.domain.dto.faq.FaqDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/27 15:58
 */
@Data
@ApiModel(description = "通用faq")
public class CommonFaqDto {

    @ApiModelProperty(value = "faq实体")
    private FaqDto faq;

    @ApiModelProperty(value = "通用faq id，新增不必填")
    private Long commonFaqId;

}
