package com.chervon.operation.domain.vo.product;

import cn.hutool.core.annotation.Alias;
import com.chervon.common.core.utils.CsvUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022-08-15
 */
@Data
public class ProductExportVo implements Serializable {

    @ApiModelProperty("产品Id")
    @Alias("PID")
    private Long id;

    @ApiModelProperty("品类名称")
    @Alias("品类名称")
    private String categoryName;

    @ApiModelProperty("产品model")
    @Alias("产品型号")
    private String model;

    @ApiModelProperty("商品型号Model#")
    @Alias("商品型号/Model #")
    private String commodityModel;

    @ApiModelProperty("设备类型：设备类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，gatewaySubDevice：网关子设备，notIotDevice：非Iot设备，oldIotDevice：老iot设备")
    @Alias("设备类型")
    private String productType;

    @ApiModelProperty("产品发布状态:无状态 -, 待发布 to_be_release, 发布审核 release_approve, 已发布 released，发布被驳回 release_refuse， ")
    @Alias("发布状态")
    private String releaseStatus;

    @ApiModelProperty("产品图片")
    @Alias("产品图片")
    private String productIcon;

    @ApiModelProperty("品牌名称")
    @Alias("品牌名称")
    private String brandName;

    @ApiModelProperty("产品名称")
    @Alias("产品名称")
    private String productName;

    @ApiModelProperty("产品snCode")
    @Alias("SN code")
    private String productSnCode;

    @ApiModelProperty("创建者")
    @Alias("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    @Alias("创建时间")
    private String createTime;

    @ApiModelProperty("更新者")
    @Alias("修改人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    @Alias("修改时间")
    private String updateTime;

    @ApiModelProperty("运营平台备注")
    @Alias("备注")
    private String operationRemark;

    public String getId() {
        return CsvUtil.format(this.id == null ? "" : this.id + "");
    }

    public String getCategoryName() {
        return CsvUtil.format(this.categoryName);
    }

    public String getModel() {
        return CsvUtil.format(this.model);
    }

    public String getCommodityModel() {
        return CsvUtil.format(this.commodityModel);
    }

    public String getProductType() {
        return CsvUtil.format(this.productType);
    }

    public String getReleaseStatus() {
        return CsvUtil.format(this.releaseStatus);
    }

    public String getProductIcon() {
        return CsvUtil.format(this.productIcon);
    }

    public String getBrandName() {
        return CsvUtil.format(this.brandName);
    }

    public String getProductName() {
        return CsvUtil.format(this.productName);
    }

    public String getProductSnCode() {
        return CsvUtil.format(this.productSnCode);
    }

    public String getCreateBy() {
        return CsvUtil.format(this.createBy);
    }

    public String getCreateTime() {
        return CsvUtil.format(this.createTime);
    }

    public String getUpdateBy() {
        return CsvUtil.format(this.updateBy);
    }

    public String getUpdateTime() {
        return CsvUtil.format(this.updateTime);
    }

    public String getOperationRemark() {
        return CsvUtil.format(this.operationRemark);
    }
}
