package com.chervon.operation.domain.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 配件类型
 * <AUTHOR>
 */
public enum PartsTypeEnum {
    /**
     * 通用配件:common_parts_type, 专用配件:special_parts_type,
     *             电池包充电器：battery_pack_charger
     */
    COMMON_PARTS_TYPE("common_parts_type", "通用配件"),
    SPECIAL_PARTS_TYPE("special_parts_type", "专用配件"),
    BATTERY_PACK_CHARGER("battery_pack_charger", "电池包充电器");
    private String value;

    private String label;

    PartsTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getLabel() {
        return label;
    }

    public String getValue() {
        return value;
    }
    public static String getValueByLabel(String label) {
        if (StringUtils.isBlank(label)) {
            return"";
        }
        for (PartsTypeEnum s : PartsTypeEnum.values()) {
            if (label.equals(s.getLabel())) {
                return s.getValue();
            }
        }
        return"";
    }
}
