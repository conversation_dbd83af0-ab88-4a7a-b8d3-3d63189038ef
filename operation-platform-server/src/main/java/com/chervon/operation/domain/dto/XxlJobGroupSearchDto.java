package com.chervon.operation.domain.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> yimin.du
 * @since 2022/12/20 16:39
 */
@Data
public class XxlJobGroupSearchDto implements Serializable {
    private Integer recordsFiltered;
    private List<JobGroupDto> data;
    private Integer recordsTotal;

    @Data
    public static class JobGroupDto implements Serializable {
        private Integer id;
        private String appname;
        private String title;
        private Integer addressType;
        private String addressList;
        private LocalDateTime updateTime;
        private List<String> registryList;
    }

}
