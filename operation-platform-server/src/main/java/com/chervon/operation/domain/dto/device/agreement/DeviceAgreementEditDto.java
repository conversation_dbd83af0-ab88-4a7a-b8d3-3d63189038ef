package com.chervon.operation.domain.dto.device.agreement;

import com.chervon.common.core.domain.MultiLanguageVo;
import com.chervon.operation.api.enums.DeviceAgreementTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-08-25 14:57
 **/
@Data
public class DeviceAgreementEditDto implements Serializable {
    /**
     * id
     */
    @ApiModelProperty("id")
    @NotNull
    private Long id;
    /**
     * 产品Id
     */
    @ApiModelProperty("产品Id")
    @NotNull
    private Long productId;
    /**
     * 协议名称
     */
    @ApiModelProperty("协议名称")
    @NotNull
    private MultiLanguageVo name;
    /**
     * 协议版本号
     */
    @ApiModelProperty("协议版本号")
    @NotNull
    private String version;
    /**
     * 协议类型
     */
    @ApiModelProperty("协议类型")
    @NotNull
    private DeviceAgreementTypeEnum type;
    /**
     * 判断条件
     */
    @ApiModelProperty("判断条件")
    private List<String> groupNameList;
    /**
     * 协议内容
     */
    @ApiModelProperty("协议内容")
    private MultiLanguageVo content;
}
