package com.chervon.operation.domain.dto.faq;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/10/27 16:04
 */
@Data
@ApiModel(description = "faq对象")
public class FaqDto {

    @ApiModelProperty(value = "faq id", hidden = true)
    private Long faqId;

    @ApiModelProperty(value = "问题类型")
    private String typeCode;

    @ApiModelProperty(value = "题目")
    private String title;

    @ApiModelProperty(value = "题目多语言id")
    private Long titleLangId;

    @ApiModelProperty(value = "答案")
    private String answer;

    @ApiModelProperty(value = "答案多语言id")
    private Long answerLangId;

    @ApiModelProperty(value = "typeCode是否必要", hidden = true)
    private boolean needTypeCode = true;

}
