package com.chervon.operation.domain.vo.faq.common;

import com.chervon.operation.domain.vo.faq.FaqVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/27 16:46
 */
@Data
@ApiModel(description = "通用faq数据")
public class CommonFaqVo {

    @ApiModelProperty(value = "通用faq id")
    private Long commonFaqId;

    @ApiModelProperty(value = "faq")
    private FaqVo faq;

    @ApiModelProperty(value = "测试分组")
    private List<String> testGroup;
}
