package com.chervon.operation.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产品用户手册
 *
 * <AUTHOR>
 * @date 2022/10/24 16:03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_manual")
public class ProductManual extends BaseDo {

    /**
     * 用户手册id
     */
    private Long manualId;


    /**
     * 产品id
     */
    private Long productId;

}
