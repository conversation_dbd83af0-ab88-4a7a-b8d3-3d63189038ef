package com.chervon.operation.config;

import com.chervon.operation.api.exception.OperationErrorCode;
import com.chervon.operation.api.exception.OperationException;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/7/23 16:37
 */
@Getter
public enum MarketingMsgOperationEnum {

    /**
     * 查看
     */
    VIEW("view"),
    /**
     * 修改
     */
    UPDATE("update"),
    /**
     * 删除
     */
    DELETE("delete"),
    /**
     * 复制
     */
    COPY("copy"),

    /**
     * 申请发布
     */
    APPLY_RELEASE("apply_release"),
    /**
     * 撤销发布申请
     */
    CANCEL_APPLY_RELEASE("cancel_apply_release"),
    /**
     * 确认测试
     */
    ENSURE_TEST("ensure_test"),
    /**
     * 封板测试
     */
    REFUSE_TEST("refuse_test"),
    /**
     * 确认发布
     */
    ENSURE_RELEASE("ensure_release"),
    /**
     * 驳回发布
     */
    REFUSE_RELEASE("refuse_release"),
    /**
     * 申请停止发布
     */
    APPLY_STOP_RELEASE("apply_stop_release"),
    /**
     * 撤销停止发布申请
     */
    CANCEL_APPLY_STOP_RELEASE("cancel_apply_stop_release"),
    /**
     * 确认停止发布
     */
    ENSURE_STOP_RELEASE("ensure_stop_release"),
    /**
     * 驳回停止发布
     */
    REFUSE_STOP_RELEASE("refuse_stop_release"),
    /**
     * 查看发布被驳回原因
     */
    VIEW_REFUSE_RELEASE_REASON("view_refuse_release_reason"),
    /**
     * 查看测试被驳回原因
     */
    VIEW_REFUSE_TEST_REASON("view_refuse_test_reason"),
    /**
     * 查看停止发布被驳回原因
     */
    VIEW_REFUSE_STOP_RELEASE_REASON("view_refuse_stop_release_reason"),

    ;

    private final String name;

    MarketingMsgOperationEnum(String name) {
        this.name = name;
    }

    public static MarketingMsgOperationEnum getFromName(String name) {
        for (MarketingMsgOperationEnum e : MarketingMsgOperationEnum.values()) {
            if (e.getName().equals(name)) {
                return e;
            }
        }
        throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_APP_MARKETING_MSG_OPERATE_ILLEGAL, name);
    }

}
