package com.chervon.operation.controller;

import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.operation.api.vo.SuggestionVo;
import com.chervon.operation.domain.dto.suggestion.SuggestionDto;
import com.chervon.operation.domain.dto.suggestion.SuggestionPageDto;
import com.chervon.operation.domain.dto.suggestion.SuggestionRead;
import com.chervon.operation.domain.vo.suggestion.SuggestionExcel;
import com.chervon.operation.domain.vo.suggestion.SuggestionPageVo;
import com.chervon.operation.service.SuggestionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/25 11:22
 */
@Api(tags = "处理建议接口")
@RestController
@Slf4j
@RequestMapping("/suggestion")
public class SuggestionController {

    private final SuggestionService suggestionService;

    public SuggestionController(SuggestionService suggestionService) {
        this.suggestionService = suggestionService;
    }

    @ApiOperation("处理建议-分页查询")
    @PostMapping("page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<SuggestionPageVo> page(@RequestBody SuggestionPageDto req) {
        return suggestionService.page(req);
    }

    @ApiOperation("处理建议-新增")
    @PostMapping("add")
    @Log(businessType = BusinessType.INSERT)
    public void add(@RequestBody SuggestionDto req) {
        suggestionService.add(req);
    }

    @ApiOperation("处理建议-编辑")
    @PostMapping("edit")
    @Log(businessType = BusinessType.EDIT)
    public void edit(@RequestBody SuggestionDto req) {
        suggestionService.edit(req);
    }

    @ApiOperation("处理建议-详情-传入suggestionId")
    @PostMapping("detail")
    @Log(businessType = BusinessType.VIEW)
    public SuggestionVo detail(@RequestBody SingleInfoReq<Long> req) {
        return suggestionService.detail(req.getReq());
    }

    @ApiOperation("处理建议-删除-传入suggestionId")
    @PostMapping("delete")
    @Log(businessType = BusinessType.DELETE)
    public void delete(@RequestBody SingleInfoReq<Long> req) {
        suggestionService.delete(req.getReq());
    }

    @ApiOperation(value = "处理建议-模板下载")
    @PostMapping("template")
    @Log(businessType = BusinessType.DOWNLOAD)
    public void template(HttpServletResponse response) throws IOException {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("suggestion_template", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream(), SuggestionRead.class).autoCloseStream(Boolean.FALSE).sheet("sheet1")
                    .doWrite(new ArrayList<SuggestionRead>() {{
                        add(new SuggestionRead());
                    }});
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(R.fail("下载模板失败")));
        }
    }

    @ApiOperation(value = "处理建议-导入多语言")
    @PostMapping("import")
    @Log(businessType = BusinessType.IMPORT)
    public List<String> importSuggestion(@RequestParam(value = "file") MultipartFile file) {
        return suggestionService.importSuggestion(file);
    }

    @ApiOperation(value = "导出")
    @PostMapping("export")
    @Log(businessType = BusinessType.EXPORT)
    public void export(@RequestBody SuggestionPageDto req, HttpServletResponse response) throws IOException {
        try {
            List<SuggestionExcel> data = suggestionService.listData(req);
            if (CollectionUtils.isEmpty(data)) {
                data = new ArrayList<>();
                data.add(new SuggestionExcel());
            }
            response.setContentType(ContentType.APPLICATION_OCTET_STREAM.toString());
            response.setCharacterEncoding("UTF-8");
            //进行下载
            String fileName = URLEncoder.encode("Suggestion-" + new SimpleDateFormat("yyyyMMdd").format(new Date()), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            // 加上UTF-8文件的标识字符
            response.getWriter().write(new String(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF}));
            CsvWriter csvWriter = CsvUtil.getWriter(response.getWriter());
            csvWriter.writeBeans(data);
            csvWriter.close();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().println(JSON.toJSONString(R.fail("下载文件失败")));
        }
    }

}
