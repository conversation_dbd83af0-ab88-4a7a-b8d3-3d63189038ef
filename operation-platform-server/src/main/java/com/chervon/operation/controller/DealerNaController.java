package com.chervon.operation.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.operation.api.exception.OperationErrorCode;
import com.chervon.operation.common.CommonConstant;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.domain.dto.DealerNaDto;
import com.chervon.operation.domain.dto.DealerNaPageDto;
import com.chervon.operation.domain.dto.DealerNaRead;
import com.chervon.operation.domain.vo.DealerNaPageVo;
import com.chervon.operation.domain.vo.DealerNaVo;
import com.chervon.operation.service.DealerNaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.chervon.operation.api.exception.OperationErrorCode.OPERATION_DEALER_READ_EXCEL_EMPTY;
import static com.chervon.operation.api.exception.OperationErrorCode.OPERATION_DEALER_READ_EXCEL_UPPER_LIMIT;

/**
 * <AUTHOR>
 * @since 2022-08-15 14:18
 **/
@Slf4j
@Api(tags = "北美经销商管理")
@RestController
@AllArgsConstructor
@RequestMapping("/dealer/na")
public class DealerNaController {

    private final DealerNaService dealerNaService;

    @ApiOperation("分页接口")
    @PostMapping("page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<DealerNaPageVo> page(@RequestBody DealerNaPageDto req) {
        return dealerNaService.page(req);
    }

    @ApiOperation("详情接口")
    @PostMapping("detail")
    @Log(businessType = BusinessType.VIEW)
    public DealerNaVo detail(@RequestBody SingleInfoReq<Long> req) {
        return dealerNaService.detail(req.getReq());
    }

    @ApiOperation("新增接口")
    @PostMapping("add")
    @Log(businessType = BusinessType.INSERT)
    public void add(@RequestBody DealerNaDto req) {
        dealerNaService.add(req);
    }

    @ApiOperation("编辑接口")
    @PostMapping("edit")
    @Log(businessType = BusinessType.EDIT)
    public void edit(@RequestBody DealerNaDto req) {
        dealerNaService.edit(req);
    }

    @ApiOperation("删除接口")
    @PostMapping("delete")
    @Log(businessType = BusinessType.DELETE)
    public void delete(@RequestBody SingleInfoReq<Long> req) {
        dealerNaService.delete(req.getReq());
    }

    @ApiOperation(value = "模板下载")
    @PostMapping("template")
    @Log(businessType = BusinessType.DOWNLOAD)
    public void template(HttpServletResponse response) throws IOException {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("dealer_na_template", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream(), DealerNaRead.class).autoCloseStream(Boolean.FALSE).sheet("sheet1")
                    .doWrite(new ArrayList<DealerNaRead>() {{
                        add(new DealerNaRead());
                    }});
        } catch (Exception e) {
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(R.fail("下载模板失败")));
        }
    }

    @ApiOperation(value = "导入")
    @PostMapping("import")
    @Log(businessType = BusinessType.IMPORT)
    public List<String> importDealer(@RequestParam(value = "file") MultipartFile file) {
        List<DealerNaRead> data;
        InputStream inputStream;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_DEALER_READ_EXCEL_ERROR);
        }
        data = EasyExcel.read(inputStream, new AnalysisEventListener<DealerNaRead>() {
            @Override
            public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                int count = 0;
                Field[] fields = DealerNaRead.class.getDeclaredFields();
                for (Field field : fields) {
                    ExcelProperty fieldAnnotation = field.getAnnotation(ExcelProperty.class);
                    if (fieldAnnotation != null) {
                        ++count;
                        String headName = headMap.get(fieldAnnotation.index());
                        if (StringUtils.isEmpty(headName) || !headName.equals(fieldAnnotation.value()[0])) {
                            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_DEALER_IMPORT_HEAD_ERROR);
                        }
                    }
                }
                // 判断用户导入表格的标题头是否完全符合模板
                if (count != headMap.size()) {
                    throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_DEALER_IMPORT_HEAD_ERROR);
                }
            }
            @Override
            public void invoke(DealerNaRead dealerNaRead, AnalysisContext analysisContext) {
            }
            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }
        }).head(DealerNaRead.class).sheet().doReadSync();

        if (CollectionUtils.isEmpty(data)) {
            throw ExceptionMessageUtil.getException(OPERATION_DEALER_READ_EXCEL_EMPTY);
        }
        if (data.size() > CommonConstant.DEALER_NA_IMPORT_THRESHOLD) {
            throw ExceptionMessageUtil.getException(OPERATION_DEALER_READ_EXCEL_UPPER_LIMIT,"20000");
        }
        return dealerNaService.importDealer(data);
    }

}
