package com.chervon.operation.controller;

import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.operation.domain.dto.CommonOperationProductDto;
import com.chervon.operation.domain.dto.CommonOperationProductPageDto;
import com.chervon.operation.domain.dto.faq.common.*;
import com.chervon.operation.domain.vo.faq.common.CommonFaqExcel;
import com.chervon.operation.domain.vo.faq.common.CommonFaqManagePageVo;
import com.chervon.operation.domain.vo.faq.common.CommonFaqVo;
import com.chervon.operation.service.CommonFaqService;
import com.chervon.technology.api.vo.CommonProductVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/25 11:22
 */
@Api(tags = "通用faq")
@RestController
@Slf4j
@RequestMapping("/common/faq")
public class CommonFaqController {

    @Autowired
    private CommonFaqService commonFaqService;

    @ApiOperation("内容管理-通用faq-分页查询")
    @PostMapping("manage/page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<CommonFaqManagePageVo> managePage(@RequestBody CommonFaqManagePageDto req) {
        return commonFaqService.managePage(req);
    }

    @ApiOperation("内容管理-通用faq-新增")
    @PostMapping("add")
    @Log(businessType = BusinessType.INSERT)
    public void add(@RequestBody CommonFaqDto req) {
        commonFaqService.add(req);
    }

    @ApiOperation("内容管理-通用faq-编辑")
    @PostMapping("edit")
    @Log(businessType = BusinessType.EDIT)
    public void edit(@RequestBody CommonFaqDto req) {
        commonFaqService.edit(req);
    }

    @ApiOperation("内容管理-通用faq-详情-传入commonFaqId")
    @PostMapping("detail")
    @Log(businessType = BusinessType.VIEW)
    public CommonFaqVo detail(@RequestBody SingleInfoReq<Long> req) {
        return commonFaqService.detail(req.getReq());
    }

    @ApiOperation("内容管理-通用faq-查看已关联产品")
    @PostMapping("product/page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<CommonProductVo> productPage(@RequestBody CommonOperationProductPageDto req) {
        return commonFaqService.productPage(req);
    }

    @ApiOperation("内容管理-通用faq-获取该faq所有已关联的产品id列表-传入commonFaqId")
    @PostMapping("productId/list")
    public List<Long> productIdList(@RequestBody SingleInfoReq<Long> req) {
        return commonFaqService.productIdList(req.getReq());
    }

    @ApiOperation("内容管理-通用faq-新增关联产品")
    @PostMapping("product/add")
    @Log(businessType = BusinessType.INSERT)
    public void productAdd(@RequestBody CommonOperationProductDto req) {
        commonFaqService.productAdd(req);
    }

    @ApiOperation("内容管理-通用faq-删除关联产品")
    @PostMapping("product/delete")
    @Log(businessType = BusinessType.DELETE)
    public void productDelete(@RequestBody CommonOperationProductDto req) {
        commonFaqService.productDelete(req);
    }

    @ApiOperation("内容管理-通用faq-删除-传入commonFaqId")
    @PostMapping("delete")
    @Log(businessType = BusinessType.DELETE)
    public void delete(@RequestBody SingleInfoReq<Long> req) {
        commonFaqService.delete(req.getReq());
    }

    @ApiOperation(value = "通用faq-模板下载")
    @PostMapping("template")
    @Log(businessType = BusinessType.DOWNLOAD)
    public void template(HttpServletResponse response) throws IOException {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("common_faq_template", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream(), CommonFaqRead.class).autoCloseStream(Boolean.FALSE).sheet("sheet1")
                    .doWrite(new ArrayList<CommonFaqRead>() {{
                        add(new CommonFaqRead());
                    }});
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(R.fail("下载模板失败")));
        }
    }

    @ApiOperation(value = "通用faq-导入")
    @PostMapping("import")
    @Log(businessType = BusinessType.IMPORT)
    public List<String> importHelpFaq(@RequestParam(value = "file") MultipartFile file) {
        return commonFaqService.importCommonFaq(file);
    }

    @ApiOperation(value = "导出")
    @PostMapping("export")
    @Log(businessType = BusinessType.EXPORT)
    public void export(@RequestBody CommonFaqManagePageDto req, HttpServletResponse response) throws IOException {
        try {
            List<CommonFaqExcel> data = commonFaqService.listData(req);
            if (CollectionUtils.isEmpty(data)) {
                data = new ArrayList<>();
                data.add(new CommonFaqExcel());
            }
            response.setContentType(ContentType.APPLICATION_OCTET_STREAM.toString());
            response.setCharacterEncoding("UTF-8");
            //进行下载
            String fileName = URLEncoder.encode("CommonFaq-" + new SimpleDateFormat("yyyyMMdd").format(new Date()), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            // 加上UTF-8文件的标识字符
            response.getWriter().write(new String(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF}));
            CsvWriter csvWriter = CsvUtil.getWriter(response.getWriter());
            csvWriter.writeBeans(data);
            csvWriter.close();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().println(JSON.toJSONString(R.fail("下载文件失败")));
        }
    }
}
