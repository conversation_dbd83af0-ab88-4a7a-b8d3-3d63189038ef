package com.chervon.operation.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.CsvUtil;
import com.chervon.fleet.web.api.entity.dto.FleetCompanyDevicePageDto;
import com.chervon.fleet.web.api.entity.vo.FleetCompanyDeviceVo;
import com.chervon.operation.api.vo.FleetCategoryListVo;
import com.chervon.operation.domain.dataobject.FleetCategory;
import com.chervon.operation.domain.vo.FleetCompanyDeviceExcel;
import com.chervon.operation.service.FleetCategoryService;
import com.chervon.operation.service.FleetCompanyDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-08-15 14:18
 **/
@Slf4j
@Api(tags = "fleet租户&设备关系管理")
@RestController
@AllArgsConstructor
@RequestMapping("/fleet/company/device")
public class FleetCompanyDeviceController {

    private final FleetCompanyDeviceService fleetCompanyDeviceService;

    private final FleetCategoryService fleetCategoryService;

    @ApiOperation("分页接口")
    @PostMapping("page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<FleetCompanyDeviceVo> page(@RequestBody FleetCompanyDevicePageDto req) {
        return fleetCompanyDeviceService.companyDevicePage(req);
    }

    @ApiOperation(value = "导出")
    @PostMapping("export")
    @Log(businessType = BusinessType.EXPORT)
    public void export(@RequestBody FleetCompanyDevicePageDto req, HttpServletResponse response) throws IOException {
        List<FleetCompanyDeviceExcel> data = fleetCompanyDeviceService.companyDeviceListData(req);
        CsvUtil.export(FleetCompanyDeviceExcel.class, data, "FleetCompanyDevice", response);
    }

    @ApiOperation("fleet品类接口")
    @PostMapping("category/list")
    public List<FleetCategoryListVo> categoryList() {
        List<FleetCategory> list = fleetCategoryService.list(new LambdaQueryWrapper<FleetCategory>().eq(FleetCategory::getLanguage, LocaleContextHolder.getLocale().getLanguage()));
        return list.stream().map(e -> {
            FleetCategoryListVo vo = new FleetCategoryListVo();
            vo.setCode(e.getCode());
            vo.setCategoryName(e.getCategoryName());
            return vo;
        }).collect(Collectors.toList());
    }
}
