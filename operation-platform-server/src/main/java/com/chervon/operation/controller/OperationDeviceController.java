package com.chervon.operation.controller;

import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import com.alibaba.fastjson.JSON;
import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.technology.api.RemoteOperationDeviceService;
import com.chervon.technology.api.dto.DeviceEditStatusDto;
import com.chervon.technology.api.dto.OperationComponentOtaPageDto;
import com.chervon.technology.api.dto.OperationComponentPageDto;
import com.chervon.technology.api.dto.OperationDevicePageDto;
import com.chervon.technology.api.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.http.entity.ContentType;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 运营平台-设备管理相关接口
 *
 * <AUTHOR>
 */
@Api(value = "设备管理", tags = "设备管理")
@RestController
@RequestMapping("/device/manage")
@Slf4j
public class OperationDeviceController {

    @DubboReference
    private RemoteOperationDeviceService remoteOperationDeviceService;

    @ApiOperation("设备分页")
    @PostMapping("list")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<OperationDevicePageVo> page(@RequestBody OperationDevicePageDto req) {
        return remoteOperationDeviceService.page(req);
    }

    @ApiOperation("导出设备表格")
    @PostMapping("/export")
    @Log(businessType = BusinessType.EXPORT)
    public void export(@RequestBody OperationDevicePageDto req, HttpServletResponse response) throws IOException {
        try {
            List<OperationDeviceExcel> data = remoteOperationDeviceService.list(req);
            if (CollectionUtils.isEmpty(data)) {
                data = new ArrayList<>();
                data.add(new OperationDeviceExcel());
            }
            response.setContentType(ContentType.APPLICATION_OCTET_STREAM.toString());
            response.setCharacterEncoding("UTF-8");
            //进行下载
            String fileName = URLEncoder.encode("Operation-Device" + new SimpleDateFormat("yyyyMMdd").format(new Date()), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + fileName + ".csv");
            // 加上UTF-8文件的标识字符
            response.getWriter().write(new String(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF}));
            CsvWriter csvWriter = CsvUtil.getWriter(response.getWriter());
            csvWriter.writeBeans(data);
            csvWriter.close();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().println(JSON.toJSONString(R.fail("下载文件失败")));
        }
    }

    @ApiOperation(value = "停用,启用设备")
    @PostMapping("/edit/status")
    @Log(businessType = BusinessType.EDIT)
    public void editStatus(@Validated @RequestBody DeviceEditStatusDto req) {
        remoteOperationDeviceService.editStatus(req.getDeviceId(), req.getStatus());
    }

    @ApiOperation(value = "获取设备详情")
    @PostMapping("/detail")
    @Log(businessType = BusinessType.VIEW)
    public OperationDeviceVo detail(@RequestBody SingleInfoReq<String> req) {
        return remoteOperationDeviceService.detail(req.getReq());
    }

    @ApiOperation(value = "通过设备Id获取总成零件列表")
    @PostMapping("/component/list")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<OperationComponentVo> componentList(@RequestBody OperationComponentPageDto req) {
        return remoteOperationDeviceService.componentPage(req);
    }

    @ApiOperation(value = "查看总成固件升级记录")
    @PostMapping("/component/ota/list")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<OperationComponentOtaVo> componentOtaList(@RequestBody OperationComponentOtaPageDto req) {
        return remoteOperationDeviceService.componentOtaList(req);
    }

}
