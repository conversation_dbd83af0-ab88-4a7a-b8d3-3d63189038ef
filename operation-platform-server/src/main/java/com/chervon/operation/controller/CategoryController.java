package com.chervon.operation.controller;

import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.operation.api.RemoteOperationCacheService;
import com.chervon.operation.api.vo.CategoryVo;
import com.chervon.operation.domain.dto.category.CategoryAddDto;
import com.chervon.operation.domain.dto.category.CategoryEditDto;
import com.chervon.operation.domain.dto.category.SearchCategoryDto;
import com.chervon.operation.service.AppListOrderService;
import com.chervon.operation.service.CategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2022-07-01
 */
@Api(tags = "品类相关")
@RestController
@RequestMapping("/category")
public class CategoryController {

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private RemoteOperationCacheService remoteOperationCacheService;

    /**
     * 创建品类
     *
     * @param categoryAdd 创建品类信息
     */
    @ApiOperation("创建品类信息")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Log(businessType = BusinessType.INSERT)
    public void addCategory(@Validated @RequestBody CategoryAddDto categoryAdd) throws UnsupportedEncodingException {
        if (categoryAdd != null && categoryAdd.getCategoryIcon() != null ) {
            categoryAdd.setCategoryIcon(URLDecoder.decode(categoryAdd.getCategoryIcon(), "UTF-8"));
        }
        categoryService.addCategory(categoryAdd);
    }

    /**
     * 更新品类
     *
     * @param categoryEdit 更新品类信息
     */
    @ApiOperation("更新品类")
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @Log(businessType = BusinessType.EDIT)
    public void editCategory(@Validated @RequestBody CategoryEditDto categoryEdit) throws UnsupportedEncodingException {
        if (categoryEdit != null && categoryEdit.getCategoryIcon() != null ) {
            categoryEdit.setCategoryIcon(URLDecoder.decode(categoryEdit.getCategoryIcon(), "UTF-8"));
        }
        categoryService.editCategory(categoryEdit);
        if (categoryEdit != null && categoryEdit.getId() != null) {
            remoteOperationCacheService.removeCategoryCache(categoryEdit.getId());
        }
    }

    /**
     * 根据id删除品类
     *
     * @param req 品类id
     */
    @ApiOperation("根据id删除品类")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @Log(businessType = BusinessType.DELETE)
    public void deleteCategory(@Validated @RequestBody SingleInfoReq<Long> req) {
        categoryService.deleteCategory(req.getReq());
        remoteOperationCacheService.removeCategoryCache(req.getReq());
    }

    /**
     * 获取品类详情
     *
     * @param req 品类id
     * @return 返回信息
     */
    @ApiOperation("获取品类详情")
    @RequestMapping(value = "/detail/get", method = RequestMethod.POST)
    @Log(businessType = BusinessType.VIEW)
    public CategoryVo getDetail(@Validated @RequestBody SingleInfoReq<Long> req) {
        return categoryService.getDetail(req.getReq());
    }

    /**
     * 分页获取品类信息
     *
     * @param searchName 搜索品类信息
     * @return 结果
     */
    @ApiOperation("分页获取品类信息")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @Log(businessType = BusinessType.VIEW)
    public PageResult<CategoryVo> list(@Validated @RequestBody SearchCategoryDto searchName) {
        return categoryService.list(searchName);
    }

    /**
     * 获取所有品类列表
     *
     * @return 结果
     */
    @ApiOperation("获取所有品类列表")
    @RequestMapping(value = "/all", method = RequestMethod.POST)
    public List<CategoryVo> allList() {
        return categoryService.allList();
    }

}
