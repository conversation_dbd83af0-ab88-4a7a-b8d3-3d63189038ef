package com.chervon.operation.controller;

import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.domain.SingleInfoResp;
import com.chervon.operation.config.AppAgreementOperationEnum;
import com.chervon.operation.domain.dto.app.AppAgreementDto;
import com.chervon.operation.domain.dto.app.AppAgreementManagePageDto;
import com.chervon.operation.domain.dto.app.AppAgreementOperationDto;
import com.chervon.operation.domain.dto.app.AppAgreementReleasePageDto;
import com.chervon.operation.domain.vo.app.AppAgreementManagePageVo;
import com.chervon.operation.domain.vo.app.AppAgreementReleasePageVo;
import com.chervon.operation.domain.vo.app.AppAgreementVo;
import com.chervon.operation.service.AppAgreementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/8/23 14:42
 */
@Api(tags = "app协议管理")
@RestController
@Slf4j
@RequestMapping("/app/agreement")
public class AppAgreementController {

    @Autowired
    private AppAgreementService appAgreementService;

    @ApiOperation("app协议管理分页查询")
    @PostMapping("manage/page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<AppAgreementManagePageVo> managePage(@RequestBody AppAgreementManagePageDto req) {
        return appAgreementService.managePage(req);
    }

    @ApiOperation("app协议发布管理分页查询")
    @PostMapping("release/page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<AppAgreementReleasePageVo> releasePage(@RequestBody AppAgreementReleasePageDto req) {
        return appAgreementService.releasePage(req);
    }

    @ApiOperation("新增")
    @PostMapping("save")
    @Log(businessType = BusinessType.INSERT)
    public void save(@RequestBody AppAgreementDto req) {
        appAgreementService.save(req);
    }

    @ApiOperation("编辑")
    @PostMapping("update")
    @Log(businessType = BusinessType.EDIT)
    public void update(@RequestBody AppAgreementDto req) {
        appAgreementService.update(req);
    }

    @ApiOperation("详情--传入appAgreementContentId")
    @PostMapping("detail")
    @Log(businessType = BusinessType.VIEW)
    public AppAgreementVo detail(@RequestBody SingleInfoReq<Long> req) {
        return appAgreementService.detail(req.getReq());
    }

    @ApiOperation("添加新版本")
    @PostMapping("addNewVersion")
    @Log(businessType = BusinessType.INSERT)
    public void addNewVersion(@RequestBody AppAgreementDto req) {
        appAgreementService.addNewVersion(req);
    }

    @ApiOperation("删除--传入appAgreementContentId")
    @PostMapping("delete")
    @Log(businessType = BusinessType.DELETE)
    public void delete(@RequestBody SingleInfoReq<Long> req) {
        AppAgreementOperationDto op = new AppAgreementOperationDto(req.getReq(), AppAgreementOperationEnum.DELETE.getName());
        appAgreementService.operation(op);
    }

    @ApiOperation("申请发布--传入appAgreementContentId")
    @PostMapping("applyRelease")
    @Log(businessType = BusinessType.GRANT)
    public void applyRelease(@RequestBody SingleInfoReq<Long> req) {
        AppAgreementOperationDto op = new AppAgreementOperationDto(req.getReq(), AppAgreementOperationEnum.APPLY_RELEASE.getName());
        appAgreementService.operation(op);
    }

    @ApiOperation("撤回发布申请--传入appAgreementContentId")
    @PostMapping("cancelApplyRelease")
    @Log(businessType = BusinessType.GRANT)
    public void cancelApplyRelease(@RequestBody SingleInfoReq<Long> req) {
        AppAgreementOperationDto op = new AppAgreementOperationDto(req.getReq(), AppAgreementOperationEnum.CANCEL_APPLY_RELEASE.getName());
        appAgreementService.operation(op);
    }

    @ApiOperation("申请停止发布--传入appAgreementContentId")
    @PostMapping("applyStopRelease")
    @Log(businessType = BusinessType.GRANT)
    public void applyStopRelease(@RequestBody SingleInfoReq<Long> req) {
        AppAgreementOperationDto op = new AppAgreementOperationDto(req.getReq(), AppAgreementOperationEnum.APPLY_STOP_RELEASE.getName());
        appAgreementService.operation(op);
    }

    @ApiOperation("撤回停止发布申请--传入appAgreementContentId")
    @PostMapping("cancelApplyStopRelease")
    @Log(businessType = BusinessType.GRANT)
    public void cancelApplyStopRelease(@RequestBody SingleInfoReq<Long> req) {
        AppAgreementOperationDto op = new AppAgreementOperationDto(req.getReq(), AppAgreementOperationEnum.CANCEL_APPLY_STOP_RELEASE.getName());
        appAgreementService.operation(op);
    }

    @ApiOperation("确认发布--传入appAgreementContentId")
    @PostMapping("ensureRelease")
    @Log(businessType = BusinessType.GRANT)
    public void ensureRelease(@RequestBody SingleInfoReq<Long> req) {
        AppAgreementOperationDto op = new AppAgreementOperationDto(req.getReq(), AppAgreementOperationEnum.ENSURE_RELEASE.getName());
        appAgreementService.operation(op);
    }

    @ApiOperation("发布驳回")
    @PostMapping("refuseRelease")
    @Log(businessType = BusinessType.GRANT)
    public void refuseRelease(@RequestBody AppAgreementOperationDto req) {
        req.setOperation(AppAgreementOperationEnum.REFUSE_RELEASE.getName());
        appAgreementService.operation(req);
    }

    @ApiOperation("确认停止发布--传入appAgreementContentId")
    @PostMapping("ensureStopRelease")
    @Log(businessType = BusinessType.GRANT)
    public void ensureStopRelease(@RequestBody SingleInfoReq<Long> req) {
        AppAgreementOperationDto op = new AppAgreementOperationDto(req.getReq(), AppAgreementOperationEnum.ENSURE_STOP_RELEASE.getName());
        appAgreementService.operation(op);
    }


    @ApiOperation("停止发布驳回")
    @PostMapping("refuseStopRelease")
    @Log(businessType = BusinessType.GRANT)
    public void refuseStopRelease(@RequestBody AppAgreementOperationDto req) {
        req.setOperation(AppAgreementOperationEnum.REFUSE_STOP_RELEASE.getName());
        appAgreementService.operation(req);
    }

    @ApiOperation("确认测试--传入appAgreementContentId")
    @PostMapping("ensureTest")
    @Log(businessType = BusinessType.GRANT)
    public void ensureTest(@RequestBody SingleInfoReq<Long> req) {
        AppAgreementOperationDto op = new AppAgreementOperationDto(req.getReq(), AppAgreementOperationEnum.ENSURE_TEST.getName());
        appAgreementService.operation(op);
    }


    @ApiOperation("测试驳回")
    @PostMapping("refuseTest")
    @Log(businessType = BusinessType.GRANT)
    public void refuseTest(@RequestBody AppAgreementOperationDto req) {
        req.setOperation(AppAgreementOperationEnum.REFUSE_TEST.getName());
        appAgreementService.operation(req);
    }

    @ApiOperation("查看发布被驳回原因--传入appAgreementContentId")
    @PostMapping("viewRefuseReleaseReason")
    @Log(businessType = BusinessType.VIEW)
    public SingleInfoResp<String> viewRefuseReleaseReason(@RequestBody SingleInfoReq<Long> req) {
        AppAgreementOperationDto op = new AppAgreementOperationDto(req.getReq(), AppAgreementOperationEnum.VIEW_REFUSE_RELEASE_REASON.getName());
        return new SingleInfoResp<>(appAgreementService.view(op));
    }

    @ApiOperation("查看停止发布被驳回原因--传入appAgreementContentId")
    @PostMapping("viewRefuseStopReleaseReason")
    @Log(businessType = BusinessType.VIEW)
    public SingleInfoResp<String> viewRefuseStopReleaseReason(@RequestBody SingleInfoReq<Long> req) {
        AppAgreementOperationDto op = new AppAgreementOperationDto(req.getReq(), AppAgreementOperationEnum.VIEW_REFUSE_STOP_RELEASE_REASON.getName());
        return new SingleInfoResp<>(appAgreementService.view(op));
    }

    @ApiOperation("查看测试被驳回原因--传入appAgreementContentId")
    @PostMapping("viewRefuseTestReason")
    @Log(businessType = BusinessType.VIEW)
    public SingleInfoResp<String> viewRefuseTestReason(@RequestBody SingleInfoReq<Long> req) {
        AppAgreementOperationDto op = new AppAgreementOperationDto(req.getReq(), AppAgreementOperationEnum.VIEW_REFUSE_TEST_REASON.getName());
        return new SingleInfoResp<>(appAgreementService.view(op));
    }

    @ApiOperation("复制协议--传入appAgreementContentId")
    @PostMapping("copy")
    @Log(businessType = BusinessType.OTHER)
    public void copy(@RequestBody SingleInfoReq<Long> req) {
        appAgreementService.copy(req.getReq());
    }
}
