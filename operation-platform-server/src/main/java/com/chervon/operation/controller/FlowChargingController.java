package com.chervon.operation.controller;

import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import com.alibaba.fastjson.JSON;
import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.technology.api.RemoteFlowChargingService;
import com.chervon.technology.api.core.BaseRemoteReqDto;
import com.chervon.technology.api.dto.charging.FlowChargingDetailPageDto;
import com.chervon.technology.api.dto.charging.FlowChargingPageDto;
import com.chervon.technology.api.vo.charging.FlowChargingDetailExcel;
import com.chervon.technology.api.vo.charging.FlowChargingDetailVo;
import com.chervon.technology.api.vo.charging.FlowChargingExcel;
import com.chervon.technology.api.vo.charging.FlowChargingVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.http.entity.ContentType;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/25 14:50
 */
@Api(tags = "流量计费管理")
@RestController
@Slf4j
@RequestMapping("/charging/flow")
public class FlowChargingController {

    @DubboReference
    private RemoteFlowChargingService remoteFlowChargingService;

    @ApiOperation(value = "流量计费分页查询")
    @PostMapping("page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<FlowChargingVo> page(@RequestBody FlowChargingPageDto req) {
        return remoteFlowChargingService.flowChargingPage(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(), req));
    }

    @ApiOperation(value = "流量计费导出")
    @PostMapping("export")
    @Log(businessType = BusinessType.EXPORT)
    public void export(@RequestBody FlowChargingPageDto req, HttpServletResponse response) throws IOException {
        try {
            List<FlowChargingExcel> data = remoteFlowChargingService.flowChargingList(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(), req));
            if (CollectionUtils.isEmpty(data)) {
                data = new ArrayList<>();
                data.add(new FlowChargingExcel());
            }
            response.setContentType(ContentType.APPLICATION_OCTET_STREAM.toString());
            response.setCharacterEncoding("UTF-8");
            //进行下载
            String fileName = URLEncoder.encode("FlowCharging-" + new SimpleDateFormat("yyyyMMdd").format(new Date()), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            // 加上UTF-8文件的标识字符
            response.getWriter().write(new String(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF}));
            CsvWriter csvWriter = CsvUtil.getWriter(response.getWriter());
            csvWriter.writeBeans(data);
            csvWriter.close();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().println(JSON.toJSONString(R.fail("下载文件失败")));
        }
    }

    @ApiOperation(value = "流量计费详情分页查询")
    @PostMapping("detail/page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<FlowChargingDetailVo> detailPage(@RequestBody FlowChargingDetailPageDto req) {
        return remoteFlowChargingService.flowChargingDetailPage(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(), req));
    }

    @ApiOperation(value = "流量计费详情导出")
    @PostMapping("detail/export")
    @Log(businessType = BusinessType.EXPORT)
    public void detailExport(@RequestBody FlowChargingDetailPageDto req, HttpServletResponse response) throws IOException {
        try {
            List<FlowChargingDetailExcel> data = remoteFlowChargingService.flowChargingDetailList(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(), req));
            if (CollectionUtils.isEmpty(data)) {
                data = new ArrayList<>();
                data.add(new FlowChargingDetailExcel());
            }
            response.setContentType(ContentType.APPLICATION_OCTET_STREAM.toString());
            response.setCharacterEncoding("UTF-8");
            //进行下载
            String fileName = URLEncoder.encode("FlowChargingDetail-" + new SimpleDateFormat("yyyyMMdd").format(new Date()), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            // 加上UTF-8文件的标识字符
            response.getWriter().write(new String(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF}));
            CsvWriter csvWriter = CsvUtil.getWriter(response.getWriter());
            csvWriter.writeBeans(data);
            csvWriter.close();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().println(JSON.toJSONString(R.fail("下载文件失败")));
        }
    }
}
