package com.chervon.operation.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.ListInfoReq;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.operation.api.exception.OperationErrorCode;
import com.chervon.operation.common.CommonConstant;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.domain.dto.DealerEuDto;
import com.chervon.operation.domain.dto.DealerEuPageDto;
import com.chervon.operation.domain.dto.DealerEuRead;
import com.chervon.operation.domain.vo.DealerEuPageVo;
import com.chervon.operation.domain.vo.DealerEuVo;
import com.chervon.operation.service.DealerEuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.chervon.operation.api.exception.OperationErrorCode.*;

/**
 * <AUTHOR>
 * @since 2022-08-15 14:18
 **/
@Slf4j
@Api(tags = "欧洲经销商管理")
@RestController
@AllArgsConstructor
@RequestMapping("/dealer/eu")
public class DealerEuController {

    private final DealerEuService dealerEuService;

    @ApiOperation("分页接口")
    @PostMapping("page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<DealerEuPageVo> page(@RequestBody DealerEuPageDto req) {
        return dealerEuService.page(req);
    }

    @ApiOperation("详情接口")
    @PostMapping("detail")
    @Log(businessType = BusinessType.VIEW)
    public DealerEuVo detail(@RequestBody SingleInfoReq<Long> req) {
        return dealerEuService.detail(req.getReq());
    }

    @ApiOperation("新增接口")
    @PostMapping("add")
    @Log(businessType = BusinessType.IMPORT)
    public void add(@RequestBody DealerEuDto req) {
        dealerEuService.add(req);
    }

    @ApiOperation("编辑接口")
    @PostMapping("edit")
    @Log(businessType = BusinessType.EDIT)
    public void edit(@RequestBody DealerEuDto req) {
        dealerEuService.edit(req);
    }

    @ApiOperation("删除接口")
    @PostMapping("delete")
    @Log(businessType = BusinessType.DELETE)
    public void delete(@RequestBody ListInfoReq<Long> req) {
        dealerEuService.delete(req.getInfo());
    }

    @ApiOperation(value = "模板下载")
    @PostMapping("template")
    @Log(businessType = BusinessType.DOWNLOAD)
    public void template(HttpServletResponse response) throws IOException {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("dealer_eu_template", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream(), DealerEuRead.class).autoCloseStream(Boolean.FALSE).sheet("sheet1")
                    .doWrite(new ArrayList<DealerEuRead>() {{
                        add(new DealerEuRead());
                    }});
        } catch (Exception e) {
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(R.fail("下载模板失败")));
        }
    }

    @ApiOperation(value = "导入")
    @PostMapping("import")
    @Log(businessType = BusinessType.IMPORT)
    public List<String> importDealer(@RequestParam(value = "file") MultipartFile file, @RequestParam(value = "countryCode") String countryCode) {
        if (StringUtils.isBlank(countryCode)) {
            throw ExceptionMessageUtil.getException(OPERATION_DEALER_EU_COUNTRY_BLANK);
        }
        List<DealerEuRead> data;
        InputStream inputStream;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_DEALER_READ_EXCEL_ERROR);
        }
        data = EasyExcel.read(inputStream, new AnalysisEventListener<DealerEuRead>() {
            @Override
            public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                int count = 0;
                Field[] fields = DealerEuRead.class.getDeclaredFields();
                for (Field field : fields) {
                    ExcelProperty fieldAnnotation = field.getAnnotation(ExcelProperty.class);
                    if (fieldAnnotation != null) {
                        ++count;
                        String headName = headMap.get(fieldAnnotation.index());
                        if (com.chervon.common.core.utils.StringUtils.isEmpty(headName) || !headName.equals(fieldAnnotation.value()[0])) {
                            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_DEALER_IMPORT_HEAD_ERROR);
                        }
                    }
                }
                // 判断用户导入表格的标题头是否完全符合模板
                if (count != headMap.size()) {
                    throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_DEALER_IMPORT_HEAD_ERROR);
                }
            }
            @Override
            public void invoke(DealerEuRead dealerEuRead, AnalysisContext analysisContext) {
            }
            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }
        }).head(DealerEuRead.class).sheet().doReadSync();

        if (CollectionUtils.isEmpty(data)) {
            throw ExceptionMessageUtil.getException(OPERATION_DEALER_READ_EXCEL_EMPTY);
        }
        if (data.size() > CommonConstant.DEALER_EU_IMPORT_THRESHOLD) {
            throw ExceptionMessageUtil.getException(OPERATION_DEALER_READ_EXCEL_UPPER_LIMIT,"5000");
        }
        return dealerEuService.importDealer(data, countryCode);
    }

}
