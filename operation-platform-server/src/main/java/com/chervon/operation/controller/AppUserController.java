package com.chervon.operation.controller;

import cn.hutool.core.text.csv.CsvUtil;
import com.alibaba.fastjson.JSON;
import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.operation.domain.vo.app.AppUserExcel;
import com.chervon.operation.service.AppUserService;
import com.chervon.usercenter.api.dto.AppUserPageDto;
import com.chervon.usercenter.api.vo.AppUserVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/20 14:21
 */
@Api(tags = "app用户账户管理")
@RestController
@Slf4j
@RequestMapping("/app/user")
public class AppUserController {

    @Autowired
    private AppUserService appUserService;

    @ApiOperation("用户账户列表分页查询")
    @PostMapping("page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<AppUserVo> page(@RequestBody AppUserPageDto req) {
        return appUserService.page(req);
    }

    @ApiOperation(value = "导出")
    @PostMapping("export")
    @Log(businessType = BusinessType.EXPORT)
    public void export(@RequestBody AppUserPageDto req, HttpServletResponse response) throws IOException {
        try {
            List<AppUserExcel> data = appUserService.listData(req);
            if (CollectionUtils.isEmpty(data)) {
                data = new ArrayList<>();
                data.add(new AppUserExcel());
            }
            response.setContentType("application/csv");
            response.setCharacterEncoding("utf-8");
            //进行下载
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("AppUser-" + new SimpleDateFormat("yyyyMMdd").format(new Date()), "UTF-8").replaceAll("\\+", "%20");
            //响应的是  .csv 文件的后缀
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".csv");
            // 这里需要设置不关闭流
            String filePath = fileName + ".csv";
            File file = new File(filePath);
            if (!file.exists()) {
                file.createNewFile();
            }
            //将数据，写入到 文件里面。 主要是这一行代码逻辑
            CsvUtil.getWriter(file, StandardCharsets.UTF_8).writeBeans(data).close();
            downloadFile(response, file);
            //将该文件删除
            file.delete();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(R.fail("下载文件失败")));
        }
    }

    public boolean downloadFile(HttpServletResponse response, File file) {
        FileInputStream fileInputStream = null;
        BufferedInputStream bufferedInputStream = null;
        OutputStream os = null;
        try {
            fileInputStream = new FileInputStream(file);
            bufferedInputStream = new BufferedInputStream(fileInputStream);
            os = response.getOutputStream();
            //MS产本头部需要插入BOM
            //如果不写入这几个字节，会导致用Excel打开时，中文显示乱码
            os.write(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF});
            byte[] buffer = new byte[1024];
            int i = bufferedInputStream.read(buffer);
            while (i != -1) {
                os.write(buffer, 0, i);
                i = bufferedInputStream.read(buffer);
            }
            return true;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        } finally {
            //关闭流
            if (os != null) {
                try {
                    os.flush();
                    os.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (bufferedInputStream != null) {
                try {
                    bufferedInputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            file.delete();
        }
        return false;
    }

}
