package com.chervon.operation.controller;

import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.domain.SingleInfoResp;
import com.chervon.operation.config.SysMsgOperationEnum;
import com.chervon.operation.domain.dto.app.*;
import com.chervon.operation.domain.vo.app.MsgPushResultVo;
import com.chervon.operation.domain.vo.app.SysMsgManagePageVo;
import com.chervon.operation.domain.vo.app.SysMsgReleasePageVo;
import com.chervon.operation.domain.vo.app.SysMsgVo;
import com.chervon.operation.service.MsgService;
import com.chervon.operation.service.SysMsgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/8/20 18:57
 */
@Api(tags = "app系统消息发布管理")
@RestController
@Slf4j
@RequestMapping("/app/sysMsg")
public class AppSysMsgController {

    @Autowired
    private SysMsgService sysMsgService;

    @Autowired
    private MsgService msgService;

    @ApiOperation("app系统消息管理分页查询")
    @PostMapping("manage/page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<SysMsgManagePageVo> managePage(@RequestBody SysMsgManagePageDto req) {
        return sysMsgService.managePage(req);
    }

    @ApiOperation("app系统消息发布管理分页查询")
    @PostMapping("release/page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<SysMsgReleasePageVo> releasePage(@RequestBody SysMsgReleasePageDto req) {
        return sysMsgService.releasePage(req);
    }

    @ApiOperation("新增")
    @PostMapping("save")
    @Log(businessType = BusinessType.INSERT)
    public void save(@RequestBody SysMsgDto req) {
        sysMsgService.save(req);
    }

    @ApiOperation("编辑")
    @PostMapping("update")
    @Log(businessType = BusinessType.EDIT)
    public void update(@RequestBody SysMsgDto req) {
        sysMsgService.update(req);
    }

    @ApiOperation("详情--传入sysMsgId")
    @PostMapping("detail")
    @Log(businessType = BusinessType.VIEW)
    public SysMsgVo detail(@RequestBody SingleInfoReq<Long> req) {
        return sysMsgService.detail(req.getReq());
    }

    @ApiOperation("复制")
    @PostMapping("copy")
    @Log(businessType = BusinessType.COPY)
    public void copy(@RequestBody SysMsgDto req) {
        sysMsgService.copy(req);
    }

    @ApiOperation("删除--传入sysMsgId")
    @PostMapping("delete")
    @Log(businessType = BusinessType.DELETE)
    public void delete(@RequestBody SingleInfoReq<Long> req) {
        sysMsgService.delete(req.getReq());
    }

    @ApiOperation("申请发布--传入sysMsgId")
    @PostMapping("applyRelease")
    @Log(businessType = BusinessType.GRANT)
    public void applyRelease(@RequestBody SingleInfoReq<Long> req) {
        SysMsgOperationDto op = new SysMsgOperationDto(req.getReq(), SysMsgOperationEnum.APPLY_RELEASE.getName());
        sysMsgService.operation(op);
    }

    @ApiOperation("撤回发布申请--传入sysMsgId")
    @PostMapping("cancelApplyRelease")
    @Log(businessType = BusinessType.GRANT)
    public void cancelApplyRelease(@RequestBody SingleInfoReq<Long> req) {
        SysMsgOperationDto op = new SysMsgOperationDto(req.getReq(), SysMsgOperationEnum.CANCEL_APPLY_RELEASE.getName());
        sysMsgService.operation(op);
    }

    @ApiOperation("申请停止发布--传入sysMsgId")
    @PostMapping("applyStopRelease")
    @Log(businessType = BusinessType.GRANT)
    public void applyStopRelease(@RequestBody SingleInfoReq<Long> req) {
        SysMsgOperationDto op = new SysMsgOperationDto(req.getReq(), SysMsgOperationEnum.APPLY_STOP_RELEASE.getName());
        sysMsgService.operation(op);
    }

    @ApiOperation("撤回停止发布申请--传入sysMsgId")
    @PostMapping("cancelApplyStopRelease")
    @Log(businessType = BusinessType.GRANT)
    public void cancelApplyStopRelease(@RequestBody SingleInfoReq<Long> req) {
        SysMsgOperationDto op = new SysMsgOperationDto(req.getReq(), SysMsgOperationEnum.CANCEL_APPLY_STOP_RELEASE.getName());
        sysMsgService.operation(op);
    }

    @ApiOperation("确认发布--传入sysMsgId")
    @PostMapping("ensureRelease")
    @Log(businessType = BusinessType.GRANT)
    public void ensureRelease(@RequestBody SingleInfoReq<Long> req) {
        SysMsgOperationDto op = new SysMsgOperationDto(req.getReq(), SysMsgOperationEnum.ENSURE_RELEASE.getName());
        sysMsgService.operation(op);
    }

    @ApiOperation("发布驳回d")
    @PostMapping("refuseRelease")
    @Log(businessType = BusinessType.GRANT)
    public void refuseRelease(@RequestBody SysMsgOperationDto req) {
        req.setOperation(SysMsgOperationEnum.REFUSE_RELEASE.getName());
        sysMsgService.operation(req);
    }

    @ApiOperation("确认停止发布--传入sysMsgId")
    @PostMapping("ensureStopRelease")
    @Log(businessType = BusinessType.GRANT)
    public void ensureStopRelease(@RequestBody SingleInfoReq<Long> req) {
        SysMsgOperationDto op = new SysMsgOperationDto(req.getReq(), SysMsgOperationEnum.ENSURE_STOP_RELEASE.getName());
        sysMsgService.operation(op);
    }


    @ApiOperation("停止发布驳回")
    @PostMapping("refuseStopRelease")
    @Log(businessType = BusinessType.GRANT)
    public void refuseStopRelease(@RequestBody SysMsgOperationDto req) {
        req.setOperation(SysMsgOperationEnum.REFUSE_STOP_RELEASE.getName());
        sysMsgService.operation(req);
    }

    @ApiOperation("确认测试--传入sysMsgId")
    @PostMapping("ensureTest")
    @Log(businessType = BusinessType.GRANT)
    public void ensureTest(@RequestBody SingleInfoReq<Long> req) {
        SysMsgOperationDto op = new SysMsgOperationDto(req.getReq(), SysMsgOperationEnum.ENSURE_TEST.getName());
        sysMsgService.operation(op);
    }


    @ApiOperation("测试驳回")
    @PostMapping("refuseTest")
    @Log(businessType = BusinessType.GRANT)
    public void refuseTest(@RequestBody SysMsgOperationDto req) {
        req.setOperation(SysMsgOperationEnum.REFUSE_TEST.getName());
        sysMsgService.operation(req);
    }

    @ApiOperation("查看发布被驳回原因--传入sysMsgId")
    @PostMapping("viewRefuseReleaseReason")
    @Log(businessType = BusinessType.VIEW)
    public SingleInfoResp<String> viewRefuseReleaseReason(@RequestBody SingleInfoReq<Long> req) {
        SysMsgOperationDto op = new SysMsgOperationDto(req.getReq(), SysMsgOperationEnum.VIEW_REFUSE_RELEASE_REASON.getName());
        return new SingleInfoResp<>(sysMsgService.view(op));
    }

    @ApiOperation("查看停止发布被驳回原因--传入sysMsgId")
    @PostMapping("viewRefuseStopReleaseReason")
    @Log(businessType = BusinessType.VIEW)
    public SingleInfoResp<String> viewRefuseStopReleaseReason(@RequestBody SingleInfoReq<Long> req) {
        SysMsgOperationDto op = new SysMsgOperationDto(req.getReq(), SysMsgOperationEnum.VIEW_REFUSE_STOP_RELEASE_REASON.getName());
        return new SingleInfoResp<>(sysMsgService.view(op));
    }

    @ApiOperation("查看测试被驳回原因--传入sysMsgId")
    @PostMapping("viewRefuseTestReason")
    @Log(businessType = BusinessType.VIEW)
    public SingleInfoResp<String> viewRefuseTestReason(@RequestBody SingleInfoReq<Long> req) {
        SysMsgOperationDto op = new SysMsgOperationDto(req.getReq(), SysMsgOperationEnum.VIEW_REFUSE_TEST_REASON.getName());
        return new SingleInfoResp<>(sysMsgService.view(op));
    }

    @ApiOperation("推送结果")
    @PostMapping("pushResult")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<MsgPushResultVo> pushResult(@RequestBody MsgPushResultPageDto req) {
        return msgService.pushResult(req, CommonConstant.ZERO);
    }

}
