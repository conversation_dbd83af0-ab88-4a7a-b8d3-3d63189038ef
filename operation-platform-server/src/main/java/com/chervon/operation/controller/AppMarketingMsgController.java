package com.chervon.operation.controller;

import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.domain.SingleInfoResp;
import com.chervon.operation.domain.dto.app.*;
import com.chervon.operation.domain.vo.app.MarketingMsgManagePageVo;
import com.chervon.operation.domain.vo.app.MarketingMsgReleasePageVo;
import com.chervon.operation.domain.vo.app.MarketingMsgVo;
import com.chervon.operation.domain.vo.app.MsgPushResultVo;
import com.chervon.operation.service.MarketingMsgService;
import com.chervon.operation.service.MsgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/8/20 18:57
 */
@Api(tags = "app营销消息发布管理")
@RestController
@Slf4j
@RequestMapping("/app/marketingMsg")
public class AppMarketingMsgController {

    @Autowired
    private MarketingMsgService marketingMsgService;

    @Autowired
    private MsgService msgService;

    @ApiOperation("app营销消息管理分页查询")
    @PostMapping("manage/page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<MarketingMsgManagePageVo> managePage(@RequestBody MarketingMsgManagePageDto req) {
        return marketingMsgService.managePage(req);
    }

    @ApiOperation("app营销消息发布管理分页查询")
    @PostMapping("release/page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<MarketingMsgReleasePageVo> releasePage(@RequestBody MarketingMsgReleasePageDto req) {
        return marketingMsgService.releasePage(req);
    }

    @ApiOperation("新增")
    @PostMapping("save")
    @Log(businessType = BusinessType.INSERT)
    public void save(@RequestBody MarketingMsgDto req) {
        marketingMsgService.save(req);
    }

    @ApiOperation("编辑")
    @PostMapping("update")
    @Log(businessType = BusinessType.EDIT)
    public void update(@RequestBody MarketingMsgDto req) {
        marketingMsgService.update(req);
    }

    @ApiOperation("详情--传入marketingMsgId")
    @PostMapping("detail")
    @Log(businessType = BusinessType.VIEW)
    public MarketingMsgVo detail(@RequestBody SingleInfoReq<Long> req) {
        return marketingMsgService.detail(req.getReq());
    }

    @ApiOperation("复制")
    @PostMapping("copy")
    @Log(businessType = BusinessType.OTHER)
    public void copy(@RequestBody MarketingMsgDto req) {
        marketingMsgService.copy(req);
    }

    @ApiOperation("删除--传入marketingMsgId")
    @PostMapping("delete")
    @Log(businessType = BusinessType.DELETE)
    public void delete(@RequestBody SingleInfoReq<Long> req) {
        marketingMsgService.delete(req.getReq());
    }

    @ApiOperation("申请发布--传入marketingMsgId")
    @PostMapping("applyRelease")
    @Log(businessType = BusinessType.GRANT)
    public void applyRelease(@RequestBody SingleInfoReq<Long> req) {
        MarketingMsgOperationDto op = new MarketingMsgOperationDto(req.getReq(), "apply_release");
        marketingMsgService.operation(op);
    }

    @ApiOperation("撤回发布申请--传入marketingMsgId")
    @PostMapping("cancelApplyRelease")
    @Log(businessType = BusinessType.GRANT)
    public void cancelApplyRelease(@RequestBody SingleInfoReq<Long> req) {
        MarketingMsgOperationDto op = new MarketingMsgOperationDto(req.getReq(), "cancel_apply_release");
        marketingMsgService.operation(op);
    }

    @ApiOperation("申请停止发布--传入marketingMsgId")
    @PostMapping("applyStopRelease")
    @Log(businessType = BusinessType.GRANT)
    public void applyStopRelease(@RequestBody SingleInfoReq<Long> req) {
        MarketingMsgOperationDto op = new MarketingMsgOperationDto(req.getReq(), "apply_stop_release");
        marketingMsgService.operation(op);
    }

    @ApiOperation("撤回停止发布申请--传入marketingMsgId")
    @PostMapping("cancelApplyStopRelease")
    @Log(businessType = BusinessType.GRANT)
    public void cancelApplyStopRelease(@RequestBody SingleInfoReq<Long> req) {
        MarketingMsgOperationDto op = new MarketingMsgOperationDto(req.getReq(), "cancel_apply_stop_release");
        marketingMsgService.operation(op);
    }

    @ApiOperation("确认发布--传入marketingMsgId")
    @PostMapping("ensureRelease")
    @Log(businessType = BusinessType.GRANT)
    public void ensureRelease(@RequestBody SingleInfoReq<Long> req) {
        MarketingMsgOperationDto op = new MarketingMsgOperationDto(req.getReq(), "ensure_release");
        marketingMsgService.operation(op);
    }

    @ApiOperation("发布驳回d")
    @PostMapping("refuseRelease")
    @Log(businessType = BusinessType.GRANT)
    public void refuseRelease(@RequestBody MarketingMsgOperationDto req) {
        req.setOperation("refuse_release");
        marketingMsgService.operation(req);
    }

    @ApiOperation("确认停止发布--传入marketingMsgId")
    @PostMapping("ensureStopRelease")
    @Log(businessType = BusinessType.GRANT)
    public void ensureStopRelease(@RequestBody SingleInfoReq<Long> req) {
        MarketingMsgOperationDto op = new MarketingMsgOperationDto(req.getReq(), "ensure_stop_release");
        marketingMsgService.operation(op);
    }


    @ApiOperation("停止发布驳回")
    @PostMapping("refuseStopRelease")
    @Log(businessType = BusinessType.GRANT)
    public void refuseStopRelease(@RequestBody MarketingMsgOperationDto req) {
        req.setOperation("refuse_stop_release");
        marketingMsgService.operation(req);
    }

    @ApiOperation("确认测试--传入marketingMsgId")
    @PostMapping("ensureTest")
    @Log(businessType = BusinessType.GRANT)
    public void ensureTest(@RequestBody SingleInfoReq<Long> req) {
        MarketingMsgOperationDto op = new MarketingMsgOperationDto(req.getReq(), "ensure_test");
        marketingMsgService.operation(op);
    }


    @ApiOperation("测试驳回")
    @PostMapping("refuseTest")
    @Log(businessType = BusinessType.GRANT)
    public void refuseTest(@RequestBody MarketingMsgOperationDto req) {
        req.setOperation("refuse_test");
        marketingMsgService.operation(req);
    }

    @ApiOperation("查看发布被驳回原因--传入marketingMsgId")
    @PostMapping("viewRefuseReleaseReason")
    @Log(businessType = BusinessType.VIEW)
    public SingleInfoResp<String> viewRefuseReleaseReason(@RequestBody SingleInfoReq<Long> req) {
        MarketingMsgOperationDto op = new MarketingMsgOperationDto(req.getReq(), "view_refuse_release_reason");
        return new SingleInfoResp<>(marketingMsgService.view(op));
    }

    @ApiOperation("查看停止发布被驳回原因--传入marketingMsgId")
    @PostMapping("viewRefuseStopReleaseReason")
    @Log(businessType = BusinessType.VIEW)
    public SingleInfoResp<String> viewRefuseStopReleaseReason(@RequestBody SingleInfoReq<Long> req) {
        MarketingMsgOperationDto op = new MarketingMsgOperationDto(req.getReq(), "view_refuse_stop_release_reason");
        return new SingleInfoResp<>(marketingMsgService.view(op));
    }

    @ApiOperation("查看测试被驳回原因--传入marketingMsgId")
    @PostMapping("viewRefuseTestReason")
    @Log(businessType = BusinessType.VIEW)
    public SingleInfoResp<String> viewRefuseTestReason(@RequestBody SingleInfoReq<Long> req) {
        MarketingMsgOperationDto op = new MarketingMsgOperationDto(req.getReq(), "view_refuse_test_reason");
        return new SingleInfoResp<>(marketingMsgService.view(op));
    }

    @ApiOperation("推送结果")
    @PostMapping("pushResult")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<MsgPushResultVo> pushResult(@RequestBody MsgPushResultPageDto req) {
        return msgService.pushResult(req, CommonConstant.ONE);
    }

}
