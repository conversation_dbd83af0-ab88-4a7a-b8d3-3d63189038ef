package com.chervon.operation.rpc;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.domain.MultiLanguageVo;
import com.chervon.common.core.utils.JsonUtils;
import com.chervon.common.web.entity.I18nErrorDto;
import com.chervon.operation.api.RemoteIntroductionService;
import com.chervon.operation.api.dto.IntroductionTypeDto;
import com.chervon.operation.api.vo.IntroductionRobotVo;
import com.chervon.operation.api.vo.IntroductionVo;
import com.chervon.operation.domain.dataobject.Introduction;
import com.chervon.operation.service.IntroductionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品配网引导页服务
 * <AUTHOR>
 * @since 2022-09-02 10:19
 **/
@DubboService
@Service
@Slf4j
public class RemoteIntroductionServiceImpl implements RemoteIntroductionService {

    private final IntroductionService introductionService;

    public RemoteIntroductionServiceImpl(IntroductionService introductionService) {
        this.introductionService = introductionService;
    }

    @Override
    public List<IntroductionVo> listIntroductionByType(IntroductionTypeDto req) {
        String lang = LocaleContextHolder.getLocale().getLanguage();
        if (req == null || req.getProductId() == null || req.getType() == null || !Arrays.asList(0, 1, 2).contains(req.getType())) {
            return new ArrayList<>();
        }
        List<Introduction> list = introductionService.list(new LambdaQueryWrapper<Introduction>()
                .eq(Introduction::getProductId, req.getProductId())
                .eq(Introduction::getType, req.getType())
                .orderByAsc(Introduction::getCreateTime)
        );
        String finalLang = lang;
        return list.stream().map(e -> {
            IntroductionVo vo = new IntroductionVo();
            BeanUtils.copyProperties(e, vo);
            vo.setIntroductionId(e.getId());
            vo.setContent(new MultiLanguageVo(e.getContentLangId(),
                    com.chervon.operation.config.MultiLanguageUtil.getByLangCode(e.getContentLangCode(), finalLang)));
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<IntroductionRobotVo> getIntroductionConfig(IntroductionTypeDto req) {
        if (req == null || req.getProductId() == null || req.getType() == null) {
            return Collections.EMPTY_LIST;
        }
        List<Introduction> list = introductionService.list(new LambdaQueryWrapper<Introduction>()
                .eq(Introduction::getProductId, req.getProductId())
                .eq(Introduction::getType, req.getType())
                .orderByAsc(Introduction::getCreateTime)
        );
        if(CollectionUtils.isEmpty(list)){
            return Collections.EMPTY_LIST;
        }
        List<IntroductionRobotVo> listIntroductions=new ArrayList<>();
        for (Introduction introduction : list) {
            listIntroductions.addAll(ResolverContentValue(introduction.getContent()));
        }
        return listIntroductions;
    }

    public static List<IntroductionRobotVo> ResolverContentValue(String jsonInput) {
        if(!StringUtils.hasText(jsonInput)){
            return Collections.emptyList();
        }
        String lang = LocaleContextHolder.getLocale().getLanguage();
        List<IntroductionRobotVo> list = JSONUtil.toBean(jsonInput, new TypeReference<List<IntroductionRobotVo>>() {},true);
        if(CollectionUtils.isEmpty(list)){
            return Collections.emptyList();
        }
        for (IntroductionRobotVo introductionRobotVo : list) {
            final Map mapButtonText = JSONUtil.toBean(introductionRobotVo.getButtonText(), Map.class);
            String buttonText = (String)mapButtonText.getOrDefault(lang, "No specified language configured");
            introductionRobotVo.setButtonText(buttonText);
            for (IntroductionRobotVo.IntroductionContent content : introductionRobotVo.getContent()) {
                final Map<String,String> contentLang = JSONUtil.toBean(content.getMessage(), Map.class);
                String contentText = contentLang.getOrDefault(lang, "No specified language configured");
                content.setMessage(contentText);
            }
        }
        return list;
    }

}
