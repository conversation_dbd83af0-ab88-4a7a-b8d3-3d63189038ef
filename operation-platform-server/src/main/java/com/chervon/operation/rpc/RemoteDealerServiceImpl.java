package com.chervon.operation.rpc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.domain.PageResult;
import com.chervon.operation.api.RemoteDealerService;
import com.chervon.operation.api.vo.AppDealerVo;
import com.chervon.operation.config.OperationCommon;
import com.chervon.operation.domain.dataobject.DealerNa;
import com.chervon.operation.service.DealerNaService;
import com.chervon.operation.util.PointUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/19 18:03
 */
@DubboService
@Service
@Slf4j
@AllArgsConstructor
public class RemoteDealerServiceImpl implements RemoteDealerService {

    private final DealerNaService dealerNaService;

    @Override
    public List<AppDealerVo> list(String category, double lat, double lng, String lang) {
        Map<String, Double> point = PointUtil.find4Point(lat, lng, 300);
        List<AppDealerVo> list = dealerNaService.list(category, lat, lng,
                point.get("minLat"), point.get("maxLat"), point.get("minLng"), point.get("maxLng"), true);
        list.forEach(e -> {
            e.setCategory(Arrays.asList(Optional.ofNullable(e.getCa()).orElse("").split(",")));
        });
        return list;
    }

    @Override
    public List<AppDealerVo> list(String category, double lat, double lng, double distance) {
        return dealerNaService.list(category, lat, lng, distance);
    }

    @Override
    public List<AppDealerVo> listByIds(List<Long> dealerIds, String lang) {
        List<DealerNa> list = dealerNaService.list(new LambdaQueryWrapper<DealerNa>().in(DealerNa::getId, dealerIds)
                .last("order by field(id, " + dealerIds.stream().map(String::valueOf).collect(Collectors.joining(",")) + ")"));
          return list.stream().map(e -> {
            AppDealerVo vo = new AppDealerVo();
            BeanUtils.copyProperties(e, vo);
            vo.setDealerId(String.valueOf(e.getId()));
            vo.setCategory(Arrays.asList(e.getCategory().split(",")));
            return vo;
        }).collect(Collectors.toList());
    }
}
