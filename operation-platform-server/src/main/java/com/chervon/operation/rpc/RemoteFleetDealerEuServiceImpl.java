package com.chervon.operation.rpc;

import com.chervon.operation.api.RemoteFleetDealerEuService;
import com.chervon.operation.api.dto.AppDealerSearchDto;
import com.chervon.operation.api.vo.AppDealerEuVo;
import com.chervon.operation.domain.bo.DictBo;
import com.chervon.operation.domain.bo.DictNodeBo;
import com.chervon.operation.domain.dataobject.DealerEu;
import com.chervon.operation.service.DealerEuService;
import com.chervon.operation.service.DictService;
import com.chervon.operation.util.PointUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/1 11:29
 */
@Service
@Slf4j
@DubboService
@AllArgsConstructor
public class RemoteFleetDealerEuServiceImpl implements RemoteFleetDealerEuService {

    private final DealerEuService dealerEuService;

    @Autowired
    private DictService dictService;

    @Override
    public List<AppDealerEuVo> search(String lang, AppDealerSearchDto req) {
        Map<String, Double> point = PointUtil.find4Point(req.getLat(), req.getLng(), 30);
        List<AppDealerEuVo> list = dealerEuService.list(req.getLat(), req.getLng(),
                point.get("minLat"), point.get("maxLat"), point.get("minLng"), point.get("maxLng"));
        handleList(list, lang, req.getContent());
        return list;
    }

    @Override
    public AppDealerEuVo detail(String lang, Long dealerId) {
        DealerEu dealerEu = dealerEuService.getById(dealerId);
        AppDealerEuVo res = new AppDealerEuVo();
        if (dealerEu == null) {
            return res;
        }
        Map<String, String> collect = handleCountry(lang);
        BeanUtils.copyProperties(dealerEu, res);
        res.setDealerId(dealerEu.getId());
        res.setCountry(collect.get(dealerEu.getCountryCode()));
        return res;
    }

    @Override
    public Integer countByDealerIds(List<Long> dealerIds) {
        if (CollectionUtils.isEmpty(dealerIds)) {
            return 0;
        }
        return dealerEuService.countByIds(dealerIds);
    }

    @Override
    public List<AppDealerEuVo> listByDealerIds(String lang, List<Long> dealerIds, Double lat, Double lng, String searchContent) {
        if (CollectionUtils.isEmpty(dealerIds)) {
            return new ArrayList<>();
        }
        List<AppDealerEuVo> list = dealerEuService.listWithDistanceByIds(lat, lng, dealerIds);
        handleList(list, lang, searchContent);
        return list;
    }

    private Map<String, String> handleCountry(String lang) {
        List<DictBo> dictList = dictService.listByDictName(lang, Collections.singletonList(DealerEuService.EU_COUNTRY_CODE));
        List<DictNodeBo> nodes = dictList.stream().collect(Collectors.toMap(DictBo::getDictName, Function.identity())).getOrDefault(DealerEuService.EU_COUNTRY_CODE, new DictBo()).getNodes();
        if (nodes == null) {
            nodes = new ArrayList<>();
        }
        return nodes.stream().collect(Collectors.toMap(DictNodeBo::getLabel, DictNodeBo::getDescription, (k1, k2) -> k2));
    }

    private void handleList(List<AppDealerEuVo> list, String lang, String searchContent) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<String, String> collect = handleCountry(lang);
        list.forEach(e -> e.setCountry(collect.get(e.getCountry())));
        if (StringUtils.isNotBlank(searchContent)) {
            list.removeIf(e -> {
                boolean title = StringUtils.containsIgnoreCase(e.getTitle(), searchContent);
                boolean addressOne = StringUtils.containsIgnoreCase(e.getAddressOne(), searchContent);
                boolean addressTwo = StringUtils.containsIgnoreCase(e.getAddressTwo(), searchContent);
                boolean country = StringUtils.containsIgnoreCase(e.getCountry(), searchContent);
                boolean region = StringUtils.containsIgnoreCase(e.getRegion(), searchContent);
                boolean city = StringUtils.containsIgnoreCase(e.getCity(), searchContent);
                boolean town = StringUtils.containsIgnoreCase(e.getTown(), searchContent);
                boolean postCode = StringUtils.containsIgnoreCase(e.getPostcode(), searchContent);
                return !(title || addressOne || addressTwo || country || region || city || town || postCode);
            });
        }
    }
}
