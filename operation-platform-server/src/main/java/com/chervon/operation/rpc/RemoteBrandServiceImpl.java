package com.chervon.operation.rpc;

import com.chervon.common.core.domain.BaseRemoteReqDto;
import com.chervon.operation.api.RemoteBrandService;
import com.chervon.operation.api.vo.BrandVo;
import com.chervon.operation.service.BrandService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022-08-22 09:41
 **/
@Slf4j
@DubboService
public class RemoteBrandServiceImpl implements RemoteBrandService {

    @Autowired
    private BrandService brandService;

    @Override
    public Map<Long, BrandVo> getMyMap(BaseRemoteReqDto<List<Long>> brandIds) {
        LocaleContextHolder.setLocale(new Locale(brandIds.getLanguage()));
        return brandService.getMapByIds(brandIds.getReq());
    }

    @Override
    public BrandVo getDetail(BaseRemoteReqDto<Long> id) {
        LocaleContextHolder.setLocale(new Locale(id.getLanguage()));
        return brandService.getDetail(id.getReq());
    }

    @Override
    public List<BrandVo> allList() {
        return brandService.allList();
    }
}