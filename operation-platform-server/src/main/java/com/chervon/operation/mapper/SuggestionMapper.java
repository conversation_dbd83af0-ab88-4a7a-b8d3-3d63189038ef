package com.chervon.operation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chervon.operation.domain.dataobject.Suggestion;
import com.chervon.operation.domain.dto.suggestion.SuggestionPageDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/30 14:17
 */
@Mapper
public interface SuggestionMapper extends BaseMapper<Suggestion> {

    /**
     * 分页查询
     *
     * @param page         容器
     * @param search       查询条件
     * @param titleLangIds 名称多语言id集合
     * @return 分页数据
     */
    IPage<Suggestion> selectSuggestionPage(IPage<Suggestion> page,
                                           @Param("search") SuggestionPageDto search,
                                           @Param("titleLangIds") List<Long> titleLangIds);

    /**
     * 列表查询
     *
     * @param search       查询条件
     * @param titleLangIds 名称多语言id集合
     * @return 列表数据
     */
    List<Suggestion> selectSuggestionList(@Param("search") SuggestionPageDto search,
                                          @Param("titleLangIds") List<Long> titleLangIds);
}
