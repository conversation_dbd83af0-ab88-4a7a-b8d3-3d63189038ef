package com.chervon.operation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.operation.api.vo.AppDealerEuVo;
import com.chervon.operation.domain.dataobject.DealerEu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-08-15 14:50
 **/
@Mapper
public interface DealerEuMapper extends BaseMapper<DealerEu> {

    /**
     * 列表查询
     *
     * @param lat    经度
     * @param lng    纬度
     * @param minLat 最小经度
     * @param maxLat 最大经度
     * @param minLng 最小纬度
     * @param maxLng 最大纬度
     * @return 列表数据
     */
    List<AppDealerEuVo> list(@Param("lat") double lat,
                             @Param("lng") double lng,
                             @Param("minLat") double minLat,
                             @Param("maxLat") double maxLat,
                             @Param("minLng") double minLng,
                             @Param("maxLng") double maxLng);

    Integer countByIds(@Param("dealerIds") List<Long> dealerIds);
    /**
     * 获取经销商信息和距离
     *
     * @param lat       纬度
     * @param lng       经度
     * @param dealerIds 经销商id集合
     * @return 数据集合
     */
    List<AppDealerEuVo> listWithDistanceByIds(@Param("lat") Double lat,
                                              @Param("lng") Double lng,
                                              @Param("dealerIds") List<Long> dealerIds);

}
