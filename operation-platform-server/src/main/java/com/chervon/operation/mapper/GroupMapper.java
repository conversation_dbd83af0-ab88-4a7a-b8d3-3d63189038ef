package com.chervon.operation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.common.core.domain.PageResult;
import com.chervon.operation.domain.dataobject.Groups;
import com.chervon.operation.domain.dto.group.ListGroupDto;
import com.chervon.operation.domain.dto.group.PageGroupDto;
import com.chervon.operation.domain.vo.group.GroupUserVo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 设备表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
public interface GroupMapper extends BaseMapper<Groups> {

    /**
     * 分页获取设备分组
     * <AUTHOR>
     * @date 17:52 2022/7/28
     * @param page:
     * @param pageGroupDto:
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.chervon.technology.domain.entity.DeviceGroup>
     **/
    Page<Groups> getPage(Page<Groups> page,
        @Param("pageGroupDto") PageGroupDto pageGroupDto);

    /**
     * 获取设备分组列表
     * <AUTHOR>
     * @date 10:01 2022/8/31
     * @param listGroupDto:
     * @return java.util.List<com.chervon.operation.domain.dataobject.Groups>
     **/
    List<Groups> listGroup(@Param("listGroupDto")ListGroupDto listGroupDto);

    /**
     * 查询用户
     * <AUTHOR>
     * @date 10:40 2022/9/1
     * @param page:
     * @param queryUserString:
     * @return com.chervon.common.core.domain.PageResult<com.chervon.operation.domain.vo.group.GroupUserVo>
     **/
    Page<GroupUserVo> queryUsers(@Param("page") Page<GroupUserVo> page,
        @Param("queryUserString") String queryUserString);

    /**
     * 判断用户分组条件是否命中
     * <AUTHOR>
     * @date 13:35 2022/9/1
     * @param queryUserString:
     * @return boolean
     **/
    boolean hasGroup(String queryUserString);

    /**
     * 分页获取分组下的用户id列表
     * <AUTHOR>
     * @date 13:44 2022/9/1
     * @param page:
     * @param queryUserString:
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<java.lang.Long>
     **/
    Page<Long> pageUserIds(@Param("page")Page<Long> page,
        @Param("queryUserString") String queryUserString);

    /**
     * 获取分组下的用户id列表
     * <AUTHOR>
     * @date 13:44 2022/9/1
     * @param queryUserString:
     * @return java.util.List<java.lang.Long>
     **/
    List<Long> listUserId(@Param("queryUserString") String queryUserString);

}
