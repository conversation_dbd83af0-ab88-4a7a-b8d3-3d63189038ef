package com.chervon.operation.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * @Author：flynn.wang
 * @Date：2024/4/7 11:01
 */
@Slf4j
public class NetworkFileUtil {


    public static long downloadFile(String networkFileUrl,File destFile){
        return HttpUtil.downloadFile(networkFileUrl,destFile);
    }



    /**
     * 获取网络文件文件名
     * @param networkFileUrl
     * @return
     */
    public static String getFileName(String networkFileUrl){
        return FileUtil.getName(URLUtil.getPath(networkFileUrl));

    }

    /**
     * 检查网络文件是否存在
     *
     * @param networkFileUrl
     * @return
     */
    public static boolean checkFileExist(String networkFileUrl) {
        HttpURLConnection connection = null;
        try {

            URL url  = new URL(networkFileUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(3000);
            connection.setReadTimeout(3000);
            connection.setRequestMethod("HEAD");
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                return true;
            }
        } catch (IOException e) {
            log.error("error", e);
        } finally {
            connection.disconnect();
        }
        return false;
    }
}
