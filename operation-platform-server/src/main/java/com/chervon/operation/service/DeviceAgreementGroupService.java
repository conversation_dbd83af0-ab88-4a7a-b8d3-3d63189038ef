package com.chervon.operation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.operation.domain.dataobject.DeviceAgreementGroup;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-08-25 19:05
 **/
public interface DeviceAgreementGroupService extends IService<DeviceAgreementGroup> {
    /**
     * 通过分组名称列表保存判断条件
     * @param agreementId 协议主键id(不是协议Id)
     * @param groupNameList 分组名称列表
     */
    void saveByGroupNameList(Long agreementId, List<String> groupNameList);
}
