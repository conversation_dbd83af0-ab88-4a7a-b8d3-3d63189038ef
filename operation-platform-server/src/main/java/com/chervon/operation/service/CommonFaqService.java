package com.chervon.operation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.operation.domain.dataobject.CommonFaq;
import com.chervon.operation.domain.dto.CommonOperationProductDto;
import com.chervon.operation.domain.dto.CommonOperationProductPageDto;
import com.chervon.operation.domain.dto.faq.common.CommonFaqDto;
import com.chervon.operation.domain.dto.faq.common.CommonFaqManagePageDto;
import com.chervon.operation.domain.dto.faq.common.CommonFaqOperationDto;
import com.chervon.operation.domain.dto.faq.common.CommonFaqReleasePageDto;
import com.chervon.operation.domain.vo.faq.common.CommonFaqExcel;
import com.chervon.operation.domain.vo.faq.common.CommonFaqManagePageVo;
import com.chervon.operation.domain.vo.faq.common.CommonFaqReleasePageVo;
import com.chervon.operation.domain.vo.faq.common.CommonFaqVo;
import com.chervon.technology.api.vo.CommonProductVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/25 11:20
 */
public interface CommonFaqService extends IService<CommonFaq> {

    /**
     * 内容管理-通用faq-分页查询
     *
     * @param req 查询条件
     * @return 分页数据
     */
    PageResult<CommonFaqManagePageVo> managePage(CommonFaqManagePageDto req);


    /**
     * 内容管理-通用faq-新增
     *
     * @param req 新增对象
     */
    void add(CommonFaqDto req);

    /**
     * 内容管理-通用faq-编辑
     *
     * @param req 编辑对象
     */
    void edit(CommonFaqDto req);

    /**
     * 内容管理-通用faq-详情
     *
     * @param commonFaqId 通用faqid
     * @return 通用faq
     */
    CommonFaqVo detail(Long commonFaqId);

    /**
     * 内容管理-通用faq-查看已关联产品
     *
     * @param req 查询条件
     * @return 已关联产品
     */
    PageResult<CommonProductVo> productPage(CommonOperationProductPageDto req);

    /**
     * 内容管理-通用faq-新增关联产品
     *
     * @param req 操作对象
     */
    void productAdd(CommonOperationProductDto req);

    /**
     * 内容管理-通用faq-删除关联产品
     *
     * @param req 操作对象
     */
    void productDelete(CommonOperationProductDto req);

    /**
     * 内容管理-通用faq-删除
     *
     * @param commonFaqId 通用faqid
     */
    void delete(Long commonFaqId);


    /**
     * 导入
     *
     * @param file 文件
     * @return 错误信息
     */
    List<String> importCommonFaq(MultipartFile file);

    /**
     * 根据条件查询数据列表
     *
     * @param req 查询条件
     * @return 列表数据
     */
    List<CommonFaqExcel> listData(CommonFaqManagePageDto req);

    /**
     * 获取该数据所有已关联产品id集合
     *
     * @param commonFaqId 通用faq id
     * @return 产品id集合
     */
    List<Long> productIdList(Long commonFaqId);
}
