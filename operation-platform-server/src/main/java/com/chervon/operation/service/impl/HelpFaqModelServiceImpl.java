package com.chervon.operation.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.operation.domain.dataobject.HelpFaqModel;
import com.chervon.operation.mapper.HelpFaqModelMapper;
import com.chervon.operation.service.HelpFaqModelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HelpFaqModelServiceImpl extends ServiceImpl<HelpFaqModelMapper, HelpFaqModel> implements HelpFaqModelService {
    @Override
    public void removeByHelpFaqId(Long helpFaqId) {
        remove(Wrappers.<HelpFaqModel>lambdaQuery()
                .eq(HelpFaqModel::getHelpFaqId, helpFaqId)
        );
    }

    @Override
    public List<HelpFaqModel> listByHelpFaqId(Long helpFaqId) {
        return list(Wrappers.<HelpFaqModel>lambdaQuery()
                .eq(HelpFaqModel::getHelpFaqId, helpFaqId)
        );
    }

    @Override
    public void batchUpdateByHelpFaqId(Long helpFaqId, List<String> models) {
        //删除helpFaqModel
        removeByHelpFaqId(helpFaqId);
        //保存helpFaqModel
        if(!CollectionUtils.isEmpty(models)){
            List<HelpFaqModel> helpFaqModels = models.stream().map(x -> {
                HelpFaqModel helpFaqModel = new HelpFaqModel();
                helpFaqModel.setHelpFaqId(helpFaqId);
                helpFaqModel.setCommodityModel(x);
                return helpFaqModel;
            }).collect(Collectors.toList());
            saveBatch(helpFaqModels);
        }
    }
}
