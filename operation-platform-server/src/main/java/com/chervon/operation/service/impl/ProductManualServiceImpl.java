package com.chervon.operation.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.operation.api.exception.OperationErrorCode;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.domain.dataobject.ProductManual;
import com.chervon.operation.domain.dataobject.UserManual;
import com.chervon.operation.domain.dto.manual.product.ProductManualDto;
import com.chervon.operation.domain.dto.postsale.PostSaleBaseDto;
import com.chervon.operation.domain.vo.manual.ManualVo;
import com.chervon.operation.domain.vo.manual.product.ProductManualVo;
import com.chervon.operation.mapper.ProductManualMapper;
import com.chervon.operation.service.ProductManualService;
import com.chervon.operation.service.UserManualService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/22 10:55
 */
@Service
@Slf4j
public class ProductManualServiceImpl extends ServiceImpl<ProductManualMapper, ProductManual> implements ProductManualService {

    @Autowired
    private UserManualService userManualService;

    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;

    @Override
    public List<ProductManualVo> listByProductId(Long productId) {
        List<ProductManual> list = this.list(new LambdaQueryWrapper<ProductManual>()
                .eq(ProductManual::getProductId, productId)
                .orderByDesc(ProductManual::getCreateTime));
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<Long> manualIds = list.stream().map(ProductManual::getManualId).distinct().collect(Collectors.toList());
        List<UserManual> manualList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(manualIds)) {
            manualList = userManualService.list(new LambdaQueryWrapper<UserManual>().in(UserManual::getId, manualIds));
        }
        Map<Long, UserManual> collect = manualList.stream().collect(Collectors.toMap(BaseDo::getId, Function.identity()));
        return list.stream().map(e -> {
            ProductManualVo vo = new ProductManualVo();
            vo.setProductManualId(e.getId());
            ManualVo manual = new ManualVo();
            UserManual userManual = collect.getOrDefault(e.getManualId(), new UserManual());
            BeanUtils.copyProperties(userManual, manual);
            manual.setName(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(userManual.getNameLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            manual.setDescription(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(userManual.getDescriptionLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            vo.setManual(manual);
            return vo;
        }).collect(Collectors.toList());
    }

    private void checkProductManual(ProductManualDto req) {
        if (req.getManual() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_POST_SALE_USER_MANUAL_NULL, "null");
        }
    }

    @Override
    public void edit(ProductManualDto req) {
        if (req.getOperateItemId() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_POST_SALE_USER_MANUAL_ID_NULL);
        }
        ProductManual productManual = this.getById(req.getOperateItemId());
        if (productManual == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_POST_SALE_USER_MANUAL_NULL, req.getOperateItemId());
        }

        checkProductManual(req);
        req.getManual().setManualId(productManual.getManualId());
        userManualService.edit(req.getManual());
        ProductManual one = new ProductManual();
        one.setId(productManual.getId());
        this.updateById(one);
    }

    @Override
    public void delete(PostSaleBaseDto req) {
        if (req.getOperateItemId() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_POST_SALE_USER_MANUAL_ID_NULL);
        }
        ProductManual productManual = this.getById(req.getOperateItemId());
        if (productManual == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_POST_SALE_USER_MANUAL_NULL, req.getOperateItemId());
        }

        UserManual userManual = userManualService.getById(productManual.getManualId());
        if (userManual != null) {
            userManualService.removeById(productManual.getManualId());
            // 清理多语言
            List<Long> list = new ArrayList<>();
            if (userManual.getNameLangId() != null) {
                list.add(userManual.getNameLangId());
            }
            if (userManual.getDescriptionLangId() != null) {
                list.add(userManual.getDescriptionLangId());
            }
            if (CollectionUtil.isNotEmpty(list)) {
                remoteMultiLanguageService.deleteByLangIds(list);
            }
        }
        this.removeById(productManual.getId());
    }

    @Override
    public String getUrl(Long productManualId) {
        if (productManualId == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_POST_SALE_USER_MANUAL_ID_NULL);
        }
        ProductManual productManual = this.getById(productManualId);
        if (productManual == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_POST_SALE_USER_MANUAL_NULL, productManualId);
        }
        UserManual userManual = userManualService.getById(productManual.getManualId());
        return Optional.ofNullable(userManual).orElse(new UserManual()).getUrl();
    }

    @Override
    public void add(ProductManualDto req) {
        checkProductManual(req);
        UserManual userManual = userManualService.add(req.getManual());
        ProductManual one = new ProductManual();
        one.setManualId(userManual.getId());
        one.setProductId(req.getProductId());
        this.save(one);
    }

    @Override
    public ProductManualVo detail(Long productManualId) {
        if (productManualId == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_POST_SALE_USER_MANUAL_ID_NULL);
        }
        ProductManual productManual = this.getById(productManualId);
        if (productManual == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_POST_SALE_USER_MANUAL_NULL, productManualId);
        }
        ProductManualVo res = new ProductManualVo();
        res.setProductManualId(productManual.getId());
        ManualVo manual = new ManualVo();
        UserManual userManual = userManualService.getById(productManual.getManualId());
        if (userManual != null) {
            BeanUtils.copyProperties(userManual, manual);
            manual.setName(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(userManual.getNameLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            manual.setDescription(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(userManual.getDescriptionLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
        }
        res.setManual(manual);
        return res;
    }



}
