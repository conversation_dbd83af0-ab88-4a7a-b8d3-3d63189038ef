package com.chervon.operation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.iot.middle.api.dto.device.IotGroupDeviceDto;
import com.chervon.operation.domain.dataobject.Groups;
import com.chervon.operation.domain.dto.group.GroupDto;
import com.chervon.operation.domain.dto.group.GroupShortInfoDto;
import com.chervon.operation.domain.dto.group.ListGroupDto;
import com.chervon.operation.domain.dto.group.PageGroupDto;
import com.chervon.operation.api.domain.dto.PageGroupUserDto;
import com.chervon.operation.domain.vo.group.GroupDeviceListVo;
import com.chervon.operation.domain.vo.group.GroupUserVo;
import com.chervon.operation.domain.vo.group.GroupVo;
import java.util.List;

/**
 * <p>
 * 设备表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
public interface GroupService extends IService<Groups> {

    /**
     * 创建设备分组
     * <AUTHOR>
     * @date 14:05 2022/7/28
     * @param groupDto:
     * @return void
     **/
    void add(GroupDto groupDto);

    /**
     * 检查分组名称是否存在
     * <AUTHOR>
     * @date 16:44 2022/7/28
     * @param groupName:
     * @return java.lang.Boolean
     **/
    Boolean checkGroupName(SingleInfoReq<String> groupName);

    /**
     * 分页获取分组信息
     * <AUTHOR>
     * @date 17:42 2022/7/28
     * @param pageGroupDto:
     * @return com.chervon.common.core.domain.PageResult<com.chervon.technology.domain.vo.group.GroupVo>
     **/
    PageResult<GroupVo> getPage(PageGroupDto pageGroupDto);

    /**
     * 修改分组
     * <AUTHOR>
     * @date 18:39 2022/7/28
     * @param groupDto:
     * @return void
     **/
    void edit(GroupDto groupDto);

    /**
     * 按分组名删除分组
     * <AUTHOR>
     * @date 19:34 2022/7/28
     * @param groupName:
     * @return List<String>
     **/
    List<String> delete(SingleInfoReq<String> groupName);

    /**
     * 获取分组下的设备
     * <AUTHOR>
     * @date 19:41 2022/7/28
     * @param iotGroupDeviceDto:
     * @return com.chervon.technology.domain.vo.group.GroupDeviceListVo
     **/
    GroupDeviceListVo queryDevices(IotGroupDeviceDto iotGroupDeviceDto);

    /**
     * 获取分组Arn列表
     * <AUTHOR>
     * @date 16:42 2022/8/10
     * @param groupNames:
     * @return java.util.List<java.lang.String>
     **/
    List<String> listArns(List<String> groupNames);

    /**
     * 获取分组列表
     * <AUTHOR>
     * @date 9:56 2022/8/31
     * @param listGroupDto:
     * @return java.util.List<com.chervon.operation.domain.vo.group.GroupVo>
     **/
    List<GroupVo> listGroup(ListGroupDto listGroupDto);

    /**
     * 获取分组用户列表
     * <AUTHOR>
     * @date 11:04 2022/8/31
     * @param pageRequest:
     * @return com.chervon.common.core.domain.PageResult<com.chervon.operation.domain.vo.group.GroupUserVo>
     **/
    PageResult<GroupUserVo> queryUsers(PageGroupUserDto pageRequest);

    /**
     * 获取用户分组名称列表
     * <AUTHOR>
     * @date 11:29 2022/9/1
     * @return java.util.List<java.lang.String>
     **/
    List<String> listUserGroupName();

    /**
     * 获取分组的基本信息
     * @return 分组信息
     */
    List<Groups> listUserGroupShortInfo();

    /**
     * 查询是否命中分组条件
     * <AUTHOR>
     * @date 11:56 2022/9/1
     * @param queryUserString:
     * @return boolean
     **/
    boolean hasGroup(String queryUserString);

    /**
     * 分页获取分组下的用户id
     * <AUTHOR>
     * @date 13:40 2022/9/1
     * @param pageNum:
     * @param pageSize:
     * @param queryUserString:
     * @return com.chervon.common.core.domain.PageResult<java.lang.Long>
     **/
    PageResult<Long> pageUserIds(Integer pageNum, Integer pageSize, String queryUserString);

    /**
     * 获取分组下的用户id列表
     * <AUTHOR>
     * @date 13:40 2022/9/1
     * @param queryUserString:
     * @return java.util.List<java.lang.Long>
     **/
    List<Long> listUserId(String queryUserString);

}
