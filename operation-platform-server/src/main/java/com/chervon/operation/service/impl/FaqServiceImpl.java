package com.chervon.operation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.enums.ApplicationEnum;
import com.chervon.common.core.utils.SnowFlake;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.operation.api.exception.OperationErrorCode;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.domain.dataobject.Faq;
import com.chervon.operation.domain.dto.faq.FaqDto;
import com.chervon.operation.domain.dto.faq.FaqRead;
import com.chervon.operation.mapper.FaqMapper;
import com.chervon.operation.service.FaqService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/19 15:31
 */
@Service
@Slf4j
public class FaqServiceImpl extends ServiceImpl<FaqMapper, Faq> implements FaqService {

    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;

    @Override
    public Faq add(FaqDto faq) {
        check(faq);
        Map<String, String> map = new HashMap<>();
        map.put("1", faq.getTitle());
        map.put("2", faq.getAnswer());
        Map<String, MultiLanguageBo> languages = remoteMultiLanguageService.simpleCreateMultiLanguages(ApplicationEnum.OPERATION_PLATFORM.getName(), map, LocaleContextHolder.getLocale().getLanguage());
        Faq one = new Faq();
        BeanUtils.copyProperties(faq, one);
        MultiLanguageBo title = languages.getOrDefault("1", new MultiLanguageBo());
        one.setTitleLangId(title.getLangId());
        one.setTitleLangCode(title.getLangCode());
        MultiLanguageBo answer = languages.getOrDefault("2", new MultiLanguageBo());
        one.setAnswerLangId(answer.getLangId());
        one.setAnswerLangCode(answer.getLangCode());
        one.setInstanceId(SnowFlake.nextId());
        this.save(one);
        return one;
    }

    @Override
    public void edit(FaqDto faq) {
        check(faq);
        if (faq.getFaqId() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_FAQ_ID_NULL);
        }
        Faq item = this.getById(faq.getFaqId());
        if (item == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_FAQ_NULL, faq.getFaqId());
        }
        Faq one = new Faq();
        BeanUtils.copyProperties(faq, one);
        one.setId(item.getId());
        Map<Long, String> map = new HashMap<>();
        map.put(item.getTitleLangId(), faq.getTitle());
        map.put(item.getAnswerLangId(), faq.getAnswer());
        remoteMultiLanguageService.simpleUpdateMultiLanguages(ApplicationEnum.OPERATION_PLATFORM.getName(), map, LocaleContextHolder.getLocale().getLanguage());
        this.updateById(one);
    }

    @Override
    public Map<String, List<String>> importFaq(List<FaqRead> data) {
        Map<String, List<String>> res = new HashMap<>();
        List<String> create = new ArrayList<>();
        List<String> update = new ArrayList<>();
        List<String> error = new ArrayList<>();
        res.put("create", create);
        res.put("update", update);
        res.put("error", error);
        if (!validImport(data, error)) {
            return res;
        }
        List<String> collect = data.stream().map(FaqRead::getInstanceId).collect(Collectors.toList());
        List<Faq> orFaq = new ArrayList<>();
        if (!CollectionUtils.isEmpty(collect)) {
            orFaq = this.list(new LambdaQueryWrapper<Faq>().in(Faq::getInstanceId, collect));
        }
        Map<Long, Faq> orFaqMap = orFaq.stream().collect(Collectors.toMap(Faq::getInstanceId, Function.identity()));

        Map<Long, String> mUpdate = new HashMap<>();
        Map<String, String> mCreate = new HashMap<>();
        List<Faq> list = data.stream().map(e -> {
            Faq faq = new Faq();
            faq.setTitle(e.getTitle());
            faq.setAnswer(e.getAnswer());
            if (StringUtils.isNotBlank(e.getInstanceId())) {
                Faq f = orFaqMap.get(Long.parseLong(e.getInstanceId()));
                if (f != null) {
                    update.add(String.valueOf(f.getId()));
                    faq.setId(f.getId());
                    if (f.getTitleLangId() != null) {
                        mUpdate.put(f.getTitleLangId(), e.getTitle());
                    } else {
                        mCreate.put(e.getTitle(), e.getTitle());
                    }
                    if (f.getAnswerLangId() != null) {
                        mUpdate.put(f.getAnswerLangId(), e.getAnswer());
                    } else {
                        mCreate.put(e.getAnswer(), e.getAnswer());
                    }
                } else {
                    faq.setInstanceId(SnowFlake.nextId());
                    mCreate.put(e.getTitle(), e.getTitle());
                    mCreate.put(e.getAnswer(), e.getAnswer());
                }
            } else {
                faq.setInstanceId(SnowFlake.nextId());
                mCreate.put(e.getTitle(), e.getTitle());
                mCreate.put(e.getAnswer(), e.getAnswer());
            }
            return faq;
        }).collect(Collectors.toList());
        if (mUpdate.size() > 0) {
            remoteMultiLanguageService.simpleUpdateMultiLanguages(ApplicationEnum.OPERATION_PLATFORM.getName(), mUpdate, LocaleContextHolder.getLocale().getLanguage());
        }
        if (mCreate.size() > 0) {
            Map<String, MultiLanguageBo> boMap = remoteMultiLanguageService.simpleCreateMultiLanguages(ApplicationEnum.OPERATION_PLATFORM.getName(), mCreate, LocaleContextHolder.getLocale().getLanguage());
            list.forEach(e -> {
                if (e.getTitleLangId() == null) {
                    MultiLanguageBo bo = boMap.get(e.getTitle());
                    e.setTitleLangId(bo.getLangId());
                    e.setTitleLangCode(bo.getLangCode());
                }
                if (e.getAnswerLangId() == null) {
                    MultiLanguageBo bo = boMap.get(e.getAnswer());
                    e.setAnswerLangId(bo.getLangId());
                    e.setAnswerLangCode(bo.getLangCode());
                }
            });
        }
        this.saveOrUpdateBatch(list);
        list.forEach(e -> {
            if (!update.contains(String.valueOf(e.getId()))) {
                create.add(e.getId() + "&&" + e.getTitleLangId());
            }
        });
        return res;
    }

    private boolean validImport(List<FaqRead> data, List<String> res) {
        List<Integer> idString = new ArrayList<>();
        Map<String, List<Integer>> idMap = new HashMap<>();
        List<Integer> titleNullFlag = new ArrayList<>();
        List<Integer> answerNullFlag = new ArrayList<>();

        for (int i = 2, j = data.size() + 2; i < j; i++) {
            FaqRead read = data.get(i - 2);

            if (StringUtils.isNotBlank(read.getInstanceId())) {
                try {
                    Long.parseLong(read.getInstanceId());
                } catch (Exception e) {
                    idString.add(i);
                }
                if (idMap.get(read.getInstanceId()) == null) {
                    List<Integer> ids = new ArrayList<>();
                    ids.add(i);
                    idMap.put(read.getInstanceId(), ids);
                } else {
                    idMap.get(read.getInstanceId()).add(i);
                }
            }

            if (StringUtils.isBlank(read.getTitle())) {
                titleNullFlag.add(i);
            }

            if (StringUtils.isBlank(read.getAnswer())) {
                answerNullFlag.add(i);
            }
        }
        // 检测id是否为数字
        if (idString.size() > 0) {
            res.add("第" + idString.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【问题ID】非法");
        }
        // 检测id是否唯一
        for (Map.Entry<String, List<Integer>> entry : idMap.entrySet()) {
            List<Integer> value = entry.getValue();
            if (value.size() != 1) {
                res.add("第" + value.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【问题ID】重复");
            }
        }
        // 检测title是否必填
        if (titleNullFlag.size() > 0) {
            res.add("第" + titleNullFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【问题】为空");
        }
        // 检测answer是否必填
        if (answerNullFlag.size() > 0) {
            res.add("第" + answerNullFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【解决办法】为空");
        }

        if (!res.isEmpty()) {
            return false;
        }

        List<Long> ids = data.stream().filter(e -> StringUtils.isNotBlank(e.getInstanceId())).map(e -> Long.parseLong(e.getInstanceId())).distinct().collect(Collectors.toList());
        if (!ids.isEmpty()) {
            List<Faq> faqs = this.list(new LambdaQueryWrapper<Faq>().in(Faq::getInstanceId, ids));
            List<Long> existIds = faqs.stream().map(Faq::getInstanceId).collect(Collectors.toList());
            for (int i = 2, j = data.size() + 2; i < j; i++) {
                FaqRead read = data.get(i - 2);
                if (StringUtils.isNotBlank(read.getInstanceId()) && !existIds.contains(Long.parseLong(read.getInstanceId()))) {
                    res.add("第" + i + "行，【问题ID】不存在");
                }
            }
        }
        return res.isEmpty();
    }

    private void check(FaqDto faq) {
        if (faq.isNeedTypeCode() && StringUtils.isBlank(faq.getTypeCode())) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_FAQ_TYPE_ERROR);
        }
        if (StringUtils.isBlank(faq.getTitle())) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_FAQ_TITLE_NULL);
        }
        if (StringUtils.isBlank(faq.getAnswer())) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_FAQ_ANSWER_NULL);
        }
    }
}
