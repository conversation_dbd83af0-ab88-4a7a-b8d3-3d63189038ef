package com.chervon.operation.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.DesensitizedUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.incrementer.DefaultIdentifierGenerator;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.iot.app.api.RemoteAppDeviceService;
import com.chervon.iot.middle.api.dto.device.IotGroupDeviceDto;
import com.chervon.iot.middle.api.dto.group.AddAwsStaticGroupDto;
import com.chervon.iot.middle.api.service.RemoteAwsGroupService;
import com.chervon.iot.middle.api.vo.device.IotDeviceQueryListVo;
import com.chervon.operation.api.domain.GroupConditionItem;
import com.chervon.operation.api.domain.dto.PageGroupUserDto;
import com.chervon.operation.api.enums.*;
import com.chervon.operation.api.exception.OperationErrorCode;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.domain.dataobject.GroupCondition;
import com.chervon.operation.domain.dataobject.GroupConditionValue;
import com.chervon.operation.domain.dataobject.Groups;
import com.chervon.operation.domain.dto.group.GroupDto;
import com.chervon.operation.domain.dto.group.ListGroupDto;
import com.chervon.operation.domain.dto.group.PageGroupDto;
import com.chervon.operation.domain.vo.group.GroupDeviceListVo;
import com.chervon.operation.domain.vo.group.GroupDeviceVo;
import com.chervon.operation.domain.vo.group.GroupUserVo;
import com.chervon.operation.domain.vo.group.GroupVo;
import com.chervon.operation.mapper.*;
import com.chervon.operation.service.DeviceAgreementService;
import com.chervon.operation.service.GroupConditionService;
import com.chervon.operation.service.GroupConditionValueService;
import com.chervon.operation.service.GroupService;
import com.chervon.technology.api.RemoteDeviceManageService;
import com.chervon.technology.api.RemoteOtaJobService;
import com.chervon.technology.api.RemoteTechProductOperationService;
import com.chervon.technology.api.enums.DeviceOnlineStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 设备表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
@Slf4j
@Service
public class GroupServiceImpl extends ServiceImpl<GroupMapper, Groups> implements
        GroupService {
    @DubboReference
    private RemoteAwsGroupService remoteAwsGroupService;

    @DubboReference
    private RemoteAppDeviceService remoteAppDeviceService;


    @DubboReference
    private RemoteDeviceManageService remoteDeviceManageService;

    @DubboReference
    private RemoteOtaJobService remoteOtaJobService;

    @DubboReference
    private RemoteTechProductOperationService remoteTechProductOperationService;

    @Autowired
    private GroupConditionService groupConditionService;

    @Resource
    private GroupMapper groupMapper;

    @Resource
    private AppAgreementMapper appAgreementMapper;

    @Resource
    private DeviceAgreementService deviceAgreementService;

    @Resource
    private CommonFaqMapper commonFaqMapper;

    @Resource
    private CommonOperationGuidanceMapper commonOperationGuidanceMapper;

    @Resource
    private SysMsgMapper sysMsgMapper;

    @Resource
    private MarketingMsgMapper marketingMsgMapper;

    @Resource
    private GroupConditionValueService groupConditionValueService;

    /**
     * AWS DEVICEID 正则 [a-zA-Z0-9:_-]+
     */
    private static final String AWS_DEVICE_REGEX="[a-zA-Z0-9:_-]+";


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(GroupDto groupDto) {
        if (CollectionUtil.isEmpty(groupDto.getItems())) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_CONDITION_CAN_NOT_BE_EMPTY);
        }
        // 保存分组信息
        Groups groups = ConvertUtil.convert(groupDto, Groups.class);

        LambdaQueryWrapper<Groups> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Groups::getGroupName, groupDto.getGroupName());
        if (this.count(wrapper) > 0) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_GROUP_NAME_ALREADY_EXISTS, groupDto.getGroupName());
        }
        checkUserIdValid(groupDto);
        checkDeviceIdValid(groupDto);
        if (GroupTypeEnum.DEVICE_GROUP.equals(groupDto.getGroupType())) {
            AddAwsStaticGroupDto addAwsStaticGroupDto = ConvertUtil.convert(groupDto,
                    AddAwsStaticGroupDto.class);
            // 将分组信息保存到iot core
            String groupArn = remoteAwsGroupService.addStaticGroup(addAwsStaticGroupDto);
            groups.setGroupArn(groupArn);

            List<String> deviceIds = getQueryDeviceIds(groupDto);
            remoteAwsGroupService.batchAddDevice2StaticGroup(groupDto.getGroupName(), deviceIds);
        }
        this.save(groups);
        saveGroupCondition(groupDto, groups);
    }

    private void saveGroupCondition(GroupDto groupDto, Groups groups) {
        // 保存分组条件信息
        List<GroupCondition> groupConditions = new ArrayList<>();
        List<GroupConditionValue> groupConditionValues = new ArrayList<>();
        for (GroupConditionItem conditionItem : groupDto.getItems()) {
            GroupCondition groupCondition = ConvertUtil.convert(conditionItem, GroupCondition.class);
            groupCondition.setId(new DefaultIdentifierGenerator().nextId(groupCondition));
            groupCondition.setGroupId(groups.getId());
            groupCondition.setConditionValue(null);
            groupConditions.add(groupCondition);
            String[] values = conditionItem.getConditionValue().split(",");
            for (String value : values) {
                GroupConditionValue groupConditionValue = new GroupConditionValue();
                groupConditionValue.setGroupConditionId(groupCondition.getId());
                groupConditionValue.setConditionValue(value);
                groupConditionValues.add(groupConditionValue);
            }
        }
        groupConditionService.saveBatch(groupConditions);
        groupConditionValueService.saveBatch(groupConditionValues);
    }

    @Override
    public Boolean checkGroupName(SingleInfoReq<String> groupName) {
        LambdaQueryWrapper<Groups> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Groups::getGroupName, groupName.getReq());
        return this.count(wrapper) > 0;
    }

    @Override
    public PageResult<GroupVo> getPage(PageGroupDto pageGroupDto) {
        Page<Groups> page = new Page<>(pageGroupDto.getPageNum(), pageGroupDto.getPageSize());
        Page<Groups> pageResult = groupMapper.getPage(page, pageGroupDto);
        PageResult<GroupVo> result = new PageResult<>(pageResult.getCurrent(),
                pageResult.getSize(), pageResult.getTotal());
        List<GroupVo> groupVos = new ArrayList<>();
        for (Groups groups : pageResult.getRecords()) {
            GroupVo groupVo = ConvertUtil.convert(groups, GroupVo.class);
            List<String> conditionContents = new LinkedList<>();
            List<GroupConditionRule> conditionRules = new LinkedList<>();
            List<String> conditionValues = new LinkedList<>();
            List<GroupConditionTypeEnum> conditionTypes = new ArrayList<>();
            List<GroupCondition> conditions = groupConditionService.listByGroupId(groupVo.getId());
            for (GroupCondition condition : conditions) {
                conditionContents.add(condition.getConditionContent());
                conditionRules.add(condition.getConditionRule());
                conditionValues.add(condition.getConditionValue());
                conditionTypes.add(condition.getConditionType());
            }
            groupVo.setConditionContents(conditionContents);
            groupVo.setConditionRules(conditionRules);
            groupVo.setConditionValues(conditionValues);
            groupVo.setConditionTypes(conditionTypes);
            groupVos.add(groupVo);
        }
        result.setList(groupVos);
        return result;
    }

    /**
     * 用户分组中用户ID分组条件需要满足条件内容全数字
     *
     * @param groupDto 分组Dto
     */
    public void checkUserIdValid(GroupDto groupDto) {
        if (null != groupDto.getItems() && GroupTypeEnum.USER_GROUP.equals(groupDto.getGroupType())) {
            for (GroupConditionItem item : groupDto.getItems()) {
                if ("USER_ID".equals(item.getConditionContent()) &&
                        GroupConditionTypeEnum.USER_CONDITION.equals(item.getConditionType()) &&
                        GroupConditionRule.EQUALS.equals(item.getConditionRule()) &&
                        !item.getConditionValue().matches("[0-9]*")) {
                    throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_CONDITION_VALUE_ERROR);
                }
            }
        }
    }

    /**
     * 设备分组中中设备ID分组条件需要满足AWS 设备Id规则
     *
     * @param groupDto
     */
    private void checkDeviceIdValid(GroupDto groupDto) {
        if (null != groupDto.getItems() && GroupTypeEnum.DEVICE_GROUP.equals(groupDto.getGroupType())) {
            for (GroupConditionItem item : groupDto.getItems()) {
                //设备条件且设备是设备Id
                if ("DEVICE_ID".equals(item.getConditionContent()) &&
                        GroupConditionTypeEnum.DEVICE_CONDITION.equals(item.getConditionType())) {
                    //判断规则等于
                    if (GroupConditionRule.EQUALS.equals(item.getConditionRule()) && !item.getConditionValue().matches(AWS_DEVICE_REGEX)) {
                        throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_CONDITION_VALUE_ERROR);
                    }
                    //判断规则包含
                    else if (GroupConditionRule.INCLUDE.equals(item.getConditionRule())) {
                        //每个设备id满足条件
                        String[] deviceIds=item.getConditionValue().split(",");
                        Arrays.stream(deviceIds).forEach(x->{
                            if(!x.matches(AWS_DEVICE_REGEX)){
                                throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_CONDITION_VALUE_ERROR);
                            }
                        });
                    }
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(GroupDto groupDto) {
        checkUserIdValid(groupDto);
        checkDeviceIdValid(groupDto);
        // 将设备分组信息保存到iot core
        if (GroupTypeEnum.DEVICE_GROUP.equals(groupDto.getGroupType())) {
            List<String> deviceIds = getQueryDeviceIds(groupDto);
            remoteAwsGroupService.batchAddDevice2StaticGroup(groupDto.getGroupName(), deviceIds);
        }
        LambdaQueryWrapper<Groups> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Groups::getGroupName, groupDto.getGroupName());
        Groups groups = this.getOne(wrapper);
        groups.setComments(groupDto.getComments());
        this.updateById(groups);
        // 保存分组条件信息
        LambdaQueryWrapper<GroupCondition> removeWrapper = new LambdaQueryWrapper<>();
        removeWrapper.eq(GroupCondition::getGroupId, groups.getId());
        groupConditionService.remove(removeWrapper);
        saveGroupCondition(groupDto, groups);
    }

    @Override
    public List<String> delete(SingleInfoReq<String> groupName) {
        LambdaQueryWrapper<Groups> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Groups::getGroupName, groupName.getReq()).last("limit 1");
        Groups one = this.getOne(wrapper);
        List<String> messageList = new ArrayList<>();
        if (one != null && one.getGroupType().equals(GroupTypeEnum.DEVICE_GROUP)) {
            List<Long> jobIds = remoteOtaJobService.getJobIdsByGroupName(
                    groupName.getReq());
            if (CollectionUtil.isNotEmpty(jobIds)) {
                String message = StringUtils.join(jobIds, ", ");
                messageList.add("固件发布，任务ID：" + message);
            }
            if (CollectionUtil.isNotEmpty(messageList)) {
                return messageList;
            }
            remoteAwsGroupService.deleteStaticGroup(groupName.getReq());
        } else {
            if (StringUtils.isNotBlank(groupName.getReq())) {
                List<Long> ids = appAgreementMapper.selectListIdByGroupName(groupName.getReq());
                if (CollectionUtil.isNotEmpty(ids)) {
                    messageList.add("APP协议发布，协议版本ID：" + StringUtils.join(ids, ", "));
                }
            }
            if (StringUtils.isNotBlank(groupName.getReq())) {
                List<Long> deviceAgreementIds = deviceAgreementService.listDeviceAgreementIdByGroupName(groupName.getReq());
                if (CollectionUtil.isNotEmpty(deviceAgreementIds)) {
                    messageList.add("APP设备协议发布，协议版本ID：" + StringUtils.join(deviceAgreementIds, ", "));
                }
            }
            if (StringUtils.isNotBlank(groupName.getReq())) {
                List<Long> commonFaqIds = commonFaqMapper.selectListIdByGroupName(groupName.getReq());
                if (CollectionUtil.isNotEmpty(commonFaqIds)) {
                    messageList.add("通用faq发布，通用faq ID：" + StringUtils.join(commonFaqIds, ", "));
                }
            }
            if (StringUtils.isNotBlank(groupName.getReq())) {
                List<Long> commonOperationGuidanceIds = commonOperationGuidanceMapper.selectListIdByGroupName(groupName.getReq());
                if (CollectionUtil.isNotEmpty(commonOperationGuidanceIds)) {
                    messageList.add("通用售后发布，通用售后ID：" + StringUtils.join(commonOperationGuidanceIds, ", "));
                }
            }
            if (StringUtils.isNotBlank(groupName.getReq())) {
                List<Long> sysMsgIds = sysMsgMapper.selectListIdByGroupName(groupName.getReq());
                if (CollectionUtil.isNotEmpty(sysMsgIds)) {
                    messageList.add("APP系统消息发布，APP系统消息ID：" + StringUtils.join(sysMsgIds, ", "));
                }
            }
            if (StringUtils.isNotBlank(groupName.getReq())) {
                List<Long> marketingMsgIds = marketingMsgMapper.selectListIdByGroupName(groupName.getReq());
                if (CollectionUtil.isNotEmpty(marketingMsgIds)) {
                    messageList.add("APP营销消息发布，APP营销消息ID：" + StringUtils.join(marketingMsgIds, ", "));
                }
            }
            if (CollectionUtil.isNotEmpty(messageList)) {
                return messageList;
            }
        }
        this.remove(wrapper);
        return messageList;
    }

    @Override
    public PageResult<GroupUserVo> queryUsers(PageGroupUserDto pageRequest) {
        List<GroupCondition> conditions =
                groupConditionService.listByGroupName(pageRequest.getGroupName());
        String queryUserString = getQueryUserString(conditions);
        Page<GroupUserVo> page = new Page(pageRequest.getPageNum(), pageRequest.getPageSize());
        Page<GroupUserVo> groupUserVoPage = groupMapper.queryUsers(page, queryUserString);
        PageResult<GroupUserVo> result = new PageResult<>(pageRequest.getPageNum(),
                pageRequest.getPageSize());
        result.setTotal(groupUserVoPage.getTotal());
        result.setList(groupUserVoPage.getRecords());
        result.setPages(groupUserVoPage.getPages());
        return result;
    }

    @Override
    public List<String> listUserGroupName() {
        LambdaQueryWrapper<Groups> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Groups::getGroupType, GroupTypeEnum.USER_GROUP);
        wrapper.select(Groups::getGroupName);
        List<Object> objects = this.listObjs(wrapper);
        return (List<String>) (List) objects;
    }

    @Override
    public List<Groups> listUserGroupShortInfo() {
        LambdaQueryWrapper<Groups> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Groups::getGroupType, GroupTypeEnum.USER_GROUP);
        wrapper.select(Groups::getGroupName, Groups::getId);
        List<Groups> objects = this.list(wrapper);
        return objects;
    }

    @Override
    public boolean hasGroup(String queryUserString) {
        return groupMapper.hasGroup(queryUserString);
    }

    @Override
    public PageResult<Long> pageUserIds(Integer pageNum, Integer pageSize,
                                        String queryUserString) {
        Page<Long> page = new Page<>(pageNum, pageSize);
        Page<Long> result = groupMapper.pageUserIds(page, queryUserString);
        PageResult pageResult = new PageResult(pageNum, pageSize);
        pageResult.setPages(result.getPages());
        pageResult.setList(result.getRecords());
        pageResult.setTotal(result.getTotal());
        return pageResult;
    }

    @Override
    public List<Long> listUserId(String queryUserString) {
        return groupMapper.listUserId(queryUserString);
    }

    @Override
    public GroupDeviceListVo queryDevices(IotGroupDeviceDto iotGroupDeviceDto) {
        IotDeviceQueryListVo queryListVo = remoteAwsGroupService
                .searchGroupDevice(iotGroupDeviceDto);
        GroupDeviceListVo listVo = new GroupDeviceListVo();
        listVo.setNextToken(queryListVo.getNextToken());
        List<String> deviceIds = queryListVo.getDeviceIds();
        Map<String, List<Long>> boundUserIdsMap = remoteAppDeviceService
                .getBoundUserIdsMap(deviceIds);
        List<GroupDeviceVo> devices = new ArrayList<>();
        List<Map<String, Object>> deviceList = remoteDeviceManageService.listMapByIds(deviceIds);
        for (Map<String, Object> deviceMap : deviceList) {
            GroupDeviceVo groupDeviceVo = new GroupDeviceVo();
            groupDeviceVo.setDeviceId((String) deviceMap.get("deviceId"));
            List<Long> ids = boundUserIdsMap.get((String) deviceMap.get("deviceId"));
            List<String> collect = ids.stream().map(id ->
                    DesensitizedUtil.idCardNum(id.toString(), 7, 8)
            ).collect(Collectors.toList());
            groupDeviceVo.setUserIds(collect);
            groupDeviceVo.setProductModel((String) deviceMap.get("productModel"));
            groupDeviceVo.setOnlineStatus(DeviceOnlineStatusEnum.valueOf((String) deviceMap.get("isOnline")));
            devices.add(groupDeviceVo);
        }
        listVo.setDevices(devices);
        return listVo;
    }

    @Override
    public List<String> listArns(List<String> groupNames) {
        LambdaQueryWrapper<Groups> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Groups::getGroupName, groupNames).select(Groups::getGroupArn);
        List<Groups> objects = this.list(wrapper);
        return objects.stream().filter(Objects::nonNull).map(Groups::getGroupArn).collect(Collectors.toList());
    }

    @Override
    public List<GroupVo> listGroup(ListGroupDto listGroupDto) {
        List<Groups> groupList = groupMapper.listGroup(listGroupDto);
        List<GroupVo> groupVos = new ArrayList<>();
        for (Groups groups : groupList) {
            GroupVo groupVo = ConvertUtil.convert(groups, GroupVo.class);
            List<String> conditionContents = new LinkedList<>();
            List<GroupConditionRule> conditionRules = new LinkedList<>();
            List<String> conditionValues = new LinkedList<>();
            List<GroupConditionTypeEnum> conditionTypes = new ArrayList<>();
            List<GroupCondition> conditions = groupConditionService.listByGroupId(groupVo.getId());
            for (GroupCondition condition : conditions) {
                conditionContents.add(condition.getConditionContent());
                conditionRules.add(condition.getConditionRule());
                conditionValues.add(condition.getConditionValue());
                conditionTypes.add(condition.getConditionType());
            }
            groupVo.setConditionContents(conditionContents);
            groupVo.setConditionRules(conditionRules);
            groupVo.setConditionValues(conditionValues);
            groupVo.setConditionTypes(conditionTypes);
            groupVos.add(groupVo);
        }
        return groupVos;
    }

    public static void main(String[] args) {
        GroupCondition groupCondition = new GroupCondition();
        groupCondition.setConditionContent("USER_ID");
        groupCondition.setConditionRule(GroupConditionRule.INCLUDE);
        groupCondition.setConditionType(GroupConditionTypeEnum.USER_CONDITION);
        groupCondition.setConditionValue("1592717787172900866");
        List<GroupCondition> of = ListUtil.of(groupCondition);
        String queryUserString = GroupServiceImpl.getQueryUserString(of);
        System.out.println(queryUserString);
    }

    public static String getQueryUserString(List<GroupCondition> groupConditions) {
        StringBuilder query = new StringBuilder();
        for (int i = 0; i < groupConditions.size(); i++) {
            if (i > 0) {
                query.append(" AND ");
            }
            GroupCondition groupCondition = groupConditions.get(i);
            GroupConditionRule conditionRule = groupCondition.getConditionRule();
            String conditionValue = groupCondition.getConditionValue();
            if (groupCondition.getConditionType().equals(GroupConditionTypeEnum.DEVICE_CONDITION)) {
                query.append(DeviceConditionContent.
                        valueOf(groupCondition.getConditionContent()).getUserGroupContent());
            } else if (groupCondition.getConditionType().
                    equals(GroupConditionTypeEnum.USER_CONDITION)) {
                query.append(UserConditionContent.
                        valueOf(groupCondition.getConditionContent()).getUserGroupContent());
            } else {
                throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_CONDITION_TYPE_ERROR);
            }
            query.append(conditionRule.getUserRule());
            if (conditionRule.equals(GroupConditionRule.INCLUDE)) {
                query.append("(");
                String[] values = conditionValue.split(",");
                for (int index = 0; index < values.length; index++) {
                    if (index > 0) {
                        query.append(", ");
                    }
                    query.append('\'');
                    query.append(values[index]);
                    query.append('\'');
                }
                query.append(")");
            } else {
                query.append('\'');
                query.append(conditionValue);
                query.append('\'');
            }
        }
        return query.toString();
    }

    private List<String> getQueryDeviceIds(GroupDto groupDto) {
        List<GroupConditionItem> items = groupDto.getItems();
        List<String> deviceIds = new ArrayList<>();
        for (int i = 0; i < items.size(); i++) {
            GroupConditionItem item = items.get(i);
            String conditionValue = item.getConditionValue();
            List<String> tempIds = new ArrayList<>();

            if (item.getConditionType().equals(GroupConditionTypeEnum.DEVICE_CONDITION)) {
                if ("DEVICE_ID".equals(item.getConditionContent()) && GroupConditionRule.EQUALS.equals(item.getConditionRule())) {
                    tempIds.addAll(Arrays.asList(conditionValue));
                } else if ("DEVICE_ID".equals(item.getConditionContent()) && GroupConditionRule.INCLUDE.equals(item.getConditionRule())) {
                    String[] tempDeviceIds = conditionValue.split(",");
                    tempIds.addAll((Arrays.asList(tempDeviceIds)));
                }
            }
            if (i == 0) {
                deviceIds.addAll(tempIds);
            } else {
                //取交集
                deviceIds = deviceIds.stream().filter(x -> tempIds.contains(x)).collect(Collectors.toList());
            }
        }
        return deviceIds;
    }

    private String getQueryDeviceString(GroupDto groupDto) {
        StringBuilder query = new StringBuilder();
        List<GroupConditionItem> items = groupDto.getItems();
        for (int i = 0; i < items.size(); i++) {
            if (i > 0) {
                query.append(" AND ");
            }
            GroupConditionItem conditionItem = items.get(i);
            GroupConditionRule conditionRule = conditionItem.getConditionRule();
            String conditionValue = conditionItem.getConditionValue();
            if (conditionItem.getConditionType().equals(GroupConditionTypeEnum.DEVICE_CONDITION)) {
                query.append(DeviceConditionContent.
                        valueOf(conditionItem.getConditionContent()).getDeviceGroupContent());
            } else if (conditionItem.getConditionType().
                    equals(GroupConditionTypeEnum.USER_CONDITION)) {
                query.append(UserConditionContent.
                        valueOf(conditionItem.getConditionContent()).getDeviceGroupContent());
            } else {
                throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_CONDITION_TYPE_ERROR);
            }
            query.append(conditionRule.getDeviceRule());
            if (conditionRule.equals(GroupConditionRule.INCLUDE)) {
                query.append("(");
                String[] values = conditionValue.split(",");
                for (int index = 0; index < values.length; index++) {
                    if (index > 0) {
                        query.append(" OR ");
                    }
                    query.append(values[index]);
                }
                query.append(")");
            } else {
                query.append(conditionValue);
            }
        }
        return query.toString();
    }
}
