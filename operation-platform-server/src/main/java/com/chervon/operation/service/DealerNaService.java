package com.chervon.operation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.operation.api.vo.AppDealerVo;
import com.chervon.operation.domain.dataobject.DealerNa;
import com.chervon.operation.domain.dto.DealerNaDto;
import com.chervon.operation.domain.dto.DealerNaPageDto;
import com.chervon.operation.domain.dto.DealerNaRead;
import com.chervon.operation.domain.vo.DealerNaPageVo;
import com.chervon.operation.domain.vo.DealerNaVo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-08-15 14:48
 **/
public interface DealerNaService extends IService<DealerNa> {

    /**
     * 列表查询
     *
     * @param category 分类
     * @param lat      经度
     * @param lng      纬度
     * @param minLat   最小经度
     * @param maxLat   最大经度
     * @param minLng   最小纬度
     * @param maxLng   最大纬度
     * @param limit    是否限制条数
     * @return 列表数据
     */
    List<AppDealerVo> list(String category, double lat, double lng, Double minLat, Double maxLat, Double minLng, Double maxLng, boolean limit);

    /**
     * 列表查询
     *
     * @param category 分类
     * @param lat      经度
     * @param lng      纬度
     * @param distance 距离
     * @return 列表数据
     */
    List<AppDealerVo> list(String category, double lat, double lng, double distance);

    /**
     * 获取经销商信息和距离
     *
     * @param lat       纬度
     * @param lng       经度
     * @param dealerIds 经销商id集合
     * @return 数据集合
     */
    List<AppDealerVo> listWithDistanceByIds(Double lat, Double lng, List<Long> dealerIds);

    /**
     * 根据dealerId统计收藏数量
     * @param dealerIds
     * @return
     */
    Integer countByIds(List<Long> dealerIds);
    ////////////////////////////////////////////////////

    /**
     * 分页
     *
     * @param req 请求参数
     * @return 分页数据
     */
    PageResult<DealerNaPageVo> page(DealerNaPageDto req);


    /**
     * 详情
     *
     * @param dealerId 经销商id
     * @return 详情
     */
    DealerNaVo detail(Long dealerId);

    /**
     * 新增
     *
     * @param req 新增对象
     */
    void add(DealerNaDto req);

    /**
     * 编辑
     *
     * @param req 编辑对象
     */
    void edit(DealerNaDto req);

    /**
     * 删除
     *
     * @param dealerId 经销商id
     */
    void delete(Long dealerId);

    /**
     * 导入
     *
     * @param data 数据
     * @return 错误信息
     */
    List<String> importDealer(List<DealerNaRead> data);

}
