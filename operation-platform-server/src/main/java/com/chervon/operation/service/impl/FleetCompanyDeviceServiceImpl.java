package com.chervon.operation.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.fleet.web.api.entity.dto.FleetCompanyDevicePageDto;
import com.chervon.fleet.web.api.entity.vo.FleetCompanyDeviceVo;
import com.chervon.fleet.web.api.service.RemoteFleetDeviceService;
import com.chervon.operation.api.RemoteOperationCacheService;
import com.chervon.operation.api.vo.cache.BrandCache;
import com.chervon.operation.config.MultiLanguageUtil;
import com.chervon.operation.domain.dataobject.FleetCategory;
import com.chervon.operation.domain.vo.FleetCompanyDeviceExcel;
import com.chervon.operation.service.FleetCategoryService;
import com.chervon.operation.service.FleetCompanyDeviceService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/8/3 16:40
 */
@Service
@Slf4j
@AllArgsConstructor
public class FleetCompanyDeviceServiceImpl implements FleetCompanyDeviceService {

    @DubboReference
    private RemoteFleetDeviceService remoteFleetDeviceService;

    private final RemoteOperationCacheService remoteOperationCacheService;

    private final FleetCategoryService fleetCategoryService;

    @Override
    public PageResult<FleetCompanyDeviceVo> companyDevicePage(FleetCompanyDevicePageDto req) {
        List<FleetCompanyDeviceVo> list = remoteFleetDeviceService.companyDeviceList(req);
        PageResult<FleetCompanyDeviceVo> res = new PageResult<>(req.getPageNum(), req.getPageSize(), list.size());
        res.setList(ListUtil.page(req.getPageNum() - 1, req.getPageSize(), list));
        return res;
    }

    @Override
    public List<FleetCompanyDeviceExcel> companyDeviceListData(FleetCompanyDevicePageDto req) {
        List<FleetCompanyDeviceVo> list = remoteFleetDeviceService.companyDeviceList(req);
        List<String> cCodes = list.stream().map(FleetCompanyDeviceVo::getCategoryCode).filter(Objects::nonNull).flatMap(List::stream).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<Long> bIds = list.stream().map(FleetCompanyDeviceVo::getBrandId).filter(Objects::nonNull).flatMap(List::stream).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<FleetCategory> categories = new ArrayList<>();
        if (!CollectionUtils.isEmpty(cCodes)) {
            categories = fleetCategoryService.list(new LambdaQueryWrapper<FleetCategory>().in(FleetCategory::getCode, cCodes).eq(FleetCategory::getLanguage, LocaleContextHolder.getLocale().getLanguage()));
        }
        Map<String, FleetCategory> cMap = categories.stream().collect(Collectors.toMap(FleetCategory::getCode, Function.identity(), (k1, k2) -> k2));
        List<BrandCache> brands = new ArrayList<>();
        if (!CollectionUtils.isEmpty(bIds)) {
            brands = remoteOperationCacheService.listBrands(bIds);
        }
        Map<Long, BrandCache> bMap = brands.stream().collect(Collectors.toMap(BrandCache::getId, Function.identity()));
        boolean isEn = StringUtils.equals(LocaleContextHolder.getLocale().getLanguage(), RemoteOperationCacheService.DEFAULT_LANGUAGE);
        Map<String, String> bCodeMap = new HashMap<>();
        if (!isEn) {
            List<String> bCodes = brands.stream().map(BrandCache::getNameLangCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            bCodeMap = MultiLanguageUtil.getByLangCodes(bCodes, LocaleContextHolder.getLocale().getLanguage());
        }
        Map<String, String> finalBCodeMap = bCodeMap;
        List<FleetCompanyDeviceExcel> res = new ArrayList<>();
        list.forEach(e -> {
            int deviceIdSize = e.getDeviceId() == null ? 0 : e.getDeviceId().size();
            int deviceSnSize = e.getDeviceSn() == null ? 0 : e.getDeviceSn().size();
            int categorySize = e.getCategoryCode() == null ? 0 : e.getCategoryCode().size();
            int brandIdSize = e.getBrandId() == null ? 0 : e.getBrandId().size();
            int modelSize = e.getModel() == null ? 0 : e.getModel().size();
            int max = Stream.of(deviceIdSize, deviceSnSize, categorySize, brandIdSize, modelSize).max(Integer::compare).orElse(0);
            if (max > 0) {
                for (int i = 0; i < max; i++) {
                    FleetCompanyDeviceExcel excel = new FleetCompanyDeviceExcel();
                    BeanUtils.copyProperties(e, excel);
                    excel.setCount(max);
                    if (i < deviceIdSize) {
                        excel.setDeviceId(e.getDeviceId().get(i));
                    }
                    if (i < deviceSnSize) {
                        excel.setDeviceSn(e.getDeviceSn().get(i));
                    }
                    if (i < categorySize) {
                        String category = e.getCategoryCode().get(i);
                        if (category != null) {
                            FleetCategory fleetCategory = cMap.getOrDefault(category, new FleetCategory());
                            excel.setCategory(fleetCategory.getCategoryName());
                        }
                    }
                    if (i < brandIdSize) {
                        Long brandId = e.getBrandId().get(i);
                        if (brandId != null) {
                            BrandCache brandCache = bMap.get(brandId);
                            if (brandCache != null) {
                                if (isEn) {
                                    excel.setBrand(brandCache.getDefaultName());
                                } else {
                                    excel.setBrand(finalBCodeMap.get(brandCache.getNameLangCode()));
                                }
                            }
                        }
                    }
                    if (i < modelSize) {
                        excel.setModel(e.getModel().get(i));
                    }
                    res.add(excel);
                }
            } else {
                FleetCompanyDeviceExcel excel = new FleetCompanyDeviceExcel();
                BeanUtils.copyProperties(e, excel);
                excel.setCount(max);
                res.add(excel);
            }
        });
        return res;
    }

}
