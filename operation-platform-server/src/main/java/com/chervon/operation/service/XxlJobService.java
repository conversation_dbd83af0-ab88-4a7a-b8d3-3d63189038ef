package com.chervon.operation.service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-12-24 14:38
 **/
public interface XxlJobService {
    /**
     * 增加执行任务
     * 不包含执行参数
     *
     * @param jobDesc         任务描述
     * @param executorDate    执行日期
     * @param executorHandler 执行器Handler,@XxlJob注解中的字段
     * @param isEveryDay      是否每天执行,是的话会去掉执行日期中具体的日期，只取ss MM hh三个字段
     */
    void addExecutorTask(String jobDesc, LocalDateTime executorDate, String executorHandler, Boolean isEveryDay);

    /**
     * 增加执行任务
     * 包含执行参数
     *
     * @param jobDesc         任务描述
     * @param executorDate    执行日期
     * @param executorHandler 执行器Handler,@XxlJob注解中的字段
     * @param isEveryDay      是否每天执行,是的话会去掉执行日期中具体的日期，只取ss MM hh三个字段
     * @param executorParam   执行参数,目前只有消息Id
     */
    void addExecutorTask(String jobDesc, LocalDateTime executorDate, String executorHandler, Boolean isEveryDay, Long executorParam);

    /**
     * 获取执行id
     *
     * @return int
     */
    int getJobGroupId();

    /**
     * 根据任务描述搜索获取任务ID
     *
     * @param desc 任务ID,通过该类创建的描述一般为数据库中消息的主键ID
     * @return 相应的任务ID, 如果没有则为-1
     */
    int getJobInfoId(String desc);

    /**
     * 根据任务描述搜索获取任务ID列表
     *
     * @param desc 任务ID,通过该类创建的描述一般为数据库中消息的主键ID
     * @return 相应的任务ID列表, 最大限制为10
     */
    List<Integer> listJobInfoIdByDesc(String desc);

    /**
     * 删除任务
     *
     * @param id 任务ID
     */
    void removeJobInfo(Integer id);

    /**
     * 暂停任务
     *
     * @param id 任务ID
     */
    void stopJobInfo(Integer id);

    /**
     * 批量删除任务
     *
     * @param idList 任务ID列表
     */
    void removeJobInfoByIdList(List<Integer> idList);

    /**
     * 创建执行器
     */
    void createJobGroup();

    /**
     * 获取cookie
     *
     * @return String
     */
    String getCookie();
}
