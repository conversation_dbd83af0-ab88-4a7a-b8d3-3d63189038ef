package com.chervon.operation.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.enums.ApplicationEnum;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.core.utils.SnowFlake;
import com.chervon.common.oss.uitl.UrlUtil;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.operation.api.exception.OperationErrorCode;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.domain.dataobject.OperationGuidance;
import com.chervon.operation.domain.dto.operationguidance.OperationGuidanceDto;
import com.chervon.operation.mapper.OperationGuidanceMapper;
import com.chervon.operation.service.OperationGuidanceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/19 15:32
 */
@Service
@Slf4j
public class OperationGuidanceServiceImpl extends ServiceImpl<OperationGuidanceMapper, OperationGuidance> implements OperationGuidanceService {

    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;

    @Autowired
    private AwsProperties awsProperties;

    @Override
    public OperationGuidance add(OperationGuidanceDto operationGuidance) {
        check(operationGuidance);
        Map<String, String> map = new HashMap<>();
        map.put("1", operationGuidance.getName());
        if (StringUtils.isNotBlank(operationGuidance.getDescription())) {
            map.put("2", operationGuidance.getDescription());
        }
        Map<String, MultiLanguageBo> languages = remoteMultiLanguageService.simpleCreateMultiLanguages(ApplicationEnum.OPERATION_PLATFORM.getName(), map, LocaleContextHolder.getLocale().getLanguage());
        OperationGuidance one = new OperationGuidance();
        BeanUtils.copyProperties(operationGuidance, one);
        MultiLanguageBo name = languages.getOrDefault("1", new MultiLanguageBo());
        one.setNameLangId(name.getLangId());
        one.setNameLangCode(name.getLangCode());
        MultiLanguageBo description = languages.getOrDefault("2", new MultiLanguageBo());
        one.setDescriptionLangId(description.getLangId());
        one.setDescriptionLangCode(description.getLangCode());
        one.setInstanceId(SnowFlake.nextId());
        one.setUrl(UrlUtil.completeUrl(awsProperties.getPictureBucket().getCdnHost(), one.getUploadFileName()));
        this.save(one);
        return one;
    }

    @Override
    public void edit(OperationGuidanceDto operationGuidance) {
        check(operationGuidance);
        if (operationGuidance.getOperationGuidanceId() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_OPERATION_GUIDANCE_ID_NULL);
        }
        OperationGuidance guidance = this.getById(operationGuidance.getOperationGuidanceId());
        if (guidance == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_OPERATION_GUIDANCE_NULL, operationGuidance.getOperationGuidanceId());
        }
        OperationGuidance one = new OperationGuidance();
        BeanUtils.copyProperties(operationGuidance, one);
        one.setId(guidance.getId());
        remoteMultiLanguageService.simpleUpdateMultiLanguage(ApplicationEnum.OPERATION_PLATFORM.getName(), guidance.getNameLangId(), operationGuidance.getName(), LocaleContextHolder.getLocale().getLanguage());
        if (guidance.getDescriptionLangId() != null) {
            remoteMultiLanguageService.simpleUpdateMultiLanguage(ApplicationEnum.OPERATION_PLATFORM.getName(), guidance.getDescriptionLangId(), operationGuidance.getDescription() == null ? "" : operationGuidance.getDescription(), LocaleContextHolder.getLocale().getLanguage());
        } else if (StringUtils.isNotBlank(operationGuidance.getDescription())) {
            MultiLanguageBo bo = remoteMultiLanguageService.simpleCreateMultiLanguage(ApplicationEnum.OPERATION_PLATFORM.getName(), operationGuidance.getDescription(), LocaleContextHolder.getLocale().getLanguage());
            one.setDescriptionLangId(bo.getLangId());
            one.setDescriptionLangCode(bo.getLangCode());
        }
        one.setUrl(UrlUtil.completeUrl(awsProperties.getPictureBucket().getCdnHost(), one.getUploadFileName()));
        this.updateById(one);
    }

    private void check(OperationGuidanceDto operationGuidance) {
        if (StringUtils.isBlank(operationGuidance.getTypeCode()) || !Arrays.asList("1", "2").contains(operationGuidance.getTypeCode())) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_OPERATION_GUIDANCE_TYPE_ERROR, operationGuidance.getTypeCode().length()==0 ? "null" : operationGuidance.getTypeCode());
        }
        if (StringUtils.isBlank(operationGuidance.getName())) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_OPERATION_GUIDANCE_NAME_NULL);
        }
        if (StringUtils.isBlank(operationGuidance.getUploadFileName())) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_OPERATION_GUIDANCE_UPLOAD_FILE_NAME_NULL);
        }
    }
}
