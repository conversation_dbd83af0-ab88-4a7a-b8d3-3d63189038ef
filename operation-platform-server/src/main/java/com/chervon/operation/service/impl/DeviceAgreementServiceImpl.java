package com.chervon.operation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.incrementer.DefaultIdentifierGenerator;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.MultiLanguageVo;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.operation.api.exception.OperationErrorCode;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.config.OperationCommon;
import com.chervon.operation.domain.dataobject.DeviceAgreement;
import com.chervon.operation.domain.dataobject.DeviceAgreementGroup;
import com.chervon.operation.domain.dto.device.agreement.*;
import com.chervon.operation.domain.vo.device.agreement.DeviceAgreementDetailVo;
import com.chervon.operation.domain.vo.device.agreement.DeviceAgreementListVo;
import com.chervon.operation.mapper.DeviceAgreementMapper;
import com.chervon.operation.service.DeviceAgreementGroupService;
import com.chervon.operation.service.DeviceAgreementService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-08-25 11:23
 **/
@Service
@Slf4j
@AllArgsConstructor
public class DeviceAgreementServiceImpl extends ServiceImpl<DeviceAgreementMapper, DeviceAgreement>
        implements DeviceAgreementService {

    private final DeviceAgreementGroupService deviceAgreementGroupService;

    @DubboReference
    private RemoteMultiLanguageService languageService;

    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;

    /**
     * 检查创建新版本版本号是否大于老版本号
     *
     * @param oldVersion 老版本号
     * @param newVersion 新版本号
     */
    public static void checkAgreementVersion(String oldVersion, String newVersion) {
        if (oldVersion.equals(newVersion)) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_DEVICE_AGREEMENT_INVALID_VERSION, oldVersion, newVersion);
        }
        List<Integer> oldVersionList = Arrays.stream(oldVersion.split("\\.")).map(Integer::parseInt)
                .collect(Collectors.toList());
        List<Integer> newVersionList = Arrays.stream(newVersion.split("\\.")).map(Integer::parseInt)
                .collect(Collectors.toList());
        for (int i = 0; i < oldVersionList.size(); i++) {
            if (oldVersionList.get(i) > newVersionList.get(i)) {
                throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_DEVICE_AGREEMENT_INVALID_VERSION3, oldVersion, newVersion);
            }
        }
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(DeviceAgreementAddDto deviceAgreementAddDto) {
        // 文案内容+协议名称
        MultiLanguageBo contentMultiLanguageBo = languageService.simpleCreateMultiLanguage(
                OperationCommon.APPLICATION_NAME, deviceAgreementAddDto.getContent(),
                LocaleContextHolder.getLocale().getLanguage());
        deviceAgreementAddDto.setContent(String.valueOf(contentMultiLanguageBo.getLangId()));
        MultiLanguageBo nameMultiLanguageBo = languageService.simpleCreateMultiLanguage(
                OperationCommon.APPLICATION_NAME, deviceAgreementAddDto.getName(),
                LocaleContextHolder.getLocale().getLanguage());
        deviceAgreementAddDto.setName(nameMultiLanguageBo.getLangId().toString());
        DeviceAgreement target = ConvertUtil.convert(deviceAgreementAddDto, DeviceAgreement.class);
        target.setType(deviceAgreementAddDto.getType());
        target.setId(new DefaultIdentifierGenerator().nextId(target));
        target.setAgreementId(new DefaultIdentifierGenerator().nextId(target));
        target.setName(nameMultiLanguageBo.getLangId());
        target.setContent(contentMultiLanguageBo.getLangId());
        this.save(target);

        List<DeviceAgreementGroup> deviceAgreementGroups = new ArrayList<>();
        if (!CollectionUtils.isEmpty(deviceAgreementAddDto.getGroupNameList())) {
            for (String groupName : deviceAgreementAddDto.getGroupNameList()) {
                DeviceAgreementGroup deviceAgreementGroup = new DeviceAgreementGroup();
                deviceAgreementGroup.setAgreementId(target.getId());
                deviceAgreementGroup.setGroupName(groupName);
                deviceAgreementGroups.add(deviceAgreementGroup);
            }
            deviceAgreementGroupService.saveBatch(deviceAgreementGroups);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addVersion(DeviceAgreementVersionAddDto deviceAgreementVersionAddDto) {
            LambdaQueryWrapper<DeviceAgreement> wrapper = new LambdaQueryWrapper<DeviceAgreement>()
                    .eq(DeviceAgreement::getId, deviceAgreementVersionAddDto.getId())
                    .eq(DeviceAgreement::getProductId, deviceAgreementVersionAddDto.getProductId());
            DeviceAgreement target = this.getOne(wrapper);
            if (null == target) {
                throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_DEVICE_AGREEMENT_NOT_EXIST, deviceAgreementVersionAddDto.getId());
            }
            target.setId(new DefaultIdentifierGenerator().nextId(target));
            // 检查新版本号是否大于老版本号
            checkAgreementVersion(target.getVersion(), deviceAgreementVersionAddDto.getVersion());
            target.setVersion(deviceAgreementVersionAddDto.getVersion());
            // 文案内容多语言
            MultiLanguageBo nameMultiLanguageBo = languageService.simpleCreateMultiLanguage(
                    OperationCommon.APPLICATION_NAME, deviceAgreementVersionAddDto.getContent(),
                    LocaleContextHolder.getLocale().getLanguage());
            target.setContent(nameMultiLanguageBo.getLangId());
            this.save(target);

            deviceAgreementGroupService.saveByGroupNameList(
                    target.getId(), deviceAgreementVersionAddDto.getGroupNameList());
    }

    private List<Long> findNameLangIds(String name) {
        List<Long> res = new ArrayList<>();
        if (StringUtils.isBlank(name)) {
            return res;
        }
        List<DeviceAgreement> list = this.list();
        List<Long> nameLangIds = list.stream().map(DeviceAgreement::getName).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nameLangIds)) {
            return res;
        }
        Map<@NotBlank String, @NotNull List<MultiLanguageBo>> map = remoteMultiLanguageService.listByTextLike(new HashMap<String, List<Long>>() {{
            put(name, nameLangIds);
        }}, LocaleContextHolder.getLocale().getLanguage());
        return map.get(name).stream().map(MultiLanguageBo::getLangId).collect(Collectors.toList());
    }

    @Override
    public PageResult<DeviceAgreementListVo> list(DeviceAgreementListDto deviceAgreementListDto) {
        PageResult<DeviceAgreementListVo> result;
        List<Long> nameLangIds = findNameLangIds(deviceAgreementListDto.getName());
        // 如果没有匹配的多语言id，则返回空
        if (StringUtils.isNotBlank(deviceAgreementListDto.getName()) && CollectionUtils.isEmpty(nameLangIds)) {
            return new PageResult<>(deviceAgreementListDto.getPageNum(), deviceAgreementListDto.getPageSize(), 0);
        }
        LambdaQueryWrapper<DeviceAgreement> deviceAgreementWrapper = new LambdaQueryWrapper<DeviceAgreement>()
                .eq(DeviceAgreement::getProductId, deviceAgreementListDto.getProductId())
                .in(!CollectionUtils.isEmpty(nameLangIds),
                        DeviceAgreement::getName, nameLangIds)
                .eq(null != deviceAgreementListDto.getType(),
                        DeviceAgreement::getType, deviceAgreementListDto.getType())
                .eq(null != deviceAgreementListDto.getVersion(),
                        DeviceAgreement::getVersion, deviceAgreementListDto.getVersion());
        deviceAgreementWrapper.orderByDesc(DeviceAgreement::getCreateTime);
        Page<DeviceAgreement> page = this.getBaseMapper().selectPage(new Page<>(deviceAgreementListDto.getPageNum(),
                deviceAgreementListDto.getPageSize()), deviceAgreementWrapper);
        result = new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<DeviceAgreementListVo> deviceAgreementListVoList = new ArrayList<>();
        for (DeviceAgreement deviceAgreement : page.getRecords()) {
            // 获取多语言
            MultiLanguageBo nameBo = languageService.getById(deviceAgreement.getName().toString());
            MultiLanguageVo nameVo = new MultiLanguageVo(nameBo.getLangId(),
                    com.chervon.operation.config.MultiLanguageUtil.getByLangCode(nameBo.getLangCode(), LocaleContextHolder.getLocale().getLanguage()));
            DeviceAgreementListVo agreementVo = ConvertUtil.convert(deviceAgreement, DeviceAgreementListVo.class);
            if (deviceAgreement.getCreateBy() != null) {
                agreementVo.setCreateBy(deviceAgreement.getCreateBy() + "");
            }
            if (deviceAgreement.getUpdateBy() != null) {
                agreementVo.setUpdateBy(deviceAgreement.getUpdateBy() + "");
            }
            agreementVo.setName(nameVo);
            // 根据is_released字段判断能否编辑所有信息
            agreementVo.setReleased(Objects.equals(deviceAgreement.getIsReleased(), CommonConstant.ONE));
            MultiLanguageVo contentVo = new MultiLanguageVo(deviceAgreement.getContent(), "");
            agreementVo.setContent(contentVo);
            deviceAgreementListVoList.add(agreementVo);
        }
        result.setList(deviceAgreementListVoList);
        return result;
    }

    @Override
    public DeviceAgreementDetailVo detail(DeviceAgreementIdDto deviceAgreementIdDto) {
        LambdaQueryWrapper<DeviceAgreement> deviceAgreementWrapper = new LambdaQueryWrapper<DeviceAgreement>()
                .eq(DeviceAgreement::getId, deviceAgreementIdDto.getId());
        DeviceAgreement deviceAgreement = this.getOne(deviceAgreementWrapper);
        if (null == deviceAgreement) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_DEVICE_AGREEMENT_NOT_EXIST, deviceAgreementIdDto.getId());
        }
        // 获取多语言协议名称,协议文案
        MultiLanguageBo nameBo = languageService.getById(deviceAgreement.getName().toString());
        MultiLanguageVo nameVo = new MultiLanguageVo(nameBo.getLangId(),
                com.chervon.operation.config.MultiLanguageUtil.getByLangCode(nameBo.getLangCode(), LocaleContextHolder.getLocale().getLanguage()));
        MultiLanguageVo contentVo = new MultiLanguageVo();
        if (deviceAgreement.getContent() != null) {
            MultiLanguageBo contentBo = languageService.getById(deviceAgreement.getContent().toString());
            contentVo = new MultiLanguageVo(contentBo.getLangId(),
                    com.chervon.operation.config.MultiLanguageUtil.getByLangCode(contentBo.getLangCode(), LocaleContextHolder.getLocale().getLanguage()));
        }
        DeviceAgreementDetailVo result = ConvertUtil.convert(deviceAgreement, DeviceAgreementDetailVo.class);
        result.setName(nameVo);
        result.setContent(contentVo);
        // 获取判断条件(绑定分组)
        LambdaQueryWrapper<DeviceAgreementGroup> groupWrapper = new LambdaQueryWrapper<DeviceAgreementGroup>()
                .eq(DeviceAgreementGroup::getAgreementId, deviceAgreementIdDto.getId());
        List<DeviceAgreementGroup> deviceAgreementGroups = deviceAgreementGroupService.list(groupWrapper);
        List<String> groupNameList = deviceAgreementGroups.stream().map(DeviceAgreementGroup::getGroupName)
                .collect(Collectors.toList());
        result.setGroupName(groupNameList);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(DeviceAgreementEditDto deviceAgreementEditDto) {

        // 文案内容多语言更新
        if (deviceAgreementEditDto.getContent() != null &&
                deviceAgreementEditDto.getContent().getLangId() != null &&
                StringUtils.isNotEmpty(deviceAgreementEditDto.getContent().getMessage())) {
            languageService.simpleUpdateMultiLanguage(OperationCommon.APPLICATION_NAME,
                    deviceAgreementEditDto.getContent().getLangId(),
                    deviceAgreementEditDto.getContent().getMessage(),
                    LocaleContextHolder.getLocale().getLanguage());
        }
        languageService.simpleUpdateMultiLanguage(OperationCommon.APPLICATION_NAME,
                deviceAgreementEditDto.getName().getLangId(),
                deviceAgreementEditDto.getName().getMessage(),
                LocaleContextHolder.getLocale().getLanguage());
        DeviceAgreement target = ConvertUtil.convert(deviceAgreementEditDto, DeviceAgreement.class);
        // 判断版本号是否已存在
        LambdaQueryWrapper<DeviceAgreement> versionWrapper = new LambdaQueryWrapper<DeviceAgreement>()
                .eq(DeviceAgreement::getId, deviceAgreementEditDto.getId());
        DeviceAgreement deviceAgreement = this.getOne(versionWrapper);
        if (null == deviceAgreement) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_DEVICE_AGREEMENT_NOT_EXIST, deviceAgreementEditDto.getId());
        }
        versionWrapper.eq(DeviceAgreement::getVersion, deviceAgreementEditDto.getVersion());
        if (this.count(versionWrapper) > 0 &&
                !Objects.equals(deviceAgreementEditDto.getVersion(), deviceAgreement.getVersion())) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_DEVICE_AGREEMENT_INVALID_VERSION2, deviceAgreementEditDto.getId(), deviceAgreementEditDto.getVersion());
        }
        this.updateById(target);

        deviceAgreementGroupService.saveByGroupNameList(target.getId(), deviceAgreementEditDto.getGroupNameList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(DeviceAgreementIdDto deviceAgreementIdDto) {
        List<Long> idList = new ArrayList<>();

        DeviceAgreement deviceAgreement = this.getById(deviceAgreementIdDto.getId());
        if (null == deviceAgreement) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_DEVICE_AGREEMENT_NOT_EXIST, deviceAgreementIdDto.getId());
        }

        if (deviceAgreement.getName() != null) {
            idList.add(deviceAgreement.getName());
        }
        if (deviceAgreement.getContent() != null) {
            idList.add(deviceAgreement.getContent());
        }
        this.removeById(deviceAgreement);
        LambdaQueryWrapper<DeviceAgreementGroup> deviceAgreementGroupWrapper = new LambdaQueryWrapper<>();
        deviceAgreementGroupWrapper.eq(DeviceAgreementGroup::getAgreementId, deviceAgreement.getAgreementId());
        deviceAgreementGroupService.remove(deviceAgreementGroupWrapper);
        if (idList.size() > 0) {
            // 清理多语言
            remoteMultiLanguageService.deleteByLangIds(idList);
        }
    }

    @Override
    public List<Long> listDeviceAgreementIdByGroupName(String groupName) {
        LambdaQueryWrapper<DeviceAgreementGroup> wrapper = new LambdaQueryWrapper<DeviceAgreementGroup>()
                .eq(DeviceAgreementGroup::getGroupName, groupName)
                .select(DeviceAgreementGroup::getAgreementId);
        return deviceAgreementGroupService.list(wrapper)
                .stream().map(DeviceAgreementGroup::getAgreementId).collect(Collectors.toList());
    }
}
