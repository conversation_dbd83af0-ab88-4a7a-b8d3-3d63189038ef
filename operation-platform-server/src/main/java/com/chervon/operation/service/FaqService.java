package com.chervon.operation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.operation.domain.dataobject.Faq;
import com.chervon.operation.domain.dto.faq.FaqDto;
import com.chervon.operation.domain.dto.faq.FaqRead;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/19 15:31
 */
public interface FaqService extends IService<Faq> {

    /**
     * 新增faq
     *
     * @param faq 对象
     * @return faq
     */
    Faq add(FaqDto faq);

    /**
     * 修改faq
     *
     * @param faq 对象
     */
    void edit(FaqDto faq);

    /**
     * faq导入
     *
     * @param data 导入数据
     * @return create新增数据、update修改数据、error错误信息
     */
    Map<String, List<String>> importFaq(List<FaqRead> data);

}
