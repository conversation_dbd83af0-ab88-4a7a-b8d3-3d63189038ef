package com.chervon.operation.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.operation.domain.dataobject.GroupCondition;
import com.chervon.operation.domain.dataobject.GroupConditionValue;
import com.chervon.operation.mapper.GroupConditionMapper;
import com.chervon.operation.mapper.GroupConditionValueMapper;
import com.chervon.operation.service.GroupConditionService;
import com.chervon.operation.service.GroupConditionValueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 分组条件值表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
@Service
public class GroupConditionValueServiceImpl extends
    ServiceImpl<GroupConditionValueMapper, GroupConditionValue> implements
        GroupConditionValueService {
    @Autowired
    private GroupConditionValueMapper groupConditionValueMapper;

    @Override
    public List<String> listValueByConditionId(Long groupConditionId) {
        return groupConditionValueMapper.listValueByConditionId(groupConditionId);
    }
}
