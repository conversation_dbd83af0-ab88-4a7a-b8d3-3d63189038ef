package com.chervon.operation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.operation.domain.dataobject.PostSale;
import com.chervon.operation.domain.dto.faq.product.ProductFaqDto;
import com.chervon.operation.domain.dto.faq.product.ProductFaqOrderDto;
import com.chervon.operation.domain.dto.manual.product.ProductManualDto;
import com.chervon.operation.domain.dto.operationguidance.product.ProductOperationGuidanceDto;
import com.chervon.operation.domain.dto.operationguidance.product.ProductOperationGuidanceOrderDto;
import com.chervon.operation.domain.dto.postsale.PostSaleBaseDto;
import com.chervon.operation.domain.dto.postsale.PostSaleEditDto;
import com.chervon.operation.domain.dto.postsale.PostSaleFaqListDto;
import com.chervon.operation.domain.vo.faq.product.ProductFaqVo;
import com.chervon.operation.domain.vo.manual.product.ProductManualVo;
import com.chervon.operation.domain.vo.operationguidance.product.ProductOperationGuidanceVo;
import com.chervon.operation.domain.vo.postsale.PostSaleVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/19 15:33
 */
public interface PostSaleService extends IService<PostSale> {

    /**
     * 产品短描述
     */
    String SHORT_DESCRIPTION = "shortDescription";


    /**
     * 产品长描述
     */
    String LONG_DESCRIPTION = "longDescription";


    /**
     * 产品技术规格
     */
    String TECHNICAL_SPECIFICATION = "technicalSpecification";

    /**
     * 详情-传productId
     *
     * @param productId 产品id
     * @return 售后内容
     */
    PostSaleVo detail(Long productId);

    /**
     * 编辑
     *
     * @param req 编辑对象
     */
    void edit(PostSaleEditDto req);

    /**
     * 编辑用户手册
     *
     * @param req 操作对象
     */
    void manualEdit(ProductManualDto req);

    /**
     * 删除用户手册
     *
     * @param req 操作对象
     */
    void manualDelete(PostSaleBaseDto req);

    /**
     * 下载用户手册
     *
     * @param req 操作对象
     * @return url
     */
    String manualDownload(PostSaleBaseDto req);

    /**
     * 添加用户手册
     *
     * @param req 操作对象
     */
    void manualAdd(ProductManualDto req);

    /**
     * 详情用户手册
     *
     * @param req 操作对象
     * @return 详情
     */
    ProductManualVo manualDetail(PostSaleBaseDto req);

    /**
     * 列表用户手册-用于新增、编辑、删除用户手册后，刷新列表-传productId
     *
     * @param productId 产品id
     * @return 列表数据
     */
    List<ProductManualVo> manualList(Long productId);

    /**
     * 编辑操作指导
     *
     * @param req 操作对象
     */
    void operationGuidanceEdit(ProductOperationGuidanceDto req);

    /**
     * 删除操作指导
     *
     * @param req 操作对象
     */
    void operationGuidanceDelete(PostSaleBaseDto req);

    /**
     * 下载操作指导
     *
     * @param req 操作对象
     * @return url
     */
    String operationGuidanceDownload(PostSaleBaseDto req);

    /**
     * 添加操作指导
     *
     * @param req 操作对象
     */
    void operationGuidanceAdd(ProductOperationGuidanceDto req);

    /**
     * 列表操作指导-用于新增、编辑、删除、排序操作指导后，刷新列表-传productId
     *
     * @param productId 产品id
     * @return 列表数据
     */
    List<ProductOperationGuidanceVo> operationGuidanceList(Long productId);

    /**
     * 详情操作指导
     *
     * @param req 操作对象
     * @return 详情
     */
    ProductOperationGuidanceVo operationGuidanceDetail(PostSaleBaseDto req);

    /**
     * 排序操作指导
     *
     * @param req 操作对象
     */
    void operationGuidanceOrder(ProductOperationGuidanceOrderDto req);

    /**
     * 编辑faq
     *
     * @param req 操作对象
     */
    void faqEdit(ProductFaqDto req);

    /**
     * 删除faq
     *
     * @param req 操作对象
     */
    void faqDelete(PostSaleBaseDto req);

    /**
     * 添加faq
     *
     * @param req 操作对象
     */
    void faqAdd(ProductFaqDto req);

    /**
     * 列表faq-用于新增、编辑、删除、排序faq后，刷新列表
     *
     * @param req faq列表dto
     * @return 列表数据
     */
    List<ProductFaqVo> faqList(PostSaleFaqListDto req);

    /**
     * 详情faq
     *
     * @param req 操作对象
     * @return 详情
     */
    ProductFaqVo faqDetail(PostSaleBaseDto req);

    /**
     * 排序faq
     *
     * @param req 操作对象
     */
    void faqOrder(ProductFaqOrderDto req);

}
