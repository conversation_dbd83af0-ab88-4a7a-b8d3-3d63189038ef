package com.chervon.operation.service.impl;

import com.chervon.common.i18n.util.MessageTools;
import com.chervon.operation.domain.bo.DictBo;
import com.chervon.operation.domain.bo.DictNodeBo;
import com.chervon.operation.service.DictService;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/17 11:39
 * @desc 描述
 */
@Service
@Slf4j
public class DictServiceImpl implements DictService {
    @Resource
    MessageTools messageTools;

    @Override
    public List<DictBo> listByDictName(String lang, List<String> dictNames) {
        List<DictBo> dictBoList = new ArrayList<>();
        if (!dictNames.isEmpty()) {
            // 获取字典值
            for (String dictName : dictNames) {
                DictBo dictVo = new DictBo();
                dictVo.setDictName(dictName);
                String value = messageTools.getCodeValue(dictName, lang);
                Gson gson = new Gson();
                Type dictNodeType = new TypeToken<List<DictNodeBo>>(){}.getType();
                List<DictNodeBo> dictValueList = gson.fromJson(value, dictNodeType);
                dictVo.setNodes(dictValueList);
                dictBoList.add(dictVo);
            }
        }
        return dictBoList;
    }
}
