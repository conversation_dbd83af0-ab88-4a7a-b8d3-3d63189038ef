package com.chervon.operation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.operation.domain.dataobject.GroupCondition;
import java.util.List;

/**
 * <p>
 * 设备表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
public interface GroupConditionService extends IService<GroupCondition> {

    /**
     * 根据分组id获取条件列表
     * <AUTHOR>
     * @date 18:22 2022/7/28
     * @param groupId:
     * @return java.util.List<com.chervon.technology.domain.entity.GroupCondition>
     **/
    List<GroupCondition> listByGroupId(Long groupId);

    /**
     * 根据分组名称获取条件列表
     * <AUTHOR>
     * @date 11:37 2022/8/31
     * @param groupName: 
     * @return java.util.List<com.chervon.operation.domain.dataobject.GroupCondition>
     **/
    List<GroupCondition> listByGroupName(String groupName);

    /**
     * 根据分组名称列表获取条件列表
     * @param groupNames:
     * @return java.util.List<com.chervon.operation.domain.dataobject.GroupCondition>
     **/
    List<GroupCondition> listByGroupNames(List<String> groupNames);
}
