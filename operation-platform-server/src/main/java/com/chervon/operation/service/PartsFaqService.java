package com.chervon.operation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.operation.domain.dataobject.PartsFaq;
import com.chervon.operation.domain.dto.faq.parts.PartsFaqDto;
import com.chervon.operation.domain.dto.faq.parts.PartsFaqListDto;
import com.chervon.operation.domain.vo.faq.parts.PartsFaqVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-08-10
 */
public interface PartsFaqService extends IService<PartsFaq> {

    /**
     * 根据配件id获取faq列表
     *
     * @param partsId 配件id
     * @return faq列表
     */
    List<PartsFaqVo> listByPartsId(Long partsId);

    /**
     * 编辑
     *
     * @param req 修改对象
     */
    void edit(PartsFaqDto req);

    /**
     * 删除
     *
     * @param partsFaqId 配件下Faq id
     */
    void delete(Long partsFaqId);

    /**
     * 新增
     *
     * @param req 新增对象
     */
    void add(PartsFaqDto req);

    /**
     * 详情
     *
     * @param partsFaqId 配件下Faq id
     * @return 详情
     */
    PartsFaqVo detail(Long partsFaqId);

    /**
     * 排序
     *
     * @param partsFaqIds id集合
     */
    void order(List<Long> partsFaqIds);

    /**
     * 根据条件查询列表
     *
     * @param req 查询条件
     * @return 列表
     */
    List<PartsFaqVo> search(PartsFaqListDto req);
}
