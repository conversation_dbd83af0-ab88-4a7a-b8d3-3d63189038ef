package com.chervon.operation.service;

import com.chervon.common.core.domain.PageResult;
import com.chervon.fleet.web.api.entity.dto.FleetCompanyDevicePageDto;
import com.chervon.fleet.web.api.entity.vo.FleetCompanyDeviceVo;
import com.chervon.operation.domain.vo.FleetCompanyDeviceExcel;

import java.util.List;

public interface FleetCompanyDeviceService {

    /**
     * 租户设备关系分页
     *
     * @param req 查询条件
     * @return 分页数据
     */
    PageResult<FleetCompanyDeviceVo> companyDevicePage(FleetCompanyDevicePageDto req);

    /**
     * 租户设备关系列表
     *
     * @param req 查询条件
     * @return 列表数据
     */
    List<FleetCompanyDeviceExcel> companyDeviceListData(FleetCompanyDevicePageDto req);
}
