<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chervon.operation.mapper.CategoryMapper">

    <select id="getAllCategoryIds" resultType="java.lang.Long">
        select id
        from category
        where is_deleted = 0
    </select>
    <select id="getAppShowMaxOrder" resultType="java.lang.Integer">
        select ifnull(max(app_show_order),0)
        from category
        where is_deleted = 0
    </select>


</mapper>