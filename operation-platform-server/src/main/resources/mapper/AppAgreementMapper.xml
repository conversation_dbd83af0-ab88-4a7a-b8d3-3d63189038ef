<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chervon.operation.mapper.AppAgreementMapper">
    <select id="selectManagePage" resultType="com.chervon.operation.domain.vo.app.AppAgreementManagePageVo">
        select
        t1.id as appAgreementContentId,
        t2.id as appAgreementId,
        t2.title_lang_code as title,
        t1.version as version,
        t2.type_code as typeCode,
        t1.status_code as statusCode,
        t1.create_by as createBy,
        t1.update_by as updateBy,
        t1.create_time as createTime,
        t1.update_time as updateTime,
        t2.business_type as businessType
        from app_agreement_content as t1 left join app_agreement as t2
        on t1.app_agreement_id = t2.id
        where t1.is_deleted = 0 and t2.is_deleted = 0
        <if test="search.statusCode != null and search.statusCode != ''">
            and t1.status_code = #{search.statusCode}
        </if>
        <if test="search.version != null and search.version != ''">
            and t1.version like concat('%', #{search.version}, '%')
        </if>
        <if test="search.typeCode != null and search.typeCode != ''">
            and t2.type_code = #{search.typeCode}
        </if>
        <if test="titleLangIds != null and titleLangIds.size() > 0">
            and t2.title_lang_id in
            <foreach collection="titleLangIds" item="titleLangId" open="(" separator="," close=")">
                #{titleLangId}
            </foreach>
        </if>
        <if test="search.createStartTime != null and search.createStartTime != ''">
            and t1.create_time &gt;= #{search.createStartTime}
        </if>
        <if test="search.createEndTime != null and search.createEndTime != ''">
            and t1.create_time &lt;= #{search.createEndTime}
        </if>
        <if test="search.updateStartTime != null and search.updateStartTime != ''">
            and t1.update_time &gt;= #{search.updateStartTime}
        </if>
        <if test="search.updateEndTime != null and search.updateEndTime != ''">
            and t1.update_time &lt;= #{search.updateEndTime}
        </if>
        <if test="search.businessType != null and search.businessType != 0">
            and t2.business_type = #{search.businessType}
        </if>
        order by t1.create_time desc
    </select>

    <select id="selectReleasePage" resultType="com.chervon.operation.domain.vo.app.AppAgreementReleasePageVo">
        select
        t1.id as appAgreementContentId,
        t2.id as appAgreementId,
        t2.title_lang_code as title,
        t1.version as version,
        t2.type_code as typeCode,
        t1.status_code as statusCode,
        t1.apply_by as applyBy,
        t1.approved_by as approvedBy,
        t1.apply_time as applyTime,
        t1.approved_time as approvedTime
        from app_agreement_content as t1
        join app_agreement as t2 on t1.app_agreement_id = t2.id
        where t1.is_deleted = 0 and t2.is_deleted = 0 and t1.status_code != 'will_release'
        <if test="search.statusCode != null and search.statusCode != ''">
            and t1.status_code = #{search.statusCode}
        </if>
        <if test="search.version != null and search.version != ''">
            and t1.version like concat('%', #{search.version}, '%')
        </if>
        <if test="search.typeCode != null and search.typeCode != ''">
            and t2.type_code = #{search.typeCode}
        </if>
        <if test="titleLangIds != null and titleLangIds.size() > 0">
            and t2.title_lang_id in
            <foreach collection="titleLangIds" item="titleLangId" open="(" separator="," close=")">
                #{titleLangId}
            </foreach>
        </if>
        <!--申请时间，审批时间筛选-->
        <if test="search.applyBy != null and search.applyBy != ''">
            and t1.apply_by like concat('%', #{search.applyBy}, '%')
        </if>
        <if test="search.approveBy != null and search.approveBy != '' ">
            and t1.approved_by like concat('%', #{search.approveBy}, '%')
        </if>
        <!--申请时间，审批时间筛选-->
        <if test="search.applyStartTime != null and search.applyStartTime != ''">
            and t1.apply_time &gt;= #{search.applyStartTime}
        </if>
        <if test="search.applyEndTime != null and search.applyEndTime != ''">
            and t1.apply_time &lt;= #{search.applyEndTime}
        </if>
        <if test="search.approveStartTime != null and search.approveStartTime != ''">
            and t1.approved_time &gt;= #{search.approveStartTime}
        </if>
        <if test="search.approveEndTime != null and search.approveEndTime != ''">
            and t1.approved_time &lt;= #{search.approveEndTime}
        </if>
        order by t1.create_time desc
    </select>

    <select id="selectMaxVersionAgreement" resultType="com.chervon.operation.api.vo.AppAgreementVo">
        select t2.type_code         as type,
               t1.version           as version,
               t2.title_lang_code   as title,
               t1.approved_time       as updateTime,
               t1.content_lang_code as content
        from app_agreement_content as t1
                 join app_agreement as t2 on t1.app_agreement_id = t2.id
        where t1.is_deleted = 0
          and t2.is_deleted = 0
          and t2.id = #{agreementId}
          and t2.type_code = #{typeCode}
          and t1.customer_status_code = 'released'
        order by INET_ATON(CONCAT(t1.version, '.0')) desc limit 1
    </select>

    <select id="selectListByAgreementIds" resultType="com.chervon.operation.api.vo.AppAgreementVo">
        select t2.type_code as type,
        t1.version as version
        from app_agreement_content as t1
        join app_agreement as t2 on t1.app_agreement_id = t2.id
        where t1.is_deleted = 0 and t2.is_deleted = 0
        and t2.id in
        <foreach collection="agreementIds" item="agreementId" open="(" separator="," close=")">
            #{agreementId}
        </foreach>
        and t1.customer_status_code = 'released'
    </select>

    <select id="selectOneByVersion" resultType="com.chervon.operation.api.vo.AppAgreementVo">
        select t2.type_code         as type,
               t1.version           as version,
               t2.title_lang_code   as title,
               t1.approved_time       as updateTime,
               t1.content_lang_code as content
        from app_agreement_content as t1
                 join app_agreement as t2 on t1.app_agreement_id = t2.id
        where t1.is_deleted = 0
          and t2.is_deleted = 0
          and t2.id = #{agreementId}
          and t2.type_code = #{typeCode}
          and t1.version = #{version}
          and t1.customer_status_code = 'released'
    </select>

    <select id="selectListIdByGroupName" resultType="long">
        SELECT app_agreement_id
        FROM app_agreement_content
        WHERE is_deleted = 0
          AND (
            CONCAT(',', prd_group, ',') REGEXP CONCAT( ',', #{groupName}, ',')
                   OR CONCAT(',', test_group, ',') REGEXP CONCAT(',', #{groupName}, ',')
            );
    </select>

</mapper>