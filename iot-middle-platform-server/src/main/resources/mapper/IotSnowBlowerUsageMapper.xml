<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chervon.iot.middle.mapper.IotSnowBlowerUsageMapper">
    <select id="getLastUsageTs" resultType="java.lang.String">
        select idf_1033
        from sub_product_${productKey}_${deviceId}
        where  idf_1032>0 and idf_1033>0  and idf_1033-idf_1032>=30 order by ts desc limit 1
    </select>

    <select id="selectLastUsageData" resultType="com.chervon.iot.middle.domain.pojo.SnowBlowerUsage">
        select ts,device_id,idf_1032 working_time_start,idf_1033 working_time_end,idf_1034 power_consumed
        from sub_product_${productKey}_${query.deviceId}
        where  idf_1032>0 and idf_1033>0
        and idf_1032 <![CDATA[>]]> #{query.timeStart}
        and idf_1033 <![CDATA[<=]]> #{query.timeEnd} order by ts
    </select>

    <select id="selectWorkLoadTimeSpan" resultType="com.chervon.iot.middle.domain.pojo.SnowBlowerUsage">
        select idf_4004 work_load
        from sub_product_${productKey}_${query.deviceId}
        where idf_4004 is not null
          and idf_load_curve_start_time <![CDATA[>=]]> #{query.timeStart}
          and idf_load_curve_start_time <![CDATA[<=]]> #{query.timeEnd}
    </select>

    <select id="selectAvgPowerConsumed" resultType="java.lang.Double">
        select  AVG(cast(idf_1034 as int))
        from sub_product_${productKey}_${query.deviceId}
        where  idf_1034>0
          and ts <![CDATA[>=]]> #{query.timeStart}
          and ts <![CDATA[<=]]> #{query.timeEnd}
          and idf_1033-idf_1032>=30
    </select>

</mapper>
