package com.chervon.iot.middle.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageRequest;
import com.chervon.common.core.domain.PageResult;
import com.chervon.iot.middle.api.dto.model.EventDto;
import com.chervon.iot.middle.api.dto.model.PropertyDto;
import com.chervon.iot.middle.api.dto.model.ServiceDto;
import com.chervon.iot.middle.api.pojo.thingmodel.Event;
import com.chervon.iot.middle.api.pojo.thingmodel.IotThingModel;
import com.chervon.iot.middle.api.pojo.thingmodel.Property;
import com.chervon.iot.middle.api.pojo.thingmodel.Service;
import com.chervon.iot.middle.api.vo.product.IotThingModelDto;
import com.chervon.iot.middle.domain.pojo.IotThingModelIdentifier;
import com.chervon.iot.middle.domain.pojo.IotThingModelRule;
import java.util.List;

/**
 * 物模型规则服务接口
 *
 * <AUTHOR>
 * @since 2024-05-14 17:01:18
 * @description 物模型规则服务接口
 */
public interface IotThingModelRuleService  extends IService<IotThingModelRule> {
    /**
     * 根据产品key获取物模型规则对象（status参数不可空）
     * @param productKey
     * @param status
     * @return
     */
    IotThingModelRule getIotThingRule(String productKey,String status);
    /**
     * 根据产品key获取物模型规则列表（status参数可空）
     * @param productKey 产品key
     * @return List<IotThingModelRule>
     */
    List<IotThingModelRule> getIotThingRuleList(String productKey,String status);

    /**
     * 获取首个规则进行编辑
     * @param productKey 产品key
     * @return
     */
    IotThingModelRule getFirstIotThingModelRule(String productKey);
    /**
     * 获取单个标识符
     * @param ruleId
     * @param modelType
     * @param identifier
     * @return
     */
    IotThingModelIdentifier getIdentifier(Long ruleId, String modelType, String identifier);

    /**
     * 获取产品全属性物模型值
     * @param productKey 产品key
     * @param status 状态
     * @return
     */
    IotThingModel getProductThingModelAll(String productKey, String status);

    /**
     * 保存物模型整体规则信息
     * @param requestDto
     * @return
     */
    int saveProductThingModelAll(IotThingModelRule requestDto);

    /**
     * 判断是否存在某个物模型id
     * @param ruleId
     * @param identifier
     * @return
     */
    boolean existIdentifierId(Long ruleId,String identifier);

    /**
     * 判断是否存在某个物模型名称
     * @param ruleId
     * @param name
     * @return
     */
    boolean existIdentifierName(Long ruleId,String name);

    /**
     * 获取标识符列表
     * @param ruleId
     * @param modelType
     * @return
     */
    List<IotThingModelIdentifier> getIdentifierList(Long ruleId,String modelType);
    /**
     * 添加物模型属性
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param propertyDto:
     * @return: java.lang.Boolean
     **/
    Boolean addProperty(PropertyDto propertyDto);

    /**
     * 获取物模型属性
     * @param productKey 产品ID
     * @param identifier 物模型id
     * @param isPublished 是否已发布
     * @return 属性信息
     */
    Property getProperty(String productKey, String identifier, boolean isPublished);
    /**
     * 编辑物模型属性
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param propertyDto:
     * @return: java.lang.Boolean
     **/
    Boolean editProperty(PropertyDto propertyDto);

    /**
     * 删除物模型属性
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param productKey: 产品标识
     * @param identifier: 物模型属性id
     * @return: java.lang.Boolean
     **/
    Boolean deleteProperty(String productKey, String identifier);

    /**
     * 分页获取功能属性列表
     * <AUTHOR>
     * @date 17:47 2022/7/12
     * @param pageRequest:
     * @param productKey:
     * @param published:
     * @return com.chervon.iot.middle.api.vo.page.PageResult<com.chervon.iot.middle.api.vo.product.PropertyVo>
     **/
    PageResult<Property> pageProperties(PageRequest pageRequest,
                                        String productKey, Boolean published);

    /**
     * 属性列表
     * @param productKey
     * @param published
     * @return
     */
    List<Property> listProperties(String productKey, Boolean published);


    /**
     * 添加物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param serviceDto:
     * @return: java.lang.Boolean
     **/
    Boolean addService(ServiceDto serviceDto);

    /**
     * 添加物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param productKey: 产品标识
     * @param identifier: 物模型服务id
     * @param isPublished: 是否已发布版本
     * @return: java.lang.Boolean
     **/
    Service getService(String productKey, String identifier, boolean isPublished);

    /**
     * 编辑物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param serviceDto:
     * @return: java.lang.Boolean
     **/
    Boolean editService(ServiceDto serviceDto);

    /**
     * 删除物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param productKey: 产品标识
     * @param identifier: 物模型服务id
     * @return: java.lang.Boolean
     **/
    Boolean deleteService(String productKey, String identifier);

    /**
     * 分页获取功能事件列表
     * <AUTHOR>
     * @date 17:47 2022/7/12
     * @param pageRequest:
     * @param productKey:
     * @param published:
     * @return com.chervon.iot.middle.api.vo.page.PageResult<com.chervon.iot.middle.api.vo.product.ServiceVo>
     **/
    PageResult<Service> pageServices(PageRequest pageRequest, String productKey,
                                     Boolean published);

    /**
     * 功能事件列表
     * @param productKey:
     * @param published:
     * @return com.chervon.iot.middle.api.vo.page.PageResult<com.chervon.iot.middle.api.vo.product.ServiceVo>
     */
    List<Service> listServices(String productKey,Boolean published);
    /**
     * 添加物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param serviceDto:
     * @return: java.lang.Boolean
     **/
    Boolean addEvent(EventDto serviceDto);

    /**
     * 添加物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param productKey: 产品标识
     * @param identifier: 物模型服务id
     * @param isPublished: 是否已发布版本
     * @return: java.lang.Boolean
     **/
    Event getEvent(String productKey, String identifier, boolean isPublished);

    Event getSimpleEvent(String productKey, String identifier, boolean isPublished);
    /**
     * 编辑物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param serviceDto:
     * @return: java.lang.Boolean
     **/
    Boolean editEvent(EventDto serviceDto);

    /**
     * 删除物模型服务
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param productKey: 产品标识
     * @param identifier: 物模型服务id
     * @return: java.lang.Boolean
     **/
    Boolean deleteEvent(String productKey, String identifier);

    /**
     * 分页获取功能事件列表
     * <AUTHOR>
     * @date 17:47 2022/7/12
     * @param pageRequest:
     * @param productKey:
     * @param published:
     * @return com.chervon.iot.middle.api.vo.page.PageResult<com.chervon.iot.middle.api.vo.product.EventVo>
     **/
    PageResult<Event> pageEvents(PageRequest pageRequest, String productKey,
                                 Boolean published);

    /**
     * 功能事件列表
     * @param productKey
     * @param published
     * @return
     */
    List<Event> listEvents(String productKey, Boolean published);

    /**
     * 批量添加物模型属性（导入）
     * <AUTHOR>
     * @date 13:57 2022/2/25
     * @param iotThingModelDto:
     * @return: java.lang.Boolean
     **/
    Boolean addThingModel(IotThingModelDto iotThingModelDto);

    /**
     * 查看物模型（导出物模型）
     * <AUTHOR>
     * @date 10:28 2022/5/10
     * @param productKey: 产品标识
     * @param published: 是否发布，true获取已发布版本，false获取未发布版本，null优先获取未发布版本
     * @return: com.chervon.iot.api.vo.product.IotThingModel
     **/
    IotThingModelRule getThingModel(String productKey, Boolean published);

    /**
     * 更新物模型状态，需要根据产品状态做同步
     * <AUTHOR>
     * @date 11:49 2022/7/15
     * @param productKey: 产品标识
     * @param published: 发布状态 true发布 false开发中
     * @return void
     **/
    void updateThingModelStatus(String productKey, boolean published);
}
