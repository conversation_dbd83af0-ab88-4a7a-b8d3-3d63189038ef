package com.chervon.iot.middle.rpc;

import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.iot.middle.api.dto.query.TimeQueryDto;
import com.chervon.iot.middle.api.dto.query.UsageShadowQuery;
import com.chervon.iot.middle.api.enums.ZTRShadowStatusEnum;
import com.chervon.iot.middle.api.service.RemoteIotDataService;
import com.chervon.iot.middle.api.service.RemoteIotZTRUsageService;
import com.chervon.iot.middle.api.vo.usage.CoordinateHistoryVo;
import com.chervon.iot.middle.domain.pojo.CoordinateHistory;
import com.chervon.iot.middle.domain.pojo.CropperUsage;
import com.chervon.iot.middle.api.vo.usage.TracksHistoryVo;
import com.chervon.iot.middle.api.pojo.usage.UsageKeyValue;
import com.chervon.iot.middle.mapper.IotZTRUsageMapper;
import com.chervon.iot.middle.service.AwsIotService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 61001设备使用数据服务
 * <AUTHOR>
 * @date 2023/4/23 15:41
 */
@Service
@Slf4j
@DubboService
public class RemoteIotZTRUsageServiceImpl implements RemoteIotZTRUsageService {
    private static final String TOTAL_WORK_TIME_FIELD = "idf_1016";
    private static final String TOTAL_CUTTING_AREA_FIELD = "idf_1014";
    private static final String AVERAGE_SPEED_FIELD = "idf_5006";
    @Autowired
    private IotZTRUsageMapper iotZTRUsageMapper;
    @Autowired
    private AwsIotService awsIotService;
    @Autowired
    private RemoteIotDataService remoteIotDataService;

    /**
     * 获取61001割草机设备使用数据
     * @param query 查询条件
     * @return TracksHistoryVo
     */
    public TracksHistoryVo getTracksHistoryVo(UsageShadowQuery query){
        TracksHistoryVo tracksHistoryVo;
        try {
            String productKey = awsIotService.getProductKey(query.getDeviceId());
            final CropperUsage dayUsageData = iotZTRUsageMapper.getDayUsageData(productKey, query);
            if(!Objects.isNull(dayUsageData)) {
                tracksHistoryVo = BeanCopyUtils.copy(dayUsageData, TracksHistoryVo.class);
            }else{
                tracksHistoryVo = new TracksHistoryVo();
                tracksHistoryVo.setDeviceId(query.getDeviceId());
            }
            //查询61001割草机历史坐标和状态：1024，1036，5004
            final List<CoordinateHistory> coordinateHistory = iotZTRUsageMapper.getCoordinateHistory(productKey, query);
            if(!CollectionUtils.isEmpty(coordinateHistory)){
                final List<CoordinateHistoryVo> coordinateVos = coordinateHistory.stream().map(a -> new CoordinateHistoryVo(a.getTs1024(), a.getStatus(), ZTRShadowStatusEnum.getDesc(a.getStatus()), a.getCoordinate()))
                        .collect(Collectors.toList());
                tracksHistoryVo.setListCoordinateHistory(coordinateVos);
            }
            // 查询当天最后一次上报的平均速度
            Long averageSpeed = getLastValueByTimeSpan(query.getDeviceId(), AVERAGE_SPEED_FIELD, query.getTimeStart(), query.getTimeEnd());
            tracksHistoryVo.setAverageSpeed(Math.toIntExact(averageSpeed));
        } catch (Exception e) {
            log.error("getTracksHistory data error，deviceId:{},query:{},{}", query.getDeviceId(), query, e);
            return null;
        }
        return tracksHistoryVo;
    }

    /**
     * 获取61001割草机设备工作时间历史
     * @param query 查询条件
     * @return
     */
    public List<UsageKeyValue> getWorkingTimeHistory(UsageShadowQuery query) {
        String productKey = awsIotService.getProductKey(query.getDeviceId());
        final List<UsageKeyValue> dayWorkTimeHistory;
        try {
            dayWorkTimeHistory = iotZTRUsageMapper.getDayWorkTimeHistory(productKey, query);
        } catch (Exception e) {
            log.error("getDayWorkTimeHistory data error，query:{},{}", query, e);
            return new ArrayList<>();
        }
        return dayWorkTimeHistory;
    }

    /**
     * 获取61001割草机设备割草面积历史
     * @param query 查询条件
     * @return
     */
    public List<UsageKeyValue> getDayCuttingAreaHistory(UsageShadowQuery query) {
        String productKey = awsIotService.getProductKey(query.getDeviceId());
        final List<UsageKeyValue> dayCuttingAreaHistory;
        try {
            dayCuttingAreaHistory = iotZTRUsageMapper.getDayCuttingAreaHistory(productKey, query);
        } catch (Exception e) {
            log.error("getDayCuttingAreaHistory data error，query:{},{}", query, e);
            return new ArrayList<>();
        }
        return dayCuttingAreaHistory;
    }

    /**
     * 汇总统计周和月的工作时长历史记录
     * @param query 设备使用查询对象
     * @param timeQueryList 时间查询对象
     * @return 周和月的工作时长历史记录
     */
    public List<UsageKeyValue> getSumWorkingTime(UsageShadowQuery query,List<TimeQueryDto> timeQueryList) {
        String productKey = awsIotService.getProductKey(query.getDeviceId());
        List<UsageKeyValue> listResult = new ArrayList<>();
        try {
            for(TimeQueryDto queryDto : timeQueryList){
                query.setTimeStart(queryDto.getStartTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()/1000);
                query.setTimeEnd(queryDto.getEndTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()/1000);
                final Long sumWorkTime = iotZTRUsageMapper.getSumWorkTime(productKey, query);
                listResult.add(new UsageKeyValue(queryDto.getTitle(), sumWorkTime==null? 0L : sumWorkTime));
            }
        } catch (Exception e) {
            log.error("getSumWorkingTime data error，query:{},{}", query, e);
            return listResult;
        }
        return listResult;
    }

    /**
     * 汇总统计周和月的切割面积历史记录
     * @param query 设备使用查询对象
     * @param timeQueryList 时间查询对象
     * @return 周和月的切割面积历史记录
     */
    public List<UsageKeyValue> getSumCuttingArea(UsageShadowQuery query,List<TimeQueryDto> timeQueryList) {
        String productKey = awsIotService.getProductKey(query.getDeviceId());
        List<UsageKeyValue> listResult = new ArrayList<>();
        try {
            for(TimeQueryDto queryDto : timeQueryList){
                query.setTimeStart(queryDto.getStartTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()/1000);
                query.setTimeEnd(queryDto.getEndTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()/1000);
                final Long sumCuttingArea = iotZTRUsageMapper.getSumCuttingArea(productKey, query);
                listResult.add(new UsageKeyValue(queryDto.getTitle(), sumCuttingArea== null ? 0L : sumCuttingArea));
            }
        } catch (Exception e) {
            log.error("getSumCuttingArea data error，query:{},{}", query, e);
            return listResult;
        }
        return listResult;
    }

    public Long getTotalWorkTime(String deviceId) {
        return getLastValue(deviceId, TOTAL_WORK_TIME_FIELD);
    }

    public Long getTotalCuttingArea(String deviceId) {
        return getLastValue(deviceId, TOTAL_CUTTING_AREA_FIELD);
    }

    private Long getLastValue(String deviceId, String field) {
        try {
            String productKey = awsIotService.getProductKey(deviceId);
            final Map<String, Object> latestColumnLog = remoteIotDataService.getLatestColumnLog(productKey, deviceId, field, field + " is not null");
            if (Objects.isNull(latestColumnLog) || Objects.isNull(latestColumnLog.get(field))) {
                return 0L;
            }
            return Long.valueOf(latestColumnLog.get(field).toString());
        } catch (Exception e) {
            return 0L;
        }
    }

    private Long getLastValueByTimeSpan(String deviceId, String field,Long timeBegin,Long timeEnd) {
        try {
            String productKey = awsIotService.getProductKey(deviceId);
            String condition = field + " is not null and idf_1024 >= " + timeBegin+" and idf_1024 < "+timeEnd;
            final Map<String, Object> latestColumnLog = remoteIotDataService.getLatestColumnLog(productKey, deviceId, field, condition);
            if (Objects.isNull(latestColumnLog) || Objects.isNull(latestColumnLog.get(field))) {
                return 0L;
            }
            return Long.valueOf(latestColumnLog.get(field).toString());
        } catch (Exception e) {
            return 0L;
        }
    }

}
