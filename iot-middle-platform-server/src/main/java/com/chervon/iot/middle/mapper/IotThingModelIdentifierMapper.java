package com.chervon.iot.middle.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.iot.middle.domain.pojo.IotThingModelIdentifier;
import org.apache.ibatis.annotations.Mapper;

/**
 * (iot_thing_model_identifier)数据Mapper
 *
 * <AUTHOR>
 * @since 2024-07-08 13:58:19
 * @description 物模型明细表
*/
@Mapper
@DS("second")
public interface IotThingModelIdentifierMapper extends BaseMapper<IotThingModelIdentifier> {

}
