package com.chervon.iot.middle.domain.pojo;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;
import java.util.List;

import com.chervon.iot.middle.api.pojo.thingmodel.Event;
import com.chervon.iot.middle.api.pojo.thingmodel.Property;
import com.chervon.iot.middle.api.pojo.thingmodel.Service;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * (iot_thing_model_rule)实体类
 *
 * <AUTHOR>
 * @since 2024-05-14 17:01:18
 * @description 设备证书关系
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "iot_thing_model_rule",autoResultMap = true)
public class IotThingModelRule extends Model<IotThingModelRule> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
	private Long id;
    /**
     * 产品id
     */
    private String productKey;
    /**
     * 状态：DEVELOPING, PUBLISHED
     */
    private String status;
    /**
     * 物模型属性列表
     **/
    @TableField(exist = false)
    private List<Property> properties;

    /**
     * 物模型事件列表
     **/
    @TableField(exist = false)
    private List<Event> events;

    /**
     * 物模型服务列表
     **/
    @TableField(exist = false)
    private List<Service> services;
    /**
     * 创建人
     */
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField(update = "now()")
    private Date updateTime;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 修改人
     */
    private String updateBy;

}