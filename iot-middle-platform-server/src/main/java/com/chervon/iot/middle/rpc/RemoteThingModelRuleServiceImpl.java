package com.chervon.iot.middle.rpc;

import com.chervon.common.core.domain.PageRequest;
import com.chervon.common.core.domain.PageResult;
import com.chervon.iot.middle.api.dto.model.EventDto;
import com.chervon.iot.middle.api.dto.model.PropertyDto;
import com.chervon.iot.middle.api.dto.model.ServiceDto;
import com.chervon.iot.middle.api.pojo.thingmodel.BaseThingModelItem;
import com.chervon.iot.middle.api.pojo.thingmodel.Event;
import com.chervon.iot.middle.api.pojo.thingmodel.IotThingModel;
import com.chervon.iot.middle.api.pojo.thingmodel.Property;
import com.chervon.iot.middle.api.service.RemoteThingModelService;
import com.chervon.iot.middle.api.vo.product.IotThingModelDto;
import com.chervon.iot.middle.service.IotThingModelIdentifierService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 设备物模型表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-02-18
 */
@DubboService
@Service
@Slf4j
public class RemoteThingModelRuleServiceImpl  implements RemoteThingModelService {

    @Autowired
    private IotThingModelIdentifierService iotThingModelIdentifierService;


    @Override
    public Boolean addProperty(@Valid PropertyDto propertyDto) {
        return iotThingModelIdentifierService.addProperty(propertyDto);
    }

    @Override
    public Property getProperty(String productKey, String identifier) {
        return iotThingModelIdentifierService.getProperty(productKey,identifier);
    }

    @Override
    public Boolean editProperty(PropertyDto propertyDto) {
        return iotThingModelIdentifierService.editProperty(propertyDto);
    }

    @Override
    public Boolean deleteProperty(String productKey, String identifier) {
        return iotThingModelIdentifierService.deleteProperty(productKey,identifier);
    }

    @Override
    public PageResult<Property> pageProperties(PageRequest pageRequest, String productKey) {
        return iotThingModelIdentifierService.pageProperties(pageRequest,productKey);
    }

    @Override
    public List<Property> listProperties(String productKey) {
        return iotThingModelIdentifierService.listProperties(productKey);
    }

    @Override
    public Boolean addService(ServiceDto serviceDto) {
        return iotThingModelIdentifierService.addService(serviceDto);
    }

    @Override
    public com.chervon.iot.middle.api.pojo.thingmodel.Service getService(
            String productKey, String identifier) {
        return iotThingModelIdentifierService.getService(productKey,identifier);
    }

    @Override
    public Boolean editService(ServiceDto serviceDto) {
        return iotThingModelIdentifierService.editService(serviceDto);
    }

    @Override
    public Boolean deleteService(String productKey, String identifier) {
        return iotThingModelIdentifierService.deleteService(productKey,identifier);
    }

    @Override
    public PageResult<com.chervon.iot.middle.api.pojo.thingmodel.Service> pageServices(
            PageRequest pageRequest, String productKey) {
        return iotThingModelIdentifierService.pageServices(pageRequest,productKey);
    }

    @Override
    public List<com.chervon.iot.middle.api.pojo.thingmodel.Service> listServices(String productKey) {
        return iotThingModelIdentifierService.listServices(productKey);
    }

    @Override
    public Boolean addEvent(EventDto eventDto) {
        return iotThingModelIdentifierService.addEvent(eventDto);
    }

    @Override
    public Event getEvent(String productKey, String identifier) {
        return iotThingModelIdentifierService.getEvent(productKey,identifier);
    }

    @Override
    public Event getSimpleEvent(String productKey, String identifier) {
        return iotThingModelIdentifierService.getSimpleEvent(productKey,identifier);
    }


    @Override
    public Boolean editEvent(EventDto eventDto) {
        return iotThingModelIdentifierService.editEvent(eventDto);
    }

    @Override
    public Boolean deleteEvent(String productKey, String identifier) {
        return iotThingModelIdentifierService.deleteEvent(productKey,identifier);
    }

    @Override
    public PageResult<Event> pageEvents(PageRequest pageRequest, String productKey) {
        return iotThingModelIdentifierService.pageEvents(pageRequest,productKey);
    }

    @Override
    public List<Event> listEvents(String productKey) {
        return iotThingModelIdentifierService.listEvents(productKey);
    }

    /**
     * 获取物模型
     *
     * @param productKey: 产品标识
     * <AUTHOR>
     * @date 13:36 2022/5/19
     * @return: com.chervon.iot.middle.api.pojo.thingmodel.IotThingModel
     **/
    @Override
    public IotThingModel getThingModel(String productKey) {
        return iotThingModelIdentifierService.getProductThingModelAll(productKey);
    }

    @Override
    public Boolean addThingModel(IotThingModelDto iotThingModelDto) {
        return iotThingModelIdentifierService.addThingModel(iotThingModelDto);
    }

    @Override
    public List<BaseThingModelItem> listThingModelIdentifiersLikeName(Long productId, String name) {
        return iotThingModelIdentifierService.listThingModelIdentifiersLikeName(productId,name);
    }

    @Override
    public BaseThingModelItem getThingModelByIdentifier(Long productId, String identifier) {
        return iotThingModelIdentifierService.getThingModelByIdentifier(productId,identifier);
    }
}
