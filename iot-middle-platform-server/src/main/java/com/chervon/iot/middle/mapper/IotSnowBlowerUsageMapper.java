package com.chervon.iot.middle.mapper;

import com.chervon.iot.middle.domain.pojo.SnowBlowerUsage;
import com.chervon.iot.middle.api.dto.query.UsageShadowQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 72002扫雪机设备使用数据表 Mapper 接口
 *
 */
@Mapper
public interface IotSnowBlowerUsageMapper {

    /**
     * 查询72002设备上次最近使用完成的时间戳
     * @param deviceId: 设备id
     **/
    String getLastUsageTs(@Param("productKey") String productKey,@Param("deviceId") String deviceId);
    /**
     * 查询72002设备上次最近使用完成的记录
     * @param query: 查询条件
     **/
    List<SnowBlowerUsage> selectLastUsageData(@Param("productKey") String productKey,@Param("query") UsageShadowQuery query);

    /**
     * 查询72002设备指定时间段内的负载数据集合
     * @param query
     */
    List<SnowBlowerUsage> selectWorkLoadTimeSpan(@Param("productKey") String productKey,@Param("query") UsageShadowQuery query);

    Double selectAvgPowerConsumed(@Param("productKey") String productKey,@Param("query") UsageShadowQuery query);

}
