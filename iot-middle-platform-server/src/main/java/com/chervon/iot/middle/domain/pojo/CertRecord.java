package com.chervon.iot.middle.domain.pojo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @className CertRecord
 * @description
 * @date 2022/7/15 16:52
 */
@Data
public class CertRecord implements Serializable {
    private static final long serialVersionUID = 1L;
	@ApiModelProperty("设备id")
	private String device;

	@ApiModelProperty("证书id")
	private String certificateId;

	@ApiModelProperty("证书aws资源名称")
	private String certificateArn;

	@ApiModelProperty("pem格式证书")
	private String certificatePem;

	@ApiModelProperty("证书私钥")
	private String privateKey;

    private Date createTime;

}
