package com.chervon.iot.middle.domain.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 61001割草机设备日使用数据
 * <AUTHOR>
 * @since 2024-02-26 10:52
 **/
@Data
@ApiModel(value = "61001割草机设备使用数据", description = "61001割草机设备日使用数据")
public class CropperUsage implements Serializable {
    /**
     * 设备Id
     */
    @ApiModelProperty("设备Id")
    private String deviceId;
    /**
     * 割草面积：4 Acre
     * 物模型：【1035】
     */
    @ApiModelProperty("割草面积：4 Acre: 后台返回单位：平方分米")
    private Integer cuttingArea;
    /**
     * 割草时间：124 min
     * 物模型：【1016】
     */
    @ApiModelProperty("割草时间：124 min 后台返回单位：秒")
    private Integer cuttingTime;
    /**
     * 平均速度：12 Mph      【5006】
     * 物模型：【5006】
     */
    @ApiModelProperty("平均速度：12 Mph")
    private Integer averageSpeed;

    /**
     * 最大速度：12 Mph      【5007】
     * 物模型：【5007】
     */
    @ApiModelProperty("最大速度：20 Mph")
    private Integer maxSpeed;
    /**
     * 行驶距离：5 miles
     * 物模型：【5008】
     */
    @ApiModelProperty("行驶距离")
    private Integer drivingDistance;

}
//    cutting area: 4 Acre        【1035】
//    Cutting time: 124 min       【1016】
//    Average speed:  12 Mph      【5006】
//    Max speed: 20 Mph           【5007】
//    Driving distance: 5 miles   【5008】