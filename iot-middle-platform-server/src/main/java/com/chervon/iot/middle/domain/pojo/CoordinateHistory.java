package com.chervon.iot.middle.domain.pojo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备历史状态及轨迹
 * <AUTHOR>
 * @since 2024-01-24 10:52
 **/
@Data
public class CoordinateHistory implements Serializable {
    /**
     * 使用时间戳
     */
    @ApiModelProperty("使用时间戳")
    private Long  ts1024;
    /**
     * 设备当时状态
     */
    @ApiModelProperty("设备当时状态")
    private Integer status;

    /**
     * 设备当时坐标值：
     */
    @ApiModelProperty("设备当时坐标值")
    private String coordinate;
}
