package com.chervon.iot.middle.rpc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.chervon.iot.middle.api.dto.query.TimeQueryDto;
import com.chervon.iot.middle.api.dto.query.UsageShadowQuery;
import com.chervon.iot.middle.api.pojo.PowerLoadVo;
import com.chervon.iot.middle.api.pojo.SnowBlowerUsageVo;
import com.chervon.iot.middle.api.pojo.usage.UsageKeyValue;
import com.chervon.iot.middle.api.service.RemoteIotDataUsageService;
import com.chervon.iot.middle.api.vo.usage.CoordinateHistoryVo;
import com.chervon.iot.middle.domain.dto.WorkLoadDto;
import com.chervon.iot.middle.domain.pojo.CoordinateHistory;
import com.chervon.iot.middle.domain.pojo.SnowBlowerUsage;
import com.chervon.iot.middle.mapper.IotSnowBlowerUsageMapper;
import com.chervon.iot.middle.mapper.IotUsageMapper;
import com.chervon.iot.middle.service.AwsIotService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 设备使用数据服务
 *
 * <AUTHOR>
 * @date 2023/4/23 15:41
 */
@Service
@Slf4j
@DubboService
public class RemoteIotDataUsageServiceImpl implements RemoteIotDataUsageService {

    @Autowired
    private IotUsageMapper iotUsageMapper;
    @Autowired
    private IotSnowBlowerUsageMapper iotSnowBlowerUsageMapper;
    @Autowired
    private AwsIotService awsIotService;

    /**
     * 获取三个月前的时间戳
     *
     * @param currentTimeMillis 当前时间戳
     * @return 三个月前时间戳
     */
    public static Long get3MothBeforeTs(long currentTimeMillis) {
        // 获取当前时间戳
        // 创建一个日历对象
        Calendar calendar = Calendar.getInstance();
        // 设置时间戳
        calendar.setTimeInMillis(currentTimeMillis);
        // 往前推三个月
        calendar.add(Calendar.MONTH, -3);
        // 获取新的时间戳
        long threeMonthsAgoTimeMillis = calendar.getTimeInMillis();
        //三个月前的时间戳: " + threeMonthsAgoTimeMillis);
        return threeMonthsAgoTimeMillis;
    }

    /**
     * 查询72002设备上次最近使用完成的记录
     *
     * @param deviceId: 设备id:NSN072023000002
     **/
    @Override
    public SnowBlowerUsageVo selectIotLastHourUsageData(String deviceId) {
        String productKey = awsIotService.getProductKey(deviceId);
        final String strLastUsageTs = iotSnowBlowerUsageMapper.getLastUsageTs(productKey, deviceId);
        if (Objects.isNull(strLastUsageTs)) {
            return null;
        }
        Long lastUsageTs = Long.valueOf(strLastUsageTs);
        Long begin = lastUsageTs - 3600;//60分钟前的开始时间戳
        UsageShadowQuery query = new UsageShadowQuery();
        query.setDeviceId(deviceId);
        query.setTimeStart(begin);
        query.setTimeEnd(lastUsageTs);
        final List<SnowBlowerUsage> listHourSnowBlowerUsage = iotSnowBlowerUsageMapper.selectLastUsageData(productKey, query);
        if (CollectionUtils.isEmpty(listHourSnowBlowerUsage)) {
            return null;
        }
        int sumConsumed = 0;
        Long workDuration = 0L;
        for (SnowBlowerUsage snowBlowerUsage : listHourSnowBlowerUsage) {
            Long timeSpan = Long.valueOf(snowBlowerUsage.getWorkingTimeEnd()) - Long.valueOf(snowBlowerUsage.getWorkingTimeStart());
            //工作时长：秒
            workDuration += timeSpan;
            sumConsumed += Integer.valueOf(snowBlowerUsage.getPowerConsumed());
        }
        SnowBlowerUsageVo result = new SnowBlowerUsageVo(deviceId);
        result.setTs(lastUsageTs);
        result.setWorkingTimeStart(begin);
        result.setWorkingTimeEnd(lastUsageTs);
        result.setWorkingTimeDuration(workDuration);
        result.setPowerConsumed(sumConsumed);
        return result;
    }

    /**
     * 查询72002设备指定时间段内的负载数据集合
     * 72002负载数据值逻辑：设备最后一次使用（超过30秒）的截止时间点（物模型1033），往前推一个小时间隔的范围内的所有上报记录，
     * 然后用上面的一个小时范围的开始截止时间戳，搜索4004的物模型数据，然后将4004按每分钟的自然时间内负载值计算每个分钟内的平均功率值，
     * 平均后的61个点输出给前端展示。
     *
     * @param query
     */
    @Override
    public PowerLoadVo selectLastHourAvgWorkLoad(UsageShadowQuery query) {
        String productKey = awsIotService.getProductKey(query.getDeviceId());
        if (Objects.isNull(query.getTimeStart()) || Objects.isNull(query.getTimeEnd())) {
            //查询最后使用的时间戳
            final String strLastUsageTs = iotSnowBlowerUsageMapper.getLastUsageTs(productKey, query.getDeviceId());
            if (Objects.isNull(strLastUsageTs)) {
                return null;
            }
            Long lastUsageTs = Long.valueOf(strLastUsageTs) * 1000;
            query.setTimeEnd(lastUsageTs);
            query.setTimeStart(lastUsageTs - 3600000);//60分钟前的开始时间戳
        }

        //查询最后使用时间戳并向前偏移一小时使用负载数据列表
        List<SnowBlowerUsage> snowBlowerUsages;
        try {
            snowBlowerUsages = iotSnowBlowerUsageMapper.selectWorkLoadTimeSpan(productKey, query);
        } catch (Exception exception) {
            return null;
        }
        if (CollectionUtils.isEmpty(snowBlowerUsages)) {
            return null;
        }
        List<WorkLoadDto> listHourData = new ArrayList<>();
        for (SnowBlowerUsage snowBlowerUsage : snowBlowerUsages) {
            final String jsonWorkLoad = snowBlowerUsage.getWorkLoad();
            final List<WorkLoadDto> workLoadDtos = JSON.parseObject(jsonWorkLoad, new TypeReference<List<WorkLoadDto>>() {
            });
            listHourData.addAll(workLoadDtos);
        }
        //.filter(a->a.getTs_ms()>= query.getTimeStart() && a.getTs_ms()<= query.getTimeEnd()) 备用
        listHourData = listHourData.stream().sorted(Comparator.comparing(WorkLoadDto::getTs_ms)).collect(Collectors.toList());
        //计算采用时间范围平均值
        List<Integer> loadResult = new ArrayList<>();
        //负载数据的最早使用记录时间戳（用于判断平均值计算的中断条件）
        final Long firstTs = listHourData.get(0).getTs_ms();
        //设备使用的最后一次时间戳：毫秒
        Long end = query.getTimeEnd();
        //往前偏移一分钟毫秒数
        Long begin = end - 60 * 1000L;
        //从后面往前偏移计算平均值
        for (int i = 0; i <= 60; i++) {
            //如果最后时间戳比数据中第一个时间戳还大，说明前面已无数据，无需计算平均值，直接补0
            if (end < firstTs) {
                loadResult.add(0);
                continue;
            }
            Long finalBegin = begin;
            Long finalEnd = end;
            //分片截取一分钟内的使用负载记录累加和
            final Optional<Integer> sum = listHourData.stream().filter(a -> a.getTs_ms() > finalBegin && a.getTs_ms() <= finalEnd).map(a -> a.getLoad()).reduce(Integer::sum);
            if (sum.isPresent()) {
                loadResult.add(sum.get() / 60);
            } else {
                loadResult.add(0);
            }
            end = finalBegin;
            begin = end - 60 * 1000L;
        }
        //数组是从后往前添加的，需要反转数组
        Collections.reverse(loadResult);
        PowerLoadVo powerLoadVo = new PowerLoadVo();
        powerLoadVo.setDeviceId(query.getDeviceId());
        powerLoadVo.setEndTimeStamp(query.getTimeEnd());
        powerLoadVo.setListPowerDissipation(loadResult);
        return powerLoadVo;
    }

    /**
     * 获取三个月内平均功耗
     *
     * @param query
     * @return
     */
    @Override
    public Integer getHistoryAvgPowerConsumed(UsageShadowQuery query) {
        query.setTimeEnd(System.currentTimeMillis());
        query.setTimeStart(get3MothBeforeTs(query.getTimeEnd()));
        String productKey = awsIotService.getProductKey(query.getDeviceId());
        Double avgPowerConsumed;
        try {
            avgPowerConsumed = iotSnowBlowerUsageMapper.selectAvgPowerConsumed(productKey, query);
        } catch (Exception exception) {
            return 0;
        }
        return Objects.isNull(avgPowerConsumed)?0:avgPowerConsumed.intValue();
    }

    @Override
    public Object getUsageTrackHistory(UsageShadowQuery query1, UsageShadowQuery query2) {
        String deviceId = query1.getDeviceId();
        String productKey = awsIotService.getProductKey(deviceId);
        Map<String, String> workState = query2.getWorkState();
        Map<String, Object> usageData;
        Long lastSyncedTime = getLastSyncedTime(deviceId);
        try {
            // 查询设备当天工况情况
            usageData = iotUsageMapper.getUsageData(productKey, query1);
            if (!CollectionUtils.isEmpty(usageData)) {
                usageData.put("date", query1.getDate());
            } else {
                usageData = new HashMap<>(16);
                usageData.put("deviceId", deviceId);
                usageData.put("date", query1.getDate());
                // 当查询当天无数据时，默认返回各工况的指标值为0
                Set<String> indexList = query1.getIndexList();
                for (String i : indexList) {
                    usageData.put(i, 0);
                }
            }
            // 设备最新上报数据的时间戳
            usageData.put("lastSyncedTime", lastSyncedTime);
        } catch (Exception e) {
            log.error("getTracksHistory data error，deviceId:{}, query1:{}", query1.getDeviceId(), query1, e);
            return null;
        }
        try {
            //查询设备当天坐标轨迹和状态
            List<CoordinateHistory> coordinateHistory = iotUsageMapper.getTrackHistory(productKey, query2);
            if (!CollectionUtils.isEmpty(coordinateHistory)) {
                List<CoordinateHistoryVo> coordinateVos = coordinateHistory.stream()
                        .map(a -> new CoordinateHistoryVo(a.getTs1024(), a.getStatus(), workState.get(String.valueOf(a.getStatus())), a.getCoordinate()))
                        .collect(Collectors.toList());
                usageData.put("listCoordinateHistory", coordinateVos);
            } else {
                usageData.put("listCoordinateHistory", new ArrayList<>());
            }
            return usageData;
        } catch (Exception e) {
            log.error("getTracksHistory data error，deviceId:{}, query2:{},", query2.getDeviceId(), query2, e);
            return null;
        }
    }

    @Override
    public List<UsageKeyValue> getDayHistory(UsageShadowQuery query, List<String> timeQueryList) {
        String productKey = awsIotService.getProductKey(query.getDeviceId());
        final List<UsageKeyValue> dayHistory = new ArrayList<>();
        try {
            Long beforeHistory = iotUsageMapper.getBeforeHistory(productKey, query);
            List<UsageKeyValue> queryResult = iotUsageMapper.getDayHistory(productKey, query);
            // 如果查询结果为空
            if (queryResult.isEmpty()) {
                // 无历史数据时返回空数组，有历史数据时返回填充0的数组
                return beforeHistory == 0 ?
                        new ArrayList<>() :
                        timeQueryList.stream()
                                .map(time -> new UsageKeyValue(time, 0L))
                                .collect(Collectors.toList());
            }
            // 如果当前查询周期内有上报数据，但上报的值为空，返回空数组
            AtomicLong daySum = new AtomicLong(0L);
            queryResult.forEach(a -> {
                long dataValue = a.getDataValue() == null ? 0 : a.getDataValue();
                daySum.addAndGet(dataValue);
            });
            if (beforeHistory == 0 && daySum.get() == 0) {
                return new ArrayList<>();
            }
            // 正常查询周期内有上报数据，则返回查询结果
            dayHistory.addAll(queryResult);
        } catch (Exception e) {
            log.error("getDayUsageHistory data error, query: {}, ", query, e);
            return new ArrayList<>();
        }
        return dayHistory;
    }

    @Override
    public List<UsageKeyValue> getWMHistory(UsageShadowQuery query, List<TimeQueryDto> timeQueryList) {
        String productKey = awsIotService.getProductKey(query.getDeviceId());
        List<UsageKeyValue> listResult = new ArrayList<>();
        try {
            Long beforeHistory = iotUsageMapper.getBeforeHistory(productKey, query);
            long whSum = 0L;
            for (TimeQueryDto queryDto : timeQueryList) {
                query.setTimeStart(queryDto.getStartTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
                query.setTimeEnd(queryDto.getEndTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
                final Long whData = iotUsageMapper.getWMHistory(productKey, query);
                long whDataT = whData == null ? 0 : whData;
                listResult.add(new UsageKeyValue(queryDto.getTitle(), whDataT));
                whSum += whDataT;
            }
            if (beforeHistory == 0 && whSum == 0) {
                return new ArrayList<>();
            }
        } catch (Exception e) {
            log.error("getWMHistory data error, query: {}, ", query, e);
            return listResult;
        }
        return listResult;
    }

    @Override
    public Long getTotalHistory(UsageShadowQuery query) {
        String productKey = awsIotService.getProductKey(query.getDeviceId());
        Long total;
        try {
            total = iotUsageMapper.getTotalHistory(productKey, query);
        } catch (Exception e) {
            log.error("getTotalHistory data error, query: {}, ", query, e);
            return 0L;
        }
        return total;
    }

    @Override
    public Long getLastSyncedTime(String deviceId) {
        String productKey = awsIotService.getProductKey(deviceId);
        Long lastSyncedTime;
        try {
            lastSyncedTime = iotUsageMapper.getLastSyncedTime(productKey, deviceId);
        } catch (Exception e) {
            log.error("getLastSyncedTime data error, query: {}, ", deviceId, e);
            return null;
        }
        return lastSyncedTime;
    }


}
