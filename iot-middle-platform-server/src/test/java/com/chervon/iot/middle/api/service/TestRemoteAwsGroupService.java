package com.chervon.iot.middle.api.service;

import com.amazonaws.services.iot.AWSIot;
import com.amazonaws.services.iot.model.*;
import com.chervon.common.core.utils.JsonUtils;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.iot.middle.api.dto.group.AddAwsStaticGroupDto;
import com.chervon.iot.middle.api.dto.device.IotGroupDeviceDto;
import com.chervon.iot.middle.api.dto.group.AddDevice2GroupStaticDto;
import com.chervon.iot.middle.api.vo.device.IotDeviceQueryListVo;
import com.chervon.iot.middle.service.AwsIotService;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;
import java.util.stream.Collectors;


/**
 * @Author：flynn.wang
 * @Date：2023/10/17 15:27
 */
@SpringBootTest
public class TestRemoteAwsGroupService {

    @Autowired
    private RemoteAwsGroupService remoteAwsGroupService;

    @Autowired
    private AwsIotService awsIotService;

    /**
     * 创建静态分组
     */
    @Test
    public void testAddStaticGroupDevice() {
        String groupName = "TEST_STATIC_GROUP";
        AddAwsStaticGroupDto addAwsStaticGroupDto = new AddAwsStaticGroupDto();
        addAwsStaticGroupDto.setGroupName(groupName);
        addAwsStaticGroupDto.setRemark(groupName);
        String thingGroupArn = remoteAwsGroupService.addStaticGroup(addAwsStaticGroupDto);
        System.out.println(thingGroupArn);
    }

    /**
     * 删除静态分组
     */
    @Test
    public void testDeleteStaticGroupDevice() {
        String groupName = "TEST_STATIC_GROUP_1";
        remoteAwsGroupService.deleteStaticGroup(groupName);
    }

    /**
     * 测试搜索分组
     */
    @Test
    public void testSearchGroupDevice() {
        String groupName = "TEST_STATIC_GROUP";

        IotGroupDeviceDto iotGroupDeviceDto = new IotGroupDeviceDto();
        iotGroupDeviceDto.setGroupName(groupName);
        iotGroupDeviceDto.setNextToken(null);
        iotGroupDeviceDto.setMaxResults(20);
        IotDeviceQueryListVo queryListVo = remoteAwsGroupService
                .searchGroupDevice(iotGroupDeviceDto);
        System.out.println(JsonUtils.toJsonString(queryListVo));
    }

    /**
     * 测试获取 aws分组标识
     */
    @Test
    public void testGetThingGroupArn() {
        String groupName = "TEST_STATIC_GROUP";
        String thingGroupArn = remoteAwsGroupService.getThingGroupArn(groupName);
        System.out.println(thingGroupArn);
    }

    /**
     * 测试 查询设备所在分组
     */
    @Test
    public void testListGroupNames() {
        String deviceId = "TEST_STATIC_GROUP_DEVICE";
        List<String> groupNames = remoteAwsGroupService.listGroupNames(deviceId);
        System.out.println(JsonUtils.toJsonString(groupNames));
    }


    /**
     * 测试 添加设备到静态分组
     */
    @Test
    public void testAddDevice2StaticGroup() {
        String groupName = "TEST_STATIC_GROUP";
        String deviceId = "TEST_STATIC_GROUP_DEVICE";
        AddDevice2GroupStaticDto addDevice2GroupStaticDto = new AddDevice2GroupStaticDto();
        addDevice2GroupStaticDto.setDeviceId(deviceId);
        addDevice2GroupStaticDto.setGroupName(groupName);
        remoteAwsGroupService.addDevice2StaticGroup(addDevice2GroupStaticDto);
    }

    /**
     * 测试 批量添加设备到静态分组
     */
    @Test
    public void testBatchAddDevice2StaticGroup() {
        String groupName = "TEST_STATIC_GROUP";
        List<String> deviceIds = new ArrayList<>();
        remoteAwsGroupService.batchAddDevice2StaticGroup(groupName, deviceIds);
    }




}
