# Spring Boot 2.6.8与Swagger 3.0.0的兼容性问题解决方案

## 当前状况

1. **版本信息**：
   - Spring Boot版本: 2.6.8
   - Springfox Swagger版本: 3.0.0
   - 项目中同时使用了knife4j（基于Swagger的增强UI）

2. **兼容性问题**：
   - Spring Boot 2.6.8与Swagger 3.0.0存在已知的兼容性问题
   - 当前解决方案是使用actuator相关类实现的Bean方法`webEndpointServletHandlerMapping`

3. **安全顾虑**：
   - Spring Boot Actuator可能存在安全漏洞风险
   - 需要在保证系统功能正常的前提下降低安全风险

## 可行的解决方案

### 方案1: 保留Actuator但增强安全配置

**步骤**:
1. 仅在`chervon-common-swagger`模块保留actuator依赖
2. 从其他所有模块中移除actuator依赖
3. 在应用配置中禁用所有actuator端点

```yaml
# application.yml配置
management:
  endpoints:
    web:
      exposure:
        include: none  # 不暴露任何端点
  endpoint:
    health:
      enabled: false
    info:
      enabled: false
```

**优点**:
- 实施简单，风险低
- 不需要修改代码
- 保持兼容性

**缺点**:
- 依然存在依赖项
- 仍有安全风险，尽管已降低

### 方案2: 使用自定义WebMvcEndpointHandlerMapping实现

可以创建一个不依赖actuator的自定义实现，替换当前的解决方案:

```java
/**
 * 自定义实现以解决Spring Boot 2.6.x与Swagger 3.0.0的兼容性问题
 * 不需要依赖actuator
 */
@Bean
public WebMvcHandlerProvider webMvcHandlerProvider(
        @Qualifier("webEndpointServletHandlerMapping") Optional<HandlerMapping> webEndpointServletHandlerMapping,
        @Qualifier("requestMappingHandlerMapping") RequestMappingHandlerMapping requestMappingHandlerMapping) {
    
    List<HandlerMapping> handlerMappings = new ArrayList<>();
    // 添加所有其他HandlerMapping
    handlerMappings.add(requestMappingHandlerMapping);
    
    return () -> handlerMappings;
}
```

**优点**:
- 完全移除actuator依赖
- 消除相关安全风险

**缺点**:
- 需要修改代码
- 可能需要更多测试验证

### 方案3: 迁移到SpringDoc OpenAPI

**步骤**:
1. 移除Springfox和actuator依赖
2. 添加SpringDoc依赖
3. 更新API注解和配置

```xml
<!-- 移除Springfox依赖 -->
<!-- 添加SpringDoc依赖 -->
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-ui</artifactId>
    <version>1.6.9</version> <!-- 与Spring Boot 2.6.8兼容的版本 -->
</dependency>
```

**优点**:
- 完全兼容Spring Boot 2.6.x
- 不需要actuator依赖
- 积极维护，性能更好

**缺点**:
- 工作量较大
- 可能需要修改现有的Swagger注解

## 推荐方案

考虑到安全性、工作量和风险平衡，**推荐先实施方案1**，同时评估方案3作为长期解决方案：

1. 短期：保留最小化actuator配置，同时加强安全措施
2. 长期：规划迁移到SpringDoc OpenAPI的计划和时间表

## 下一步行动

1. 在测试环境中验证方案1的安全配置
2. 创建一个概念验证项目，评估SpringDoc OpenAPI迁移的复杂性
3. 制定详细的执行计划和回滚策略
