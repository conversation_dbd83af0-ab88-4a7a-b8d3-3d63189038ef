package com.chervon.iot.app.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.chervon.common.core.constant.Constants;
import com.chervon.usercenter.api.dto.enums.*;
import com.google.common.collect.Lists;

import com.chervon.common.core.utils.JsonUtils;
import com.chervon.iot.app.domain.dto.device.DeviceWarrantyQuestionnaireAddDto;
import com.chervon.usercenter.api.dto.sf.SfSurveySubmitDto;
import com.chervon.usercenter.api.service.SaleForceService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author：flynn.wang
 * @Date：2024/2/1 20:04
 */
@SpringBootTest
public class SalesForceSurveyServiceTest {


    @Autowired
    DeviceWarrantyQuestionnaireService deviceWarrantyQuestionnaireService;

    /**
     * 测试组装SfSurveySubmitDto对象
     */
    @Test
    public void testAssembleSurveySubmitDto() {
        String content = "{\n" +
                "\t\"lastName\": \"wang\",\n" +
                "\t\"phone\": \"aakmmakka\",\n" +
                "\t\"firstName\": \"flynn\",\n" +
                "\t\"isParticipateResearch\": \"1\",\n" +
                "\t\"province\": \"British Columbia\",\n" +
                "\t\"deviceId\": \"TLT02230000002X\",\n" +
                "\t\"questionOneAnswers\": [0, 1],\n" +
                "\t\"questionOneOther\": \"\",\n" +
                "\t\"questionTwoOther\": \"aaa\",\n" +
                "\t\"questionTwoAnswers\": [0, 1, 8, 9],\n" +
                "\t\"questionThreeAnswers\": [0, 1, 17, 18],\n" +
                "\t\"questionThreeOther\": \"a\",\t\n" +
                "\t\"questionFourAnswers\": [9],\n" +
                "\t\"questionFourOther\": \"aaa\",\n" +
                "\t\"address\": \"11\",\n" +
                "\t\"city\": \"ss\",\n" +
                "\t\"zipCode\": \"aa\",\n" +
                "\t\"country\": \"Canada\",\n" +
                "\t\"email\": \"<EMAIL>\"\n" +
                "}";
        DeviceWarrantyQuestionnaireAddDto deviceWarrantyQuestionnaireAddDto = JSONUtil.toBean(content, DeviceWarrantyQuestionnaireAddDto.class);
        deviceWarrantyQuestionnaireService.add(deviceWarrantyQuestionnaireAddDto);
    }

}
