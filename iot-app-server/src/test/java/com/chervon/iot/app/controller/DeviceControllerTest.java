package com.chervon.iot.app.controller;

import com.alibaba.fastjson.JSONObject;
import com.chervon.iot.app.domain.dto.device.DeviceFaultQueryDto;
import com.chervon.technology.api.vo.DeviceFaultVo;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 故障列表查询单元测试
 * <AUTHOR> 2024/7/23
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
public class DeviceControllerTest {
    @Autowired
    private MockMvc mockMvc;
    @Test
    void getDeviceFaultList() throws Exception {
        DeviceFaultQueryDto reqDto=new DeviceFaultQueryDto();
        reqDto.setDeviceId("XZT066440061318");
        mockMvc.perform(MockMvcRequestBuilders.post("/device/getDeviceFaultList")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .accept(MediaType.APPLICATION_JSON_UTF8)
                        .header("lang", "zh")
                        .header("authorization","Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJ1c2VyIiwibG9naW5JZCI6IjE3Njc0NzgwODU5MTgxNzExNDQiLCJyblN0ciI6ImV2VnNyazNFRUhNbEVETTZHaWVYYjZlWVlKbzNNVEhPIn0.w4GJDc4a4jc9naiwkvnFTFJhOURKQ2gtA6q3B_VXeRI")
                        .content(JSONObject.toJSONString(reqDto)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.entry.*").isArray())
                .andDo(MockMvcResultHandlers.print());
    }

    @Test
    void getDeviceFaultMessage() throws Exception {
        DeviceFaultQueryDto reqDto=new DeviceFaultQueryDto();
        reqDto.setDeviceId("XZT066440061318");
        mockMvc.perform(MockMvcRequestBuilders.post("/device/getDeviceFaultMessage")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .accept(MediaType.APPLICATION_JSON_UTF8)
                        .header("lang", "zh")
                        .header("authorization","Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJ1c2VyIiwibG9naW5JZCI6IjE3Njc0NzgwODU5MTgxNzExNDQiLCJyblN0ciI6ImV2VnNyazNFRUhNbEVETTZHaWVYYjZlWVlKbzNNVEhPIn0.w4GJDc4a4jc9naiwkvnFTFJhOURKQ2gtA6q3B_VXeRI")
                        .content(JSONObject.toJSONString(reqDto)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.entry.*").isArray())
                .andDo(MockMvcResultHandlers.print());
    }
}