package com.chervon.iot.app.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.iot.app.domain.dataobject.DeviceInfoReceipt;
import com.chervon.iot.app.mapper.DeviceInfoReceiptMapper;
import com.chervon.iot.app.service.DeviceInfoReceiptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-09-01 19:34
 **/
@Service
@Slf4j
public class DeviceInfoReceiptServiceImpl extends ServiceImpl<DeviceInfoReceiptMapper, DeviceInfoReceipt>
    implements DeviceInfoReceiptService {

    @Override
    public void removeByDeviceId(String deviceId) {
        remove(Wrappers.<DeviceInfoReceipt>lambdaQuery().eq(DeviceInfoReceipt::getDeviceId,deviceId));
    }

    @Override
    public List<DeviceInfoReceipt> listByDeviceId(String deviceId) {
        return list(Wrappers.<DeviceInfoReceipt>lambdaQuery().eq(DeviceInfoReceipt::getDeviceId,deviceId));
    }
}
