package com.chervon.iot.app.controller;

import com.chervon.common.core.domain.ListInfoReq;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.iot.app.domain.dto.dict.DictRequestDto;
import com.chervon.iot.app.domain.vo.dict.DictNode;
import com.chervon.iot.app.domain.vo.dict.DictVo;
import com.chervon.iot.app.service.DictService;
import com.chervon.iot.app.util.AppStringUtils;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-08-31 17:32
 **/
@RestController
@RequestMapping("/dict")
@Api(tags = "字典相关接口")
public class DictController {

    @Autowired
    private DictService dictService;

    /**
     * 根据字典名称获取字典详情列表
     *
     * @param req 字典名称列表
     * @return 字典详情列表
     */
    @PostMapping("/list/by/dict/name")
    public List<DictVo> listByDictName(@Validated @RequestBody ListInfoReq<String> req) {
        return dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(), req.getInfo());
    }

    /**
     * 从字典中模糊匹配一个最接近的匹配项目
     * @param req  字典key值，允许字典模糊匹配的描述值
     * @return 最接近的匹配项目
     */
    @PostMapping("/list/findClosestMatch")
    public DictNode findClosestMatch(@RequestBody DictRequestDto req) {
        Assert.hasText(req.getDicKey(), ErrorCode.PARAMETER_NOT_PROVIDED,"dicKey");
        Assert.hasText(req.getContent(), ErrorCode.PARAMETER_NOT_PROVIDED,"content");
        Assert.hasText(req.getLangCode(), ErrorCode.PARAMETER_NOT_PROVIDED,"langCode");
        final List<DictVo> dictVos = dictService.listByDictName(req.getLangCode().toLowerCase(), Arrays.asList(req.getDicKey()));
        if(CollectionUtils.isEmpty(dictVos)){
            return null;
        }
        final List<String> collect = dictVos.get(0).getNodes().stream().map(a -> a.getDescription()).collect(Collectors.toList());
        final String closestMatch = AppStringUtils.findClosestMatch(req.getContent(), collect);
        if(!StringUtils.hasText(closestMatch)){
            return null;
        }
        final Optional<DictNode> first = dictVos.get(0).getNodes().stream().filter(a -> a.getDescription().contains(closestMatch)).findFirst();
        if(first.isPresent()){
            return first.get();
        }
        return null;
    }
}
