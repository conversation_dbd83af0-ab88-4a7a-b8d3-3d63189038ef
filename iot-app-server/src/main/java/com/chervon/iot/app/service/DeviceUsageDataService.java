package com.chervon.iot.app.service;

import com.chervon.iot.app.domain.dto.device.DeviceUsageReqDto;
import com.chervon.iot.app.domain.dto.device.UsageHistoryReqDto;
import com.chervon.iot.app.domain.vo.device.UsageHistoryVo;

/**
 * <AUTHOR>
 */
public interface DeviceUsageDataService {

    /**
     * 获取设备使用历史记录
     * @param requestDto 请求对象
     * @return 返回历史记录
     */
    UsageHistoryVo getUsageHistory(UsageHistoryReqDto requestDto);

    /**
     * 获取设备轨迹数据
     *
     * @param requestDto 查询条件
     * @return Object
     */
    Object getTracksHistory(DeviceUsageReqDto requestDto);


}
