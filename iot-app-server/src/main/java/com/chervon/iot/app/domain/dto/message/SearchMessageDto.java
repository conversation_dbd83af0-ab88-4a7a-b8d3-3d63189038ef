package com.chervon.iot.app.domain.dto.message;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-08-29
 */
@Data
public class SearchMessageDto extends PageRequest implements Serializable {

    @ApiModelProperty("消息类型，0系统消息，1营销消息, 2设备消息")
    @NotNull
    private Integer messageType;

    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("产品Pid")
    private String productId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
