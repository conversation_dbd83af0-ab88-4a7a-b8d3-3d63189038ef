package com.chervon.iot.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.iot.app.domain.dataobject.DeviceShare;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/1
 **/
@Mapper
public interface DeviceShareMapper extends BaseMapper<DeviceShare> {
    /**
     * 根据主用户id和设备列表
     *
     * @param masterId
     * @return
     */
    List<Map<String, Object>> countShareNum(@Param("masterId") Long masterId);
}
