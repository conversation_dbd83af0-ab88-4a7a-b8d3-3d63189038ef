package com.chervon.iot.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.iot.app.domain.dataobject.AppUserDevice;
import com.chervon.iot.app.domain.dto.device.AppUserDeviceIdDto;

/**
 * <AUTHOR>
 * @date 2022/11/23 17:48
 */
public interface AppUserDeviceService extends IService<AppUserDevice> {
    /**
     * 校验用户是否为设备的主账户
     * @param deviceId
     * @param userId
     * @return
     */
    boolean checkIsMaster(String deviceId, Long userId);

    /**
     * 查询设备主用户绑定信息
     * @param deviceId
     * @return
     */
    AppUserDevice findMasterUserDevice(String deviceId);

}
