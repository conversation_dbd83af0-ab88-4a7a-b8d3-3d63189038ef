package com.chervon.iot.app.config;

import cn.hutool.extra.spring.SpringUtil;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.iot.app.api.exception.AppErrorCode;
import com.chervon.iot.app.api.exception.AppException;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.api.exception.TechnologyException;
import com.chervon.usercenter.api.exception.UserCenterErrorCode;
import com.chervon.usercenter.api.exception.UserCenterException;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/17 11:18
 */
public class ExceptionMessageUtil {

    private static final RemoteMultiLanguageService remoteMultiLanguageService = SpringUtil.getBean("remoteMultiLanguageService");

    public static AppException getException(AppErrorCode errorCode, Object... args) {
        AppException exception = new AppException(errorCode, args);
        String code = errorCode.getCode();
        if (code != null) {
            String language = remoteMultiLanguageService.simpleFindMultiLanguageByCode(code);
            if (language != null) {
                exception.setTempMessage(language);
            }
        }
        return exception;
    }

    public static TechnologyException getException(TechnologyErrorCode errorCode, Object... args) {
        TechnologyException exception = new TechnologyException(errorCode, args);
        String code = errorCode.getCode();
        if (code != null) {
            String language = remoteMultiLanguageService.simpleFindMultiLanguageByCode(code);
            if (language != null) {
                exception.setTempMessage(language);
            }
        }
        return exception;
    }

    public static UserCenterException getException(UserCenterErrorCode errorCode, Object... args) {
        UserCenterException exception = new UserCenterException(errorCode, args);
        String code = errorCode.getCode();
        if (code != null) {
            String language = remoteMultiLanguageService.simpleFindMultiLanguageByCode(code);
            if (language != null) {
                exception.setTempMessage(language);
            }
        }
        return exception;
    }

}
