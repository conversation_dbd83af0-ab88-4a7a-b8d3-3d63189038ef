package com.chervon.iot.app.controller;

import cn.hutool.json.JSONObject;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.iot.app.api.exception.AppErrorCode;
import com.chervon.iot.app.common.CommonConstant;
import com.chervon.iot.app.config.ExceptionMessageUtil;
import com.chervon.iot.app.domain.dto.device.UpdateThingModelDto;
import com.chervon.iot.app.domain.vo.device.AppDeviceShadowParamVo;
import com.chervon.iot.app.service.AppDeviceService;
import com.chervon.iot.middle.api.dto.device.IotPublishDto;
import com.chervon.iot.middle.api.service.RemoteDeviceShadowService;
import com.chervon.technology.api.RemoteProductDebugUserService;
import com.chervon.technology.api.dto.SearchModelDebugDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 设备物模型相关
 *
 * <AUTHOR>
 * @since 2022年12月30日
 **/
@Api(tags = "设备物模型相关")
@RestController
@RequestMapping("/device/shadow")
public class DeviceShadowController {

	@DubboReference
	private RemoteDeviceShadowService remoteDeviceShadowService;
	@DubboReference
	private RemoteProductDebugUserService remoteProductDebugUserService;

	@Autowired
	private AppDeviceService appDeviceService;



	@PostMapping("/identifier/value/get")
	@ApiOperation("设备暂停获取数据")
	public Object getShadowItem(@Validated @RequestBody AppDeviceShadowParamVo appDeviceShadowParamVo) {
		return remoteDeviceShadowService.getShadowItem(appDeviceShadowParamVo.getDeviceId(), appDeviceShadowParamVo.getIdentifier());
	}

	@PostMapping("/thing/model/get")
	@ApiOperation("获取设备物模型")
	public JSONObject getShadowItem(@Validated @RequestBody SearchModelDebugDto search) {
		return remoteProductDebugUserService.getThingModel(search);
	}

	/**
	 * 61001离线删除设备重置密码app调用后端接口发布指令：$aws/things/${deviceID}/shadow/name/cache/update
	 * mqtt模拟报文：{"state":{"desired":{"24006":{"1":true}}}}
	 * 接口路径：/device/shadow/thing/model/update
	 * 接口请求参数：{"deviceId":"NZT062024010560","identifier":"24006","value":{"1":true},"shadowName":"cache","customTopic":null}
	 * @param updateThingModelDto
	 * @return
	 */
	@ApiOperation(value = "变更设备物模型")
	@PostMapping("/thing/model/update")
	public R<Boolean> updateThingModel(@RequestBody UpdateThingModelDto updateThingModelDto) {
		Boolean result = appDeviceService.updateThingModel(updateThingModelDto);
		return R.ok(result);
	}

	@ApiOperation(value = "发布自定义topic更新设备物模型")
	@PostMapping("/thing/custom/publish")
	public R<Boolean> publishCustomTopic(@RequestBody UpdateThingModelDto updateThingModelDto) {
		if (StringUtils.isEmpty(updateThingModelDto.getCustomTopic()) || StringUtils.isEmpty(updateThingModelDto.getIdentifier())
				|| updateThingModelDto.getValue() == null) {
			throw ExceptionMessageUtil.getException(AppErrorCode.APP_SN07_PARAM_ERROR);
		}
		Map<String, Object> map = new HashMap<>();
		map.put(updateThingModelDto.getIdentifier(), updateThingModelDto.getValue());
		IotPublishDto publishDto=new IotPublishDto();
		publishDto.setTopic(updateThingModelDto.getCustomTopic());
		publishDto.setPayLoad(map);
		Boolean result = remoteDeviceShadowService.publish(publishDto);
		return R.ok(result);
	}

}
