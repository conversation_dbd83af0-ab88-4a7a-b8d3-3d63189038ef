package com.chervon.iot.app.controller;

import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.iot.app.domain.dto.device.DeviceUsageReqDto;
import com.chervon.iot.app.domain.dto.device.UsageHistoryReqDto;
import com.chervon.iot.app.domain.vo.device.UsageHistoryVo;
import com.chervon.iot.app.service.DeviceUsageDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 设备使用数据相关接口
 * <AUTHOR>
 */
@Api(tags = "设备使用数据相关接口")
@RestController
@RequestMapping("/data")
public class DataUsageController {

    private final DeviceUsageDataService deviceUsageDataService;


    public DataUsageController(DeviceUsageDataService deviceUsageDataService) {
        this.deviceUsageDataService = deviceUsageDataService;
    }


    @ApiOperation("通用接口--产品设备历史轨迹追踪信息查询")
    @PostMapping("/historyTracks")
    public Object historyTracks(@RequestBody DeviceUsageReqDto requestDto) {
        Assert.hasText(requestDto.getDeviceId(), ErrorCode.PARAMETER_NOT_PROVIDED,"deviceId");
        Assert.hasText(requestDto.getDate(), ErrorCode.PARAMETER_NOT_PROVIDED,"date");

        return deviceUsageDataService.getTracksHistory(requestDto);
    }

    @ApiOperation("通用接口--产品设备使用工况历史数据记录查询")
    @PostMapping("/usageHistory")
    public UsageHistoryVo usageHistory(@RequestBody UsageHistoryReqDto requestDto) {
        Assert.hasText(requestDto.getDeviceId(), ErrorCode.PARAMETER_NOT_PROVIDED, "deviceId");
        Assert.hasText(requestDto.getDateValue(), ErrorCode.PARAMETER_NOT_PROVIDED, "dateValue");
        Assert.notNull(requestDto.getDateType(), ErrorCode.PARAMETER_NOT_PROVIDED, "dateType");
        Assert.notNull(requestDto.getDatePeriod(), ErrorCode.PARAMETER_NOT_PROVIDED, "datePeriod");
        Assert.notNull(requestDto.getBusType(), ErrorCode.PARAMETER_NOT_PROVIDED, "busType");

        return deviceUsageDataService.getUsageHistory(requestDto);
    }

}
