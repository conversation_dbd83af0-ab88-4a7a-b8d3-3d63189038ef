package com.chervon.iot.app.service;

import com.chervon.iot.app.domain.dto.user.*;
import com.chervon.iot.app.domain.vo.user.LoginUserResp;
import com.chervon.usercenter.api.dto.PhoneInfoDto;
import com.chervon.usercenter.api.vo.UserVo;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR>
 * @date 2022/6/26 12:57
 */
public interface AppUserService {

    /**
     * 注册用户
     * @param req 注册用户信息
     * @return
     */
    LoginUserResp register(RegisterDto req, @RequestHeader("lang") String lang);

    /**
     * 用户注销
     * @return 注销结果
     */
    Boolean cancelUser();

    /**
     * 用户登录
     * @param req 账户信息
     * @param lang 语言环境
     * @return
     */
    LoginUserResp login(LoginDto req, String lang);

	/**
     * 账户登出
     */
    void logout();

    /**
     * 删除账户
     */
    void delete();

    /**
     * 通过email删除用户
     * @param email
     */
    void deleteByEmail(String email);

    /**
     * 上传用户头像信息
     * @param req
     * @return
     */
    String uploadPhoto(PhotoDto req);

    /**
     * 获取登录用户信息
     * @return
     */
    UserVo info();

    /**
     * 重置密码
     * @param req
     */
    void resetPassword(ResetPwdDto req);

    /**
     * 修改密码确认密码
     * @param req
     * @return
     */
    Boolean confirmPassword(ConfirmPwdDto req);

    /**
     * 编辑密码
     * @param req
     */
    void editPassword(EditPwdDto req);

    /**
     * 编辑用户信息
     * @param req 编辑Dto
     * @return 用户信息Vo
     */
    UserVo editInfo(SaveDto req);

    /**
     * 分组使用
     * <AUTHOR>
     * @date 11:00 2022/8/30
     * @param phoneInfoDto:
     * @return void
     **/
    void reportPhoneInfo(PhoneInfoDto phoneInfoDto);

    /**
     * 校验邮箱格式及账号是否存在
     * @param email
     */
    void checkEmail(String email);
}
