package com.chervon.iot.app.domain.dto.share;

import lombok.Data;

import java.io.Serializable;

/**
 * app-子用户设备分享信息
 * <AUTHOR>
 * @date 2024/8/1
 */
@Data
public class AppDeviceAcceptDto implements Serializable {
    private static final long serialVersionUID = 1847045803381080168L;
    /**
     * 分享记录ID
     */
    private Long shareId;

    /**
     * 0：待接受，1：已接受，2：已过期
     */
    private Integer status;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * sn码
     */
    private String sn;

    /**
     * 设备图片
     */
    private String deviceIcon;

    /**
     * 商品型号
     */
    private String commodityModel;

    /**
     * 主用户邮箱
     */
    private String masterEmail;

    /**
     * 过期剩余天数
     */
    private Integer expiredDays;

}
