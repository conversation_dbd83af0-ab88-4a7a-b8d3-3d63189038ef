package com.chervon.iot.app.domain.vo.device;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-08-31 11:22
 **/
@Data
public class DeviceInfoVo implements Serializable {
    /**
     * 购买地点
     */
    @ApiModelProperty("购买地点")
    private String purchasePlace;
    /**
     * 用途
     */
    @ApiModelProperty("用途")
    private String applyWith;
    /**
     * 购买时间 MM-DD-YYYY
     */
    @ApiModelProperty("购买时间 MM-DD-YYYY")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime purchaseTime;

    @ApiModelProperty("收据文件地址列表")
    private List<String> receiptFileUrls;
}
