package com.chervon.iot.app.domain.enums;

/**
 * 静态多语言枚举类
 *
 * <AUTHOR>
 * @date 2024/11/28
 * @description
 */
public enum StaticMultiLanguageEnum {

    /**
     * 设备分享相关
     */
    DEVICE_SHARE_ENUM_MSG_CONTENT("device_share_msg_content", "设备分享消息-内容"),
    DEVICE_SHARE_ENUM_MSG_TITLE("device_share_msg_title", "设备分享消息-标题"),
    DEVICE_SHARE_UPGRADE_MSG_CONTENT("device_share_upgrade_msg_content", "设备分享消息-升级APP内容"),
    DEVICE_SHARE_UPGRADE_MSG_TITLE("device_share_upgrade_msg_title", "设备分享消息-升级APP标题"),
    ;


    private String code;

    private String label;

    StaticMultiLanguageEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getLabel() {
        return label;
    }

    public String getCode() {
        return code;
    }
}
