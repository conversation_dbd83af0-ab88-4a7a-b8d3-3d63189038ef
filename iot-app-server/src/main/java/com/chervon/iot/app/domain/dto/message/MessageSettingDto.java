package com.chervon.iot.app.domain.dto.message;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022-08-29
 */
@Data
public class MessageSettingDto implements Serializable {

    @ApiModelProperty("系统消息开关是否开启：1开启，0不开启")
    private Integer systemMessageSwitch;

    @ApiModelProperty("设备消息开关是否开启：1开启，0不开启")
    private Integer deviceMessageSwitch;

    @ApiModelProperty("营销消息开关是否开启：1开启，0不开启")
    private Integer marketingMessageSwitch;
}
