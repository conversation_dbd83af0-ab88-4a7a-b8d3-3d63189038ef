package com.chervon.iot.app.domain.dataobject;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.common.mybatis.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022-07-18
 */
@Getter
@Setter
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "app_user_device", description = "用户设备关联表")
public class AppUserDevice extends BaseDo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 设备Id（不是设备表的ID主键）
     */
    @ApiModelProperty("设备Id")
    private String deviceId;

    /**
     * 用户Id
     */
    @ApiModelProperty("用户Id")
    private Long userId;

    /**
     * MAC地址
     */
    @ApiModelProperty("MAC地址")
    private String mac;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sort;

    /**
     * 是否注册:0没有注册 1已经注册
     */
    @ApiModelProperty("是否注册:0没有注册 1已经注册")
    private Integer status;

    /**
     * 用途,1:Residential, 2:industrial/Professional/Commercial
     */
    @ApiModelProperty("用途,1:Residential, 2:industrial/Professional/Commercial")
    private String applyWith;

    /**
     * 购买地点, 1:Lower''s, 2:Ace Hardware, 3:Amazon LLC, 4:Home Depot, 5:other
     */
    @ApiModelProperty("购买地点, 1:Lower''s, 2:Ace Hardware, 3:Amazon LLC, 4:Home Depot, 5:other")
    private String purchasePlace;

    /**
     * 购买时间
     */
    @ApiModelProperty("购买时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime purchaseTime;

    /**
     * 收据信息,1:I have My Receipt,2:I Lost My Receipt, 3:Product Was a Gift
     */
    @ApiModelProperty("收据信息,1:I have My Receipt,2:I Lost My Receipt, 3:Product Was a Gift")
    private String receiptInformation;

    /**
     * 收据文件的key
     */
    @ApiModelProperty("收据文件的key")
    private String receiptFileKey;
    @ApiModelProperty("设备昵称")
    private String deviceNickName;

    @ApiModelProperty("数据来源, 0:来自APP, 1表:来自CRM")
    private Integer sourceCode;

    /**
     * 是否可被分享，0:老设备不可分享，1:主用户可分享，2:子用户不可再分享
     */
    private Integer shareType;

}
