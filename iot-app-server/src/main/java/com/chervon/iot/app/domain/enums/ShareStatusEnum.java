package com.chervon.iot.app.domain.enums;

/**
 * 设备分享状态枚举
 * <AUTHOR>
 * @date 2024/8/1
 */
public enum ShareStatusEnum {
    /**
     * 设备分享状态，0:待接受，1:已接受，2:已过期
     */
    PENDING(0, "待接受"),
    ACCEPTED(1, "已接受"),
    EXPIRED(2, "已过期"),
    DELETE_EXPIRED(3, "子用户删除后，主用户显示过期");

    private int value;

    private String label;

    ShareStatusEnum(int value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getLabel() {
        return label;
    }

    public int getValue() {
        return value;
    }

}
