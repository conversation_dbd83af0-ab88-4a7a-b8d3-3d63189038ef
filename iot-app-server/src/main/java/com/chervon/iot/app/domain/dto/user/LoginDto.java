package com.chervon.iot.app.domain.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 20220425
 */
@Data
@ApiModel(description = "用户登录对象")
public class LoginDto implements Serializable {

    private Long userId;

    @NotEmpty
    private String email;

    private String userType;

    private String loginDevice;

    @NotEmpty
    private String password;

    private String loginType;

    @ApiModelProperty(value = "是否同意协议 true 同意  false 不同意", required = true)
    private Boolean check;
}
