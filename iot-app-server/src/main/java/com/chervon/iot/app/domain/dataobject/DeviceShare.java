package com.chervon.iot.app.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 设备分享记录
 * <AUTHOR>
 * @date 2024/8/1
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("device_share")
public class DeviceShare extends BaseDo {
    private static final long serialVersionUID = 5409492660508097896L;
    /**
     * 设备Id（不是设备表的ID主键）
     */
    private String deviceId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 主用户id
     */
    private Long masterId;

    /**
     * 主用户邮箱
     */
    private String masterEmail;

    /**
     * 子用户id
     */
    private Long subId;

    /**
     * 子用户邮箱
     */
    private String subEmail;

    /**
     * 到期时间
     */
    private LocalDateTime expiredDate;

    /**
     * 分享状态，0-待接受、1-已接受、2-已过期
     */
    private Integer status;

}
