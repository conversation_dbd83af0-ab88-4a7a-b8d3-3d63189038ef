package com.chervon.iot.app.domain.dto.agreement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/6 13:51
 */
@Data
@ApiModel(description = "判断用户是否同意最新版本协议")
public class AppCheckAgreeDto implements Serializable {

    @ApiModelProperty(value = "邮箱", required = true)
    private String email;
}
