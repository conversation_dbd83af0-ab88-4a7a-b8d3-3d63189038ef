package com.chervon.iot.app.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chervon.iot.app.domain.dataobject.AppUserAgreement;
import com.chervon.iot.app.domain.vo.agreement.AppAgreedAgreementVo;
import com.chervon.iot.app.service.AppAgreementService;
import com.chervon.iot.app.service.AppUserAgreementService;
import com.chervon.operation.api.RemoteAppAgreementService;
import com.chervon.operation.api.vo.AppAgreementConfigVo;
import com.chervon.operation.api.vo.AppAgreementVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/9/2 16:55
 */
@Service
@Slf4j
public class AppAgreementServiceImpl implements AppAgreementService {

    @Autowired
    private AppUserAgreementService appUserAgreementService;

    @DubboReference
    private RemoteAppAgreementService remoteAppAgreementService;

    @Override
    public AppAgreedAgreementVo agreed(String type) {
        List<AppUserAgreement> ones = appUserAgreementService.list(new LambdaQueryWrapper<AppUserAgreement>()
                .eq(AppUserAgreement::getUserId, StpUtil.getLoginIdAsLong())
                .eq(AppUserAgreement::getStatus, 1)
                .orderByDesc(AppUserAgreement::getAgreeTime));
        if (CollectionUtils.isEmpty(ones)) {
            return null;
        }
        AppAgreedAgreementVo res = null;
        for (AppUserAgreement one : ones) {
            Long agreementId = null;
            String version = null;
            if (StringUtils.equals("user", type)) {
                agreementId = one.getUserAgreementId();
                version = one.getUserAgreementVersion();
            } else if (StringUtils.equals("secret", type)) {
                agreementId = one.getSecretAgreementId();
                version = one.getSecretAgreementVersion();
            }
            if (agreementId == null || StringUtils.isBlank(version)) {
                return null;
            }
            AppAgreementVo vo = remoteAppAgreementService.getOneByVersionAndType(type, agreementId, version, LocaleContextHolder.getLocale().getLanguage());
            if (vo == null) {
                continue;
            }
            res = new AppAgreedAgreementVo();
            BeanUtils.copyProperties(vo, res);
            res.setAgreeTime(one.getAgreeTime());
            break;
        }
        return res;
    }

    @Override
    public void withdraw() {
        withdraw(StpUtil.getLoginIdAsLong());
    }

    @Override
    public void withdraw(Long userId) {
        appUserAgreementService.update(new AppUserAgreement(), new LambdaUpdateWrapper<AppUserAgreement>()
                .set(AppUserAgreement::getStatus, 2)
                .set(AppUserAgreement::getWithdrawTime, LocalDateTime.now())
                .eq(AppUserAgreement::getUserId, userId));
    }
    @Override
    public AppAgreementVo latest(String type) {
        return remoteAppAgreementService.latest(1, type);
    }

    @Override
    public boolean checkAgree(String email) {
        if (StringUtils.isBlank(email)) {
            return false;
        }
        AppAgreementVo user = this.latest("user");
        AppAgreementVo secret = this.latest("secret");
        AppAgreementConfigVo config = remoteAppAgreementService.getConfig();
        LambdaQueryWrapper<AppUserAgreement> wrapper = new LambdaQueryWrapper<AppUserAgreement>()
                .eq(AppUserAgreement::getUserAgreementId, config.getEgoUserAgreementId())
                .eq(AppUserAgreement::getSecretAgreementId, config.getEgoSecretAgreementId())
                .eq(AppUserAgreement::getUserEmail, email)
                .eq(AppUserAgreement::getStatus, 1)
                .eq(AppUserAgreement::getUserAgreementVersion, Optional.ofNullable(user).orElse(new AppAgreementVo()).getVersion())
                .eq(AppUserAgreement::getSecretAgreementVersion, Optional.ofNullable(secret).orElse(new AppAgreementVo()).getVersion())
                .last("limit 1");
        AppUserAgreement one = appUserAgreementService.getOne(wrapper);
        return one != null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void agreeLatest(Long userId, String userEmail) {
        AppAgreementVo user = this.latest("user");
        AppAgreementVo secret = this.latest("secret");
        AppAgreementConfigVo config = remoteAppAgreementService.getConfig();
        Long userAgreementId = Optional.ofNullable(config).orElse(new AppAgreementConfigVo()).getEgoUserAgreementId();
        String userVersion = Optional.ofNullable(user).orElse(new AppAgreementVo()).getVersion();
        Long secretAgreementId = Optional.ofNullable(config).orElse(new AppAgreementConfigVo()).getEgoSecretAgreementId();
        String secretVersion = Optional.ofNullable(secret).orElse(new AppAgreementVo()).getVersion();
        // 根据已有条件查询是否有授权记录
        List<AppUserAgreement> list = appUserAgreementService.list(new LambdaQueryWrapper<AppUserAgreement>()
                .eq(AppUserAgreement::getUserId, userId)
                .eq(AppUserAgreement::getUserEmail, userEmail)
                .eq(AppUserAgreement::getStatus, 1)
                .eq(AppUserAgreement::getUserAgreementId, userAgreementId)
                .eq(AppUserAgreement::getUserAgreementVersion, userVersion)
                .eq(AppUserAgreement::getSecretAgreementId, secretAgreementId)
                .eq(AppUserAgreement::getSecretAgreementVersion, secretVersion));
        if (CollectionUtils.isEmpty(list)) {
            AppUserAgreement one = new AppUserAgreement();
            one.setUserId(userId);
            one.setUserEmail(userEmail);
            one.setAgreeTime(LocalDateTime.now());
            one.setStatus(1);
            one.setUserAgreementId(userAgreementId);
            one.setUserAgreementVersion(userVersion);
            one.setSecretAgreementId(secretAgreementId);
            one.setSecretAgreementVersion(secretVersion);
            if (StringUtils.isBlank(one.getUserAgreementVersion())) {
                log.warn("App user agreement version not found, user email：{}", userEmail);
                return;
            }
            if (StringUtils.isBlank(one.getSecretAgreementVersion())) {
                log.warn("App user agreement version not found, user email：{}", userEmail);
                return;
            }
            appUserAgreementService.save(one);
        }
    }
}
