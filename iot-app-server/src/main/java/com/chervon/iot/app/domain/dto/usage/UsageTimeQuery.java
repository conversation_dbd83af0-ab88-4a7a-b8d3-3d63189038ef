package com.chervon.iot.app.domain.dto.usage;

import com.chervon.iot.middle.api.dto.query.TimeQueryDto;
import lombok.Data;
import java.time.LocalDate;
import java.util.List;

/**
 * 查询使用时间的DTO
 * <AUTHOR> 2024/2/29
 */
@Data
public class UsageTimeQuery {
    /**
     * 设备Id
     */
    private String deviceId;
    /**
     * 查询的时间开始节点
     */
    private Long timeStart;
    /**
     * 查询时间范围截止
     */
    private Long timeEnd;
    /**
     * 请求的日期类型
     */
    private Integer queryDateType;

    /**
     * 按周和月查询的时间跨度列表
     */
    private List<TimeQueryDto> timeQueryList;
    /**
     * 按日期的日期连续列表
     */
    private List<String> dayBetweenList;

}
