package com.chervon.iot.app.controller;

import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.iot.app.service.AppPostSaleService;
import com.chervon.operation.api.vo.ProductWikiVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/11/15 17:00
 */
@Api(tags = "售后相关")
@RestController
@RequestMapping("/postSale")
public class PostSaleController {

    @Autowired
    private AppPostSaleService appPostSaleService;

    /*******************产品百科********************/

    @ApiOperation("产品百科------参数值取产品id")
    @PostMapping("productWiki")
    public ProductWikiVo productWiki(@RequestBody SingleInfoReq<Long> req) {
        return appPostSaleService.productWiki(req.getReq());
    }

}
