package com.chervon.iot.app.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.redis.constant.RedisConstant;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.iot.app.domain.dto.device.DeviceShortInfo;
import com.chervon.iot.app.domain.dto.message.DeviceMessageDto;
import com.chervon.iot.app.domain.dto.message.SearchMessageDto;
import com.chervon.iot.app.domain.dto.message.UserMessageDetailDto;
import com.chervon.iot.middle.api.service.RemoteIotDataService;
import com.chervon.message.api.RemoteMessageService;
import com.chervon.message.api.dto.DeviceInfoDto;
import com.chervon.message.api.dto.LastMessageDto;
import com.chervon.message.api.dto.MessageDetailDto;
import com.chervon.message.api.dto.SearchMessageInfoDto;
import com.chervon.message.api.enums.MessageTypeEnum;
import com.chervon.message.api.vo.LastMessageListVo;
import com.chervon.message.api.vo.MessageVo;
import com.chervon.technology.api.RemoteProductService;
import com.chervon.usercenter.api.vo.UserVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 消息相关
 * <AUTHOR>
 * @date 2022-07-22
 */
@Api(tags = "消息相关")
@RestController
@Slf4j
@RequestMapping("/message")
public class MessageController {

    @DubboReference(timeout = 120000, retries = 0)
    private RemoteMessageService messageService;

    @DubboReference(timeout = 120000, retries = 0)
    private RemoteIotDataService remoteIotDataService;

    @DubboReference(timeout = 120000, retries = 0)
    private RemoteProductService remoteProductService;

    @ApiOperation("检查用户指定设备是否有未读消息")
    @RequestMapping(value = "/device/unread/check", method = RequestMethod.POST)
    public Boolean checkDeviceUnreadMessage(@Validated @RequestBody DeviceMessageDto deviceMessageDto) {
        // 获取用户
        UserVo userVo = RedisUtils.getCacheObject(StpUtil.getTokenValue());
        if(Objects.isNull(userVo) || Objects.isNull(userVo.getId())){
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED,"userId");
        }
        String deviceId=deviceMessageDto.getDeviceId();
        //组装参数
        List<DeviceInfoDto> deviceInfos = new ArrayList<>(1);
        DeviceInfoDto deviceInfoDto=new DeviceInfoDto();
        deviceInfoDto.setDeviceId(deviceId);
        deviceInfos.add(deviceInfoDto);

        LastMessageDto lastMessage = new LastMessageDto();
        lastMessage.setUserId(userVo.getId());
        lastMessage.setDeviceInfoList(deviceInfos);
        return messageService.checkDeviceUnreadMessage(lastMessage);
    }

    @ApiOperation("app用户获取最后收到的消息")
    @RequestMapping(value = "/get/last/message", method = RequestMethod.POST)
    public LastMessageListVo getLastMessage() {
        // 获取用户绑定的设备信息，里面需要包含产品Id
        UserVo userVo = RedisUtils.getCacheObject(StpUtil.getTokenValue());
        if(Objects.isNull(userVo) || Objects.isNull(userVo.getId())){
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED,"userId");
        }
        LastMessageDto lastMessage = new LastMessageDto();
        lastMessage.setUserId(userVo.getId());
        List<DeviceInfoDto> devices = getUserBindDeviceList(userVo.getId());
        if(!CollectionUtils.isEmpty(devices)) {
            lastMessage.setDeviceInfoList(devices);
        }
        return messageService.getLastMessage(lastMessage);
    }

    @NotNull
    private static List<DeviceInfoDto> getUserBindDeviceList(Long userId) {
        // 从缓从中获取绑定的设备
        List<DeviceShortInfo> devices= RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId);
        if (CollectionUtils.isEmpty(devices)) {
            return Collections.emptyList();
        } else {
            return devices.stream().map(deviceVo -> {
                        DeviceInfoDto deviceInfoDto = new DeviceInfoDto();
                        deviceInfoDto.setDeviceId(deviceVo.getDeviceId());
                        return deviceInfoDto;
                    }).collect(Collectors.toList());
        }
    }

    @ApiOperation("app用户获取消息列表,相同消息不同推送方式去重")
    @RequestMapping(value = "/get/message/list", method = RequestMethod.POST)
    public PageResult<MessageVo> getMessageList(@Validated @RequestBody SearchMessageDto searchMessage) {
        UserVo userVo = RedisUtils.getCacheObject(StpUtil.getTokenValue());
        SearchMessageInfoDto searchMessageInfo = ConvertUtil.convert(searchMessage, SearchMessageInfoDto.class);
        searchMessageInfo.setUserId(userVo.getId());
        if(Objects.isNull(searchMessage.getPageNum())){
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED,"pageNum");
        }
        searchMessageInfo.setPageSize(10);

        return messageService.getMessageList(searchMessageInfo);
    }

    @ApiOperation("app查看消息详情，同时设置消息为以已读")
    @RequestMapping(value = "/get/message/detail", method = RequestMethod.POST)
    public MessageVo getMessageDetail(@RequestBody UserMessageDetailDto messageDetail) {
        if(Objects.isNull(messageDetail)){
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED, "messageDetail");
        }
        MessageDetailDto detail = ConvertUtil.convert(messageDetail, MessageDetailDto.class);
        Long userId = StpUtil.getLoginIdAsLong();
        if(Objects.isNull(userId)){
            throw new ServiceException(ErrorCode.PARAMETER_ERROR,"userId");
        }
        //app端未传消息类型做兼容，默认系统消息
        if(Objects.isNull(messageDetail.getMessageType())){
            detail.setMessageType(MessageTypeEnum.SYS_MSG.getValue());
        }
        detail.setUserId(userId);
        return messageService.getMessageDetail(detail);
    }

    @ApiOperation("删除消息")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public void deleteMessage(@RequestBody UserMessageDetailDto messageDetail) {
        UserVo userVo = RedisUtils.getCacheObject(StpUtil.getTokenValue());
        MessageDetailDto detail = ConvertUtil.convert(messageDetail, MessageDetailDto.class);
        detail.setUserId(userVo.getId());
        messageService.deleteMessage(detail);
    }
}
