package com.chervon.iot.app.service;

import com.chervon.common.core.domain.PageResult;
import com.chervon.iot.app.domain.dto.explore.*;
import com.chervon.iot.app.domain.vo.explore.HaveFunReq;

/**
 * Explore服务接口
 *
 * <AUTHOR>
 */
public interface ExploreService {

    /**
     * 获取Explore首页数据
     *
     * @param userId 用户ID
     * @return Explore首页数据
     */
    ExploreBannerDto getExploreHomeData(Long userId);


    /**
     * 获取Have Fun内容列表
     *
     * @return Have Fun内容列表
     */
    PageResult<HaveFunDto> getHaveFunContents(HaveFunReq req);
}
