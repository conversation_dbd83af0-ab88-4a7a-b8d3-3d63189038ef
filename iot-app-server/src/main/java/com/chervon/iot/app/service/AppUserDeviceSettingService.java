package com.chervon.iot.app.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.iot.app.domain.dataobject.AppUserDeviceSetting;
import com.chervon.iot.app.domain.dto.device.AppUserDeviceSettingDto;
import com.chervon.iot.app.domain.vo.user.AppUserDeviceSettingVo;

/**
 * 用户设备设置表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-02 18:26:31
 * @description 
 */
public interface AppUserDeviceSettingService  extends IService<AppUserDeviceSetting> {

    AppUserDeviceSettingVo getUserDeviceSetting(AppUserDeviceSettingDto queryDto);

    boolean updateUserDeviceSetting(AppUserDeviceSettingDto updateDto);
}
