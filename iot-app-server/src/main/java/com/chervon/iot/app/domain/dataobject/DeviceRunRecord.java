package com.chervon.iot.app.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date Created in 2022/10/27 15:09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("device_run_record")
public class DeviceRunRecord extends BaseDo {

	@ApiModelProperty("设备id")
	private String deviceId;

	@ApiModelProperty("总距离(单位M)对应功能Id：23")
	private String totalDistance;

	@ApiModelProperty("割草时间(单位秒)对应功能Id：22")
	private String totalMowingTime;

	@ApiModelProperty("平均速度（单位KM/H）")
	private String averageSpeed;

	@ApiModelProperty("最大速度（单位KM/H）")
	private String maxSpeed;


	@ApiModelProperty("割草面积（单位平方米）对应功能Id：25")
	private String totalMowingArea;

	@ApiModelProperty("平均割草面积（单位平方米）")
	private String averageMowingArea;

	@ApiModelProperty("二氧化氮减排量（单位g）")
	private String totalCo2EmissionReduced;

	@ApiModelProperty("开始时间")
	private Long startTime;

	@ApiModelProperty("结束时间")
	private Long endTime;

}
