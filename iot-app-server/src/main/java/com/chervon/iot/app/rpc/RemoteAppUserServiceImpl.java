package com.chervon.iot.app.rpc;

import com.chervon.iot.app.api.RemoteAppUserService;
import com.chervon.iot.app.service.AppUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024-07-05 15:35
 **/
@Slf4j
@DubboService
public class RemoteAppUserServiceImpl implements RemoteAppUserService {
    @Resource
    private AppUserService appUserService;
    /**
     * 通过邮箱删除用户
     * @param email 用户邮箱
     */
    @Override
    public void deleteUserByEmail(String email){
        appUserService.deleteByEmail(email);
    }

}
