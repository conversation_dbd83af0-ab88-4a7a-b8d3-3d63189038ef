package com.chervon.iot.app.domain.vo.device;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class PartsInfoVo implements Serializable {

    private static final long serialVersionUID = 8730290157589651508L;

    @ApiModelProperty("配件Id")
    private Long partId;

    @ApiModelProperty("配件型号")
    private String partModel;

    @ApiModelProperty("配件名称")
    private String partName;

    @ApiModelProperty("配件图片")
    private String imageUrl;

    @ApiModelProperty("购买地址")
    private String shopUrl;


}
