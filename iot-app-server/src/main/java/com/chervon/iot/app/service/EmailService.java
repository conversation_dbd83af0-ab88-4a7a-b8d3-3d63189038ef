package com.chervon.iot.app.service;

import com.chervon.iot.app.domain.dto.user.EmailCodeDto;

/**
 * <AUTHOR>
 * @date 2022/6/26 12:57
 */
public interface EmailService {

    /**
     * 发送注册账户邮箱验证码
     * @param req 参数
     * @return 返回结果
     */
    String sendRegisterEmailCode(EmailCodeDto req);

    /**
     * 校验注册账户邮箱验证码
     * @param req 参数
     */
    void validateRegisterEmailCode(EmailCodeDto req);

    /**
     * 发送忘记密码邮箱验证码
     * @param req 邮箱信息
     * @return 结果
     */
    String sendForgotPwdEmailCode(EmailCodeDto req);

    /**
     * 校验忘记密码验证码
     * @param req
     */
    void validateForgotPwdEmailCode(EmailCodeDto req);

    /**
     * 发送其他平台用户初次登陆邮箱验证码
     * @param req 参数
     * @return 返回结果
     */
    String sendVerifyEmailCode(EmailCodeDto req);

    /**
     * 校验其他平台用户初次登陆邮箱验证码
     * @param req 参数
     */
    void validateVerifyEmailCode(EmailCodeDto req);
}
