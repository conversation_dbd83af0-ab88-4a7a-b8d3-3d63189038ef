package com.chervon.iot.app.domain.dto.explore;

import com.chervon.common.mybatis.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Have Fun内容DTO
 *
 * <AUTHOR>
 */
@Data
public class HaveFunDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 内容编号
     */
    @ApiModelProperty("编号")
    private String contentNo;

    /**
     * 头像图片链接
     */
    @ApiModelProperty("头像图片链接")
    private String avatarUrl;

    /**
     * 内容title
     */
    @ApiModelProperty("Title")
    private String title;

    /**
     * 文本内容
     */
    @ApiModelProperty("内容文案")
    private String text;

    /**
     * 文本中的链接
     */
    @ApiModelProperty("文案中的链接")
    private String linkInText;

    /**
     * 内容类型(Image/Video)
     */
    @ApiModelProperty("内容类型 1 image、2 video")
    private String type;

    /**
     * 图片/视频内容链接
     */
    @ApiModelProperty("图片/视频内容链接")
    private String ivUrl;

    /**
     * CreateTime
     */
    @ApiModelProperty("创建时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;


    /**
     * UpdateTime
     */
    @ApiModelProperty("更新时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;
}
