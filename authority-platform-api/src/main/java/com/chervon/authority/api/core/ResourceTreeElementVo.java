package com.chervon.authority.api.core;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-05-05 10:23
 **/
@Data
public class ResourceTreeElementVo implements Serializable {
    /**
     * 路由地址
     */
    private String path;
    /**
     * 组件名称
     */
    private String name;
    /**
     * 排序
     */
    private Integer orderNo;
    /**
     * 资源类型 S系统 M菜单 P页面 B按钮
     */
    private String type;
    /**
     * 元素Id
     */
    private String elementId;
    /**
     * 子组件
     */
    private List<ResourceTreeElementVo> children;

    public ResourceTreeElementVo(String name, String path, String type, Integer orderNo) {
        this.name = name;
        this.path = path;
        this.type = type;
        this.orderNo = orderNo;
    }

    public ResourceTreeElementVo() {

    }

    @JsonIgnore
    private Long nodeId;

    @JsonIgnore
    private Long parentNodeId;
}
